# Codebase-Dev 智能代码搜索系统

## 📖 项目概述

Codebase-Dev 是一个基于 AI 的智能代码搜索和分析系统，旨在帮助开发者快速理解和搜索大型代码库。系统采用前后端分离架构，结合了传统的 grep 搜索和基于 LLM 的深度搜索技术，为用户提供精准、智能的代码搜索体验。

### 🎯 核心特性

- **🔍 智能搜索**: 支持传统 grep 搜索和基于 LLM 的深度搜索
- **📁 工作区管理**: 支持多个代码仓库的工作区切换
- **🌳 文件树浏览**: 直观的文件目录结构展示
- **💻 代码查看**: 支持语法高亮的代码查看器
- **⚡ 实时搜索**: 流式搜索结果展示，实时反馈搜索进度
- **🎨 现代化UI**: 基于 React + TypeScript + Tailwind CSS 的现代化界面

## 🏗️ 系统架构

### 技术栈

#### 前端
- **框架**: React 19 + TypeScript
- **状态管理**: Zustand
- **样式**: Tailwind CSS
- **构建工具**: Vite
- **代码高亮**: react-syntax-highlighter
- **Markdown渲染**: react-markdown

#### 后端
- **框架**: FastAPI + Python 3.12
- **配置管理**: Pydantic + YAML
- **LLM集成**: OpenAI API 兼容接口
- **搜索引擎**: Grep + 自定义深度搜索
- **异步处理**: asyncio + 流式响应

### 目录结构

```
Codebase-Dev/
├── frontend/                 # 前端项目
│   ├── src/
│   │   ├── components/       # 通用组件
│   │   │   ├── layout/       # 布局组件
│   │   │   └── ui/           # UI基础组件
│   │   ├── features/         # 功能模块
│   │   │   ├── file-viewer/  # 文件查看器
│   │   │   ├── search/       # 搜索功能
│   │   │   └── workspace/    # 工作区管理
│   │   ├── services/         # API服务
│   │   ├── store/            # 状态管理
│   │   ├── types/            # 类型定义
│   │   └── utils/            # 工具函数
│   └── package.json
├── backend/                  # 后端项目
│   └── python/
│       ├── core/             # 核心配置
│       ├── server/           # API服务器
│       │   └── api/v1/       # API路由
│       ├── modules/          # 功能模块
│       │   ├── deepsearch/   # 深度搜索
│       │   ├── integration/  # 集成工具
│       │   └── llm/          # LLM客户端
│       ├── utils/            # 工具函数
│       ├── config/           # 配置文件
│       └── data/repos/       # 代码仓库数据
├── tests/                    # 测试文件
└── deploy/                   # 部署配置
```

## 🔧 核心模块详解

### 前端模块

#### 1. 工作区管理 (Workspace)
- **WorkspaceSelector**: 工作区选择器，支持多仓库切换
- **FileTree**: 文件树组件，展示目录结构
- **useWorkspace**: 工作区状态管理 Hook

#### 2. 文件查看器 (File Viewer)
- **FileViewer**: 主文件查看组件
- **CodeViewer**: 代码语法高亮显示
- **useFileContent**: 文件内容加载 Hook

#### 3. 搜索功能 (Search)
- **SearchPanel**: 搜索面板，支持查询输入和结果展示
- **useSearch**: 搜索逻辑处理 Hook
- 支持实时流式搜索结果展示

#### 4. 状态管理 (Store)
- **appStore**: 全局应用状态管理
- 管理工作区、文件、搜索等状态

### 后端模块

#### 1. API 服务层 (Server)
- **FastAPI 应用**: 提供 RESTful API 接口
- **路由管理**: 版本化的 API 路由 (v1)
- **CORS 配置**: 跨域请求支持
- **流式响应**: 支持 Server-Sent Events

#### 2. 深度搜索引擎 (DeepSearch)
- **DeepSearch**: 核心搜索引擎类
- **多轮搜索**: 基于 LLM 的智能查询扩展
- **并行处理**: 多线程并发搜索和过滤
- **结果合并**: 文件级别的搜索结果合并

#### 3. 搜索工具集成 (Integration Tools)
- **GrepSearchTool**: 基于 grep 的传统搜索
- **SearchToolABC**: 搜索工具抽象基类
- 支持扩展其他搜索引擎

#### 4. LLM 集成 (LLM Client)
- **LLMClient**: 大语言模型客户端
- **提示词管理**: 结构化的提示词模板
- **智能过滤**: 基于 LLM 的搜索结果相关性判断

#### 5. 配置管理 (Config)
- **Pydantic 模型**: 类型安全的配置验证
- **YAML 配置**: 人性化的配置文件格式
- **环境隔离**: 支持多环境配置

## 📊 主要功能调用流程图

### 1. 系统初始化流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端应用
    participant B as 后端API
    participant C as 配置系统
    participant FS as 文件系统

    U->>F: 访问应用
    F->>F: 初始化 Zustand Store
    F->>B: GET /api/v1/workspaces
    B->>C: 加载配置文件
    C->>B: 返回配置信息
    B->>FS: 扫描 repos 目录
    FS->>B: 返回工作区列表
    B->>F: 返回工作区数据
    F->>F: 自动选择第一个工作区
    F->>U: 显示工作区界面
```

### 2. 工作区切换流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant WS as WorkspaceSelector
    participant Store as AppStore
    participant FT as FileTree
    participant B as 后端API

    U->>WS: 选择工作区
    WS->>Store: setCurrentWorkspace(id)
    Store->>Store: 清空文件状态
    Store->>FT: 触发重新渲染
    FT->>B: GET /api/v1/workspaces/{id}/files
    B->>B: 构建文件树
    B->>FT: 返回文件树数据
    FT->>U: 显示新工作区文件树
```

### 3. 文件查看流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant FT as FileTree
    participant FV as FileViewer
    participant B as 后端API
    participant CV as CodeViewer

    U->>FT: 点击文件
    FT->>FV: setSelectedFile(path)
    FV->>B: GET /api/v1/files?file_path={path}
    B->>B: 读取文件内容
    B->>FV: 返回文件内容
    FV->>CV: 传递内容和语言类型
    CV->>CV: 语法高亮处理
    CV->>U: 显示高亮代码
```

### 4. 深度搜索流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant SP as SearchPanel
    participant B as 后端API
    participant DS as DeepSearch
    participant LLM as LLM客户端
    participant GT as GrepTool

    U->>SP: 输入搜索查询
    SP->>B: POST /api/v1/search (流式请求)
    B->>DS: 创建 DeepSearch 实例

    loop 多轮搜索 (最多3轮)
        DS->>LLM: 生成新查询词
        LLM->>DS: 返回查询词列表

        par 并行搜索
            DS->>GT: 执行 grep 搜索
            GT->>DS: 返回代码片段
        end

        par 并行过滤
            DS->>LLM: 判断片段相关性
            LLM->>DS: 返回过滤结果
        end

        DS->>B: 流式返回搜索进度
        B->>SP: Server-Sent Events
        SP->>U: 实时显示搜索状态
    end

    DS->>DS: 合并文件级结果
    DS->>B: 返回最终结果
    B->>SP: 完成搜索
    SP->>U: 显示搜索结果
```

## 🚀 快速开始

### 环境要求

- **Node.js**: >= 18.0.0
- **Python**: >= 3.12
- **系统**: macOS/Linux (支持 grep 命令)

### 安装步骤

#### 1. 克隆项目

```bash
git clone <repository-url>
cd Codebase-Dev
```

#### 2. 后端设置

```bash
cd backend/python

# 安装依赖 (推荐使用 uv)
uv sync

# 或使用 pip
pip install -e .

# 配置环境
cp config/config.yaml config/config.dev.yaml
# 编辑 config.dev.yaml 配置 LLM API 信息
```

#### 3. 前端设置

```bash
cd frontend

# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

#### 4. 启动后端服务

```bash
cd backend/python

# 启动开发服务器
python main.py --env dev
```

### 配置说明

#### 后端配置 (config/config.yaml)

```yaml
# API 服务配置
api:
  host: "0.0.0.0"
  port: 3451
  reload: true
  version: 1

# 数据配置
data:
  repos_path: ./data/repos  # 代码仓库存放路径

# LLM 配置
llm:
  base_url: "your-llm-api-url"
  api_key: "your-api-key"
  model: "your-model-name"
  temperature: 0.7
  max_tokens: 8192

# 深度搜索配置
deepsearch:
  max_iterations: 3        # 最大搜索轮数
  max_sub_queries: 3       # 最大子查询数
  max_new_queries: 2       # 每轮新查询数

# 文件过滤配置
file_filter:
  include: [".py", ".js", ".ts", ".java", ".cpp", ".go", ".rs"]
  exclude: [".git", "node_modules", "__pycache__"]
  max_file_size: 1048576   # 1MB
```

## 📁 数据准备

### 添加代码仓库

1. 将要搜索的代码仓库放入 `backend/python/data/repos/` 目录
2. 每个子目录代表一个工作区
3. 系统会自动扫描并在前端显示可用的工作区

示例目录结构：
```
backend/python/data/repos/
├── my-project-1/
│   ├── src/
│   └── README.md
├── my-project-2/
│   ├── app/
│   └── package.json
└── open-source-lib/
    ├── lib/
    └── docs/
```

## 📚 API 文档

### 核心 API 接口

#### 1. 获取工作区列表

```http
GET /api/v1/workspaces
```

**响应示例:**
```json
[
  {
    "id": "my-project-1",
    "name": "My Project 1",
    "path": "/path/to/repos/my-project-1",
    "description": "项目描述"
  }
]
```

#### 2. 获取工作区文件树

```http
GET /api/v1/workspaces/{workspace_name}/files
```

**响应示例:**
```json
[
  {
    "name": "src",
    "path": "my-project-1/src",
    "type": "directory",
    "children": [
      {
        "name": "main.py",
        "path": "my-project-1/src/main.py",
        "type": "file",
        "size": 1024
      }
    ]
  }
]
```

#### 3. 获取文件内容

```http
GET /api/v1/files?file_path={file_path}
```

**响应示例:**
```json
{
  "content": "文件内容",
  "language": "python",
  "size": 1024
}
```

#### 4. 执行搜索

```http
POST /api/v1/search
Content-Type: application/json

{
  "query": "搜索关键词",
  "workspaceName": "my-project-1",
  "searchType": "grep",
  "maxResults": 50
}
```

**流式响应 (Server-Sent Events):**
```
data: {"type": "start", "message": "开始搜索", "timestamp": 1234567890}

data: {"type": "process", "message": "第1次搜索: 找到 5 个相关代码片段", "timestamp": 1234567891}

data: {"type": "complete", "message": "搜索完成: 共找到 15 个代码片段", "timestamp": 1234567892}
```

## 🔍 搜索功能详解

### 搜索类型

#### 1. Grep 搜索
- **特点**: 快速、精确的文本匹配
- **适用场景**: 查找具体的函数名、变量名、关键词
- **实现**: 基于系统 grep 命令，支持正则表达式

#### 2. 深度搜索 (DeepSearch)
- **特点**: 基于 LLM 的智能搜索，支持自然语言查询
- **适用场景**: 复杂的功能查找、概念性搜索
- **核心流程**:
  1. **查询分析**: LLM 分析用户查询意图
  2. **查询扩展**: 生成多个相关的子查询
  3. **并行搜索**: 同时执行多个 grep 搜索
  4. **智能过滤**: LLM 判断搜索结果的相关性
  5. **结果合并**: 按文件合并相关代码片段

### 搜索优化策略

#### 1. 并行处理
- 多线程并发执行搜索任务
- 智能负载均衡，避免系统过载
- 可配置的并发数量控制

#### 2. 结果过滤
- 基于文件类型的智能过滤
- LLM 驱动的相关性判断
- 重复结果去除和合并

#### 3. 性能优化
- 文件大小限制，避免处理超大文件
- 目录排除规则，跳过无关目录
- 缓存机制，提高重复查询效率

## 🛠️ 开发指南

### 前端开发

#### 组件开发规范

```typescript
// 组件接口定义
interface ComponentProps {
  // 必需属性
  data: DataType;
  // 可选属性
  className?: string;
  // 回调函数
  onAction?: (param: ParamType) => void;
}

// 组件实现
export function Component({ data, className, onAction }: ComponentProps) {
  // 使用 Zustand store
  const { state, actions } = useAppStore();

  // 事件处理
  const handleClick = () => {
    onAction?.(data);
  };

  return (
    <div className={cn('base-styles', className)}>
      {/* 组件内容 */}
    </div>
  );
}
```

#### 状态管理

```typescript
// Store 定义
interface AppState {
  // 状态定义
  data: DataType[];
  loading: boolean;

  // 动作定义
  setData: (data: DataType[]) => void;
  setLoading: (loading: boolean) => void;
}

// Store 实现
export const useAppStore = create<AppState>()(
  devtools((set) => ({
    data: [],
    loading: false,

    setData: (data) => set({ data }),
    setLoading: (loading) => set({ loading }),
  }))
);
```

### 后端开发

#### API 路由开发

```python
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel

router = APIRouter(prefix="/api/v1")

class RequestModel(BaseModel):
    field: str

class ResponseModel(BaseModel):
    result: str

@router.post("/endpoint", response_model=ResponseModel)
async def endpoint(request: RequestModel):
    """API 端点描述"""
    try:
        # 业务逻辑处理
        result = process_request(request.field)
        return ResponseModel(result=result)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
```

#### 搜索工具扩展

```python
from modules.common.search_tool import SearchToolABC
from modules.common.schema import CodeSnippet

class CustomSearchTool(SearchToolABC):
    """自定义搜索工具"""

    def __init__(self, repo_path: str):
        self.repo_path = repo_path

    def search(self, query: str) -> List[CodeSnippet]:
        """实现搜索逻辑"""
        # 自定义搜索实现
        return snippets
```

## 🧪 测试

### 运行测试

```bash
# 后端测试
cd backend/python
pytest tests/

# 前端测试
cd frontend
npm test
```

### 测试覆盖

- **配置系统测试**: 验证配置加载和验证逻辑
- **搜索功能测试**: 测试各种搜索场景
- **API 接口测试**: 验证 API 响应和错误处理
- **组件单元测试**: 前端组件功能测试

## 🚀 部署

### 生产环境部署

#### 1. 前端构建

```bash
cd frontend
npm run build
# 构建产物在 dist/ 目录
```

#### 2. 后端部署

```bash
cd backend/python

# 生产环境配置
cp config/config.yaml config/config.prod.yaml
# 编辑生产环境配置

# 启动生产服务
python main.py --env prod
```

#### 3. Docker 部署 (推荐)

```dockerfile
# 后端 Dockerfile
FROM python:3.12-slim

WORKDIR /app
COPY backend/python/ .

RUN pip install -e .

EXPOSE 3451
CMD ["python", "main.py", "--env", "prod"]
```

```dockerfile
# 前端 Dockerfile
FROM node:18-alpine

WORKDIR /app
COPY frontend/ .

RUN npm install && npm run build

FROM nginx:alpine
COPY --from=0 /app/dist /usr/share/nginx/html
EXPOSE 80
```

#### 4. Docker Compose

```yaml
version: '3.8'

services:
  backend:
    build:
      context: .
      dockerfile: deploy/backend.dockerfile
    ports:
      - "3451:3451"
    volumes:
      - ./data:/app/data
    environment:
      - ENV=prod

  frontend:
    build:
      context: .
      dockerfile: deploy/frontend.dockerfile
    ports:
      - "80:80"
    depends_on:
      - backend
```

### 性能优化

#### 1. 后端优化
- **并发配置**: 调整 uvicorn workers 数量
- **内存管理**: 配置合适的文件大小限制
- **缓存策略**: 实现搜索结果缓存

#### 2. 前端优化
- **代码分割**: 使用 React.lazy 进行路由级别的代码分割
- **虚拟滚动**: 大文件列表使用虚拟滚动
- **缓存策略**: 实现文件内容缓存

## 🔧 故障排除

### 常见问题

#### 1. 后端启动失败

**问题**: `ImportError: Cannot import API module`
**解决方案**:
```bash
# 检查 Python 版本
python --version  # 需要 >= 3.12

# 重新安装依赖
pip install -e .

# 检查配置文件
python -c "from core.config import get_config; print(get_config())"
```

#### 2. 搜索功能异常

**问题**: 搜索无结果或报错
**解决方案**:
```bash
# 检查 grep 命令
which grep

# 检查文件权限
ls -la data/repos/

# 查看日志
tail -f logs/app.log
```

#### 3. LLM 调用失败

**问题**: `LLM API call failed`
**解决方案**:
```yaml
# 检查配置文件 config/config.yaml
llm:
  base_url: "正确的API地址"
  api_key: "有效的API密钥"
  model: "支持的模型名称"
```

#### 4. 前端连接失败

**问题**: 前端无法连接后端
**解决方案**:
```typescript
// 检查 API 配置 src/config/apiConfig.ts
export const API_BASE_URL = 'http://localhost:3451';

// 检查 CORS 配置
// 确保后端配置中包含前端地址
```

### 日志分析

#### 后端日志

```bash
# 查看实时日志
tail -f backend/python/logs/app.log

# 搜索错误日志
grep "ERROR" backend/python/logs/app.log

# 搜索特定功能日志
grep "DeepSearch" backend/python/logs/app.log
```

#### 前端调试

```javascript
// 开启详细日志
localStorage.setItem('debug', 'true');

// 查看网络请求
// 打开浏览器开发者工具 -> Network 标签

// 查看状态管理
// 安装 Redux DevTools 扩展
```

## 🤝 贡献指南

### 开发流程

1. **Fork 项目**
2. **创建功能分支**: `git checkout -b feature/new-feature`
3. **提交更改**: `git commit -m 'Add new feature'`
4. **推送分支**: `git push origin feature/new-feature`
5. **创建 Pull Request**

### 代码规范

#### Python 代码规范
- 遵循 PEP 8 标准
- 使用类型注解
- 编写文档字符串
- 单元测试覆盖率 > 80%

#### TypeScript 代码规范
- 使用 ESLint 配置
- 严格的类型检查
- 组件 Props 接口定义
- 遵循 React Hooks 规范

### 提交信息规范

```
type(scope): description

feat(search): add deep search functionality
fix(ui): resolve file tree rendering issue
docs(readme): update installation guide
test(api): add search endpoint tests
```

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- [FastAPI](https://fastapi.tiangolo.com/) - 现代化的 Python Web 框架
- [React](https://reactjs.org/) - 用户界面构建库
- [Tailwind CSS](https://tailwindcss.com/) - 实用优先的 CSS 框架
- [Zustand](https://github.com/pmndrs/zustand) - 轻量级状态管理
- [Pydantic](https://pydantic-docs.helpmanual.io/) - 数据验证库

## 📞 联系方式

如有问题或建议，请通过以下方式联系：

- 创建 [Issue](https://github.com/your-repo/issues)
- 发送邮件至: <EMAIL>
- 项目讨论: [Discussions](https://github.com/your-repo/discussions)

---

**⭐ 如果这个项目对你有帮助，请给它一个 Star！**