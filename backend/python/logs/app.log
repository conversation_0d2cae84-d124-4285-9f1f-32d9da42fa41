2025-08-13 21:18:14 - utils.logger - INFO - BM25 Search Scores: {'CODE_OF_CONDUCT.md:0-30': 10.872780188026397, 'CODE_OF_CONDUCT.md:25-55': 7.38580355887274, 'GOVERNANCE.md:0-30': 12.565343523359052, 'GOVERNANCE.md:25-55': 5.53452800820853, 'GOVERNANCE.md:50-80': 4.06032460869417, 'README.md:0-30': 12.066660241021852, 'README.md:25-55': 6.25995028180258, 'README.md:50-80': 14.773496051176854, 'README.md:75-105': 12.972403731374587, 'README.md:100-130': 16.225503576473436, 'README.md:125-155': 13.98594139464565, 'CONTRIBUTING.md:0-30': 8.038861671108576, 'CONTRIBUTING.md:25-55': 15.053362001500421, 'CONTRIBUTING.md:50-80': 15.782770512192418, 'CONTRIBUTING.md:75-105': 7.653505279208259, 'CONTRIBUTING-zh.md:50-80': 3.8709526136606174, 'CONTRIBUTING-zh.md:75-105': 4.6875785751866115, 'README-zh.md:0-30': 1.4162856102690216, 'spring-ai-alibaba-jmanus/README.md:0-30': 2.1726685180747527, 'spring-ai-alibaba-jmanus/README.md:25-55': 25.516475517581796, 'spring-ai-alibaba-jmanus/README.md:50-80': 2.2176969475324566, 'spring-ai-alibaba-jmanus/README.md:75-105': 1.7579816852101768, 'spring-ai-alibaba-jmanus/README.md:100-130': 1.8426605816446973, 'spring-ai-alibaba-jmanus/README.md:125-155': 18.138361372900672, 'spring-ai-alibaba-jmanus/README.md:150-180': 21.16978108847772, 'spring-ai-alibaba-jmanus/ui-vue3/env.d.ts:0-30': 1.6213799903421466, 'spring-ai-alibaba-jmanus/ui-vue3/README.md:0-30': 5.004411811238316, 'spring-ai-alibaba-jmanus/ui-vue3/README.md:25-55': 2.7117070081607375, 'spring-ai-alibaba-jmanus/ui-vue3/README.md:75-105': 9.605104129922168, 'spring-ai-alibaba-jmanus/ui-vue3/README.md:100-130': 9.064278289983491, 'spring-ai-alibaba-jmanus/ui-vue3/README.md:125-155': 3.3622795054565056, 'spring-ai-alibaba-jmanus/ui-vue3/README.md:150-180': 9.792751502204833, 'spring-ai-alibaba-jmanus/ui-vue3/README.md:175-205': 8.868029948035216, 'spring-ai-alibaba-jmanus/ui-vue3/vite.config.ts:0-30': 1.5295406001183258, 'spring-ai-alibaba-jmanus/ui-vue3/vite.config.ts:25-55': 6.475458661734712, 'spring-ai-alibaba-jmanus/ui-vue3/vitest.config.ts:0-30': 1.5346539525074083, 'spring-ai-alibaba-jmanus/ui-vue3/cypress.config.ts:0-30': 1.7249526580211665, 'spring-ai-alibaba-jmanus/ui-vue3/cypress/support/commands.ts:0-30': 1.8959327741568117, 'spring-ai-alibaba-jmanus/ui-vue3/cypress/support/e2e.ts:0-30': 1.8207351761199357, 'spring-ai-alibaba-jmanus/ui-vue3/src/main.ts:0-30': 1.5094234550964287, 'spring-ai-alibaba-jmanus/ui-vue3/src/types/cache-data.ts:0-30': 1.65648406863643, 'spring-ai-alibaba-jmanus/ui-vue3/src/types/plan-template.ts:0-30': 1.6446150239729562, 'spring-ai-alibaba-jmanus/ui-vue3/src/types/plan-execution-record.ts:0-30': 2.1057931452632648, 'spring-ai-alibaba-jmanus/ui-vue3/src/types/plan-execution-record.ts:25-55': 1.693141844245956, 'spring-ai-alibaba-jmanus/ui-vue3/src/types/plan-execution-record.ts:75-105': 1.65648406863643, 'spring-ai-alibaba-jmanus/ui-vue3/src/types/plan-execution-record.ts:175-205': 1.6624830670303476, 'spring-ai-alibaba-jmanus/ui-vue3/src/stores/counter.ts:0-30': 1.6271269812152453, 'spring-ai-alibaba-jmanus/ui-vue3/src/stores/task.ts:0-30': 1.5714276365890276, 'spring-ai-alibaba-jmanus/ui-vue3/src/stores/sidebar.ts:0-30': 1.3780221876928578, 'spring-ai-alibaba-jmanus/ui-vue3/src/stores/sidebar.ts:100-130': 1.4946794771655387, 'spring-ai-alibaba-jmanus/ui-vue3/src/stores/sidebar.ts:150-180': 1.576825380051758, 'spring-ai-alibaba-jmanus/ui-vue3/src/stores/sidebar.ts:175-205': 1.582260333065363, 'spring-ai-alibaba-jmanus/ui-vue3/src/stores/sidebar.ts:225-255': 1.6271269812152453, 'spring-ai-alibaba-jmanus/ui-vue3/src/utils/plan-execution-manager.ts:0-30': 1.3301085874193976, 'spring-ai-alibaba-jmanus/ui-vue3/src/utils/plan-execution-manager.ts:150-180': 2.306852044848528, 'spring-ai-alibaba-jmanus/ui-vue3/src/utils/plan-execution-manager.ts:175-205': 1.4250789533148378, 'spring-ai-alibaba-jmanus/ui-vue3/src/utils/plan-execution-manager.ts:200-230': 2.026766665296864, 'spring-ai-alibaba-jmanus/ui-vue3/src/utils/plan-execution-manager.ts:225-255': 2.669990110107448, 'spring-ai-alibaba-jmanus/ui-vue3/src/utils/plan-execution-manager.ts:250-280': 1.3863454179444579, 'spring-ai-alibaba-jmanus/ui-vue3/src/utils/plan-execution-manager.ts:275-305': 3.056528356600238, 'spring-ai-alibaba-jmanus/ui-vue3/src/utils/plan-execution-manager.ts:325-355': 5.831899105746569, 'spring-ai-alibaba-jmanus/ui-vue3/src/utils/plan-execution-manager.ts:350-380': 3.1882174538647488, 'spring-ai-alibaba-jmanus/ui-vue3/src/utils/use-plan-execution.ts:0-30': 2.0866492893536632, 'spring-ai-alibaba-jmanus/ui-vue3/src/utils/request.ts:0-30': 1.5607422602136187, 'spring-ai-alibaba-jmanus/ui-vue3/src/utils/request.ts:25-55': 3.2584761210481337, 'spring-ai-alibaba-jmanus/ui-vue3/src/components/editor/MonacoEditor.ts:0-30': 3.9363005139949134, 'spring-ai-alibaba-jmanus/ui-vue3/src/components/editor/MonacoEditor.ts:25-55': 1.555453881109404, 'spring-ai-alibaba-jmanus/ui-vue3/src/api/mcp-api-service.ts:0-30': 1.6446150239729562, 'spring-ai-alibaba-jmanus/ui-vue3/src/api/direct-api-service.ts:0-30': 1.5094234550964287, 'spring-ai-alibaba-jmanus/ui-vue3/src/api/common-api-service.ts:0-30': 1.4567346345301488, 'spring-ai-alibaba-jmanus/ui-vue3/src/api/agent-api-service.ts:0-30': 1.65648406863643, 'spring-ai-alibaba-jmanus/ui-vue3/src/api/agent-api-service.ts:150-180': 1.6685256744000492, 'spring-ai-alibaba-jmanus/ui-vue3/src/api/plan-act-api-service.ts:0-30': 1.3990205075566946, 'spring-ai-alibaba-jmanus/ui-vue3/src/api/model-api-service.ts:0-30': 1.6624830670303476, 'spring-ai-alibaba-jmanus/ui-vue3/src/api/model-api-service.ts:150-180': 1.9037955790598078, 'spring-ai-alibaba-jmanus/ui-vue3/src/api/admin-api-service.ts:0-30': 1.6746123679952611, 'spring-ai-alibaba-jmanus/ui-vue3/src/base/i18n/en.ts:0-30': 2.191232374304636, 'spring-ai-alibaba-jmanus/ui-vue3/src/base/i18n/en.ts:100-130': 2.4294668832765534, 'spring-ai-alibaba-jmanus/ui-vue3/src/base/i18n/en.ts:175-205': 1.7588574018955523, 'spring-ai-alibaba-jmanus/ui-vue3/src/base/i18n/en.ts:200-230': 2.355115023531469, 'spring-ai-alibaba-jmanus/ui-vue3/src/base/i18n/en.ts:225-255': 2.4337602875157067, 'spring-ai-alibaba-jmanus/ui-vue3/src/base/i18n/en.ts:275-305': 1.3224450553019844, 'spring-ai-alibaba-jmanus/ui-vue3/src/base/i18n/en.ts:300-330': 5.82129427864715, 'spring-ai-alibaba-jmanus/ui-vue3/src/base/i18n/en.ts:325-355': 5.759722158874251, 'spring-ai-alibaba-jmanus/ui-vue3/src/base/i18n/en.ts:350-380': 1.4384756257963185, 'spring-ai-alibaba-jmanus/ui-vue3/src/base/i18n/en.ts:375-405': 2.5185388473191925, 'spring-ai-alibaba-jmanus/ui-vue3/src/base/i18n/en.ts:550-580': 2.5583882040950137, 'spring-ai-alibaba-jmanus/ui-vue3/src/base/i18n/en.ts:575-605': 1.6807436320551328, 'spring-ai-alibaba-jmanus/ui-vue3/src/base/i18n/en.ts:600-630': 7.982355658751353, 'spring-ai-alibaba-jmanus/ui-vue3/src/base/i18n/en.ts:625-655': 5.128993183965175, 'spring-ai-alibaba-jmanus/ui-vue3/src/base/i18n/zh.ts:0-30': 1.7380142090139563, 'spring-ai-alibaba-jmanus/ui-vue3/src/base/i18n/zh.ts:275-305': 5.161598469160591, 'spring-ai-alibaba-jmanus/ui-vue3/src/base/i18n/zh.ts:550-580': 2.039100362400001, 'spring-ai-alibaba-jmanus/ui-vue3/src/base/i18n/zh.ts:575-605': 5.449873101612473, 'spring-ai-alibaba-jmanus/ui-vue3/src/base/i18n/sortI18n.ts:0-30': 1.587732881718793, 'spring-ai-alibaba-jmanus/ui-vue3/src/base/i18n/type.ts:0-30': 1.8352936750219124, 'spring-ai-alibaba-jmanus/ui-vue3/src/base/i18n/index.ts:0-30': 1.6043800433693445, 'spring-ai-alibaba-jmanus/ui-vue3/src/router/defaultRoutes.ts:0-30': 1.6271269812152453, 'spring-ai-alibaba-jmanus/ui-vue3/src/router/index.ts:0-30': 1.6505282088323439, 'spring-ai-alibaba-jmanus/src/temp/parallel_agent_params.md:50-80': 2.143851877853078, 'spring-ai-alibaba-jmanus/src/test/resources/test_docs.md:225-255': 2.534446325815135, 'spring-ai-alibaba-jmanus/src/test/resources/test_docs.md:250-280': 3.1764464723515666, 'spring-ai-alibaba-jmanus/src/test/resources/test_docs.md:625-655': 3.1189741254922634, 'spring-ai-alibaba-jmanus/src/test/resources/test_docs.md:650-680': 3.1136852816277996, 'spring-ai-alibaba-jmanus/src/test/resources/test_docs.md:1025-1055': 3.1808484594745527, 'spring-ai-alibaba-jmanus/src/test/resources/test_docs.md:1050-1080': 2.5205304897082357, 'spring-ai-alibaba-jmanus/src/test/resources/test_docs.md:1425-1455': 3.1941279308680444, 'spring-ai-alibaba-jmanus/src/test/resources/test_docs.md:1825-1855': 3.1764464723515666, 'spring-ai-alibaba-jmanus/src/test/resources/test_docs.md:2200-2230': 2.534446325815135, 'spring-ai-alibaba-jmanus/src/test/resources/test_docs.md:2225-2255': 3.1764464723515666, 'spring-ai-alibaba-jmanus/src/test/resources/test_docs.md:2600-2630': 3.1189741254922634, 'spring-ai-alibaba-jmanus/src/test/resources/test_docs.md:2625-2655': 3.1136852816277996, 'spring-ai-alibaba-jmanus/src/test/resources/test_docs.md:3000-3030': 3.1808484594745527, 'spring-ai-alibaba-jmanus/src/test/resources/test_docs.md:3025-3055': 2.5205304897082357, 'spring-ai-alibaba-jmanus/src/test/resources/test_docs.md:3400-3430': 3.1941279308680444, 'spring-ai-alibaba-jmanus/src/test/resources/test_docs.md:3800-3830': 3.1764464723515666, 'spring-ai-alibaba-jmanus/src/test/resources/test_docs.md:4175-4205': 2.534446325815135, 'spring-ai-alibaba-jmanus/src/test/resources/test_docs.md:4200-4230': 3.1764464723515666, 'spring-ai-alibaba-jmanus/src/test/resources/test_docs.md:4575-4605': 3.1189741254922634, 'spring-ai-alibaba-jmanus/src/test/resources/test_docs.md:4600-4630': 3.1136852816277996, 'spring-ai-alibaba-jmanus/src/test/resources/test_docs.md:4975-5005': 3.1808484594745527, 'spring-ai-alibaba-jmanus/src/test/resources/test_docs.md:5000-5030': 2.5205304897082357, 'spring-ai-alibaba-jmanus/src/test/resources/test_docs.md:5375-5405': 3.1941279308680444, 'spring-ai-alibaba-jmanus/src/test/resources/test_docs.md:5775-5805': 3.1764464723515666, 'spring-ai-alibaba-jmanus/src/test/resources/test_docs.md:6150-6180': 2.534446325815135, 'spring-ai-alibaba-jmanus/src/test/resources/test_docs.md:6175-6205': 3.1764464723515666, 'spring-ai-alibaba-jmanus/src/test/resources/test_docs.md:6550-6580': 3.1189741254922634, 'spring-ai-alibaba-jmanus/src/test/resources/test_docs.md:6575-6605': 3.1136852816277996, 'spring-ai-alibaba-jmanus/src/test/resources/test_docs.md:6950-6980': 3.1808484594745527, 'spring-ai-alibaba-jmanus/src/test/resources/test_docs.md:6975-7005': 2.5205304897082357, 'spring-ai-alibaba-jmanus/src/test/resources/test_docs.md:7350-7380': 3.1941279308680444, 'spring-ai-alibaba-jmanus/src/test/resources/test_docs.md:7750-7780': 3.1764464723515666, 'spring-ai-alibaba-jmanus/src/test/resources/test_docs.md:8125-8155': 2.534446325815135, 'spring-ai-alibaba-jmanus/src/test/resources/test_docs.md:8150-8180': 3.1764464723515666, 'spring-ai-alibaba-jmanus/src/test/resources/test_docs.md:8525-8555': 3.1189741254922634, 'spring-ai-alibaba-jmanus/src/test/resources/test_docs.md:8550-8580': 3.1136852816277996, 'spring-ai-alibaba-jmanus/src/test/resources/test_docs.md:8925-8955': 3.1808484594745527, 'spring-ai-alibaba-jmanus/src/test/resources/test_docs.md:8950-8980': 2.5205304897082357, 'spring-ai-alibaba-jmanus/src/test/resources/test_docs.md:9325-9355': 3.1941279308680444, 'spring-ai-alibaba-jmanus/src/test/resources/test_docs.md:9725-9755': 3.1764464723515666, 'spring-ai-alibaba-jmanus/src/test/resources/test_docs.md:10100-10130': 2.534446325815135, 'spring-ai-alibaba-jmanus/src/test/resources/test_docs.md:10125-10155': 3.1764464723515666, 'spring-ai-alibaba-jmanus/src/test/resources/test_docs.md:10500-10530': 3.1189741254922634, 'spring-ai-alibaba-jmanus/src/test/resources/test_docs.md:10525-10555': 3.1136852816277996, 'spring-ai-alibaba-jmanus/src/test/resources/test_docs.md:10900-10930': 3.1808484594745527, 'spring-ai-alibaba-jmanus/src/test/resources/test_docs.md:10925-10955': 2.5205304897082357, 'spring-ai-alibaba-jmanus/src/test/resources/test_docs.md:11300-11330': 3.1941279308680444, 'spring-ai-alibaba-jmanus/src/test/resources/test_docs.md:11700-11730': 3.1764464723515666, 'spring-ai-alibaba-jmanus/src/test/resources/test_docs.md:12075-12105': 2.534446325815135, 'spring-ai-alibaba-jmanus/src/test/resources/test_docs.md:12100-12130': 3.1764464723515666, 'spring-ai-alibaba-jmanus/src/test/resources/test_docs.md:12475-12505': 3.1189741254922634, 'spring-ai-alibaba-jmanus/src/test/resources/test_docs.md:12500-12530': 3.1136852816277996, 'spring-ai-alibaba-jmanus/src/test/resources/test_docs.md:12875-12905': 3.1808484594745527, 'spring-ai-alibaba-jmanus/src/test/resources/test_docs.md:12900-12930': 2.5205304897082357, 'spring-ai-alibaba-jmanus/src/test/resources/test_docs.md:13275-13305': 3.1941279308680444, 'spring-ai-alibaba-jmanus/src/test/resources/test_docs.md:13675-13705': 3.1764464723515666, 'spring-ai-alibaba-jmanus/src/test/resources/test_docs.md:14050-14080': 2.534446325815135, 'spring-ai-alibaba-jmanus/src/test/resources/test_docs.md:14075-14105': 3.1764464723515666, 'spring-ai-alibaba-jmanus/src/test/resources/test_docs.md:14450-14480': 3.1189741254922634, 'spring-ai-alibaba-jmanus/src/test/resources/test_docs.md:14475-14505': 3.1136852816277996, 'spring-ai-alibaba-jmanus/src/test/resources/test_docs.md:14850-14880': 3.1808484594745527, 'spring-ai-alibaba-jmanus/src/test/resources/test_docs.md:14875-14905': 2.5205304897082357, 'spring-ai-alibaba-jmanus/src/test/resources/test_docs.md:15250-15280': 3.1941279308680444, 'spring-ai-alibaba-jmanus/src/test/resources/test_docs.md:15650-15680': 3.1764464723515666, 'spring-ai-alibaba-jmanus/src/test/resources/test_docs.md:16025-16055': 2.534446325815135, 'spring-ai-alibaba-jmanus/src/test/resources/test_docs.md:16050-16080': 3.1764464723515666, 'spring-ai-alibaba-jmanus/src/test/resources/test_docs.md:16425-16455': 3.1189741254922634, 'spring-ai-alibaba-jmanus/src/test/resources/test_docs.md:16450-16480': 3.1136852816277996, 'spring-ai-alibaba-jmanus/src/test/resources/test_docs.md:16825-16855': 3.1808484594745527, 'spring-ai-alibaba-jmanus/src/test/resources/test_docs.md:16850-16880': 2.5205304897082357, 'spring-ai-alibaba-jmanus/src/test/resources/test_docs.md:17225-17255': 3.1941279308680444, 'spring-ai-alibaba-jmanus/src/test/resources/test_docs.md:17625-17655': 3.1764464723515666, 'spring-ai-alibaba-jmanus/src/test/java/com/alibaba/cloud/ai/example/manus/planning/executor/factory/PlanExecutorFactorySpringTest.java:0-30': 1.1981832565255308, 'spring-ai-alibaba-jmanus/src/test/java/com/alibaba/cloud/ai/example/manus/tool/BrowserUseToolSpringTest.java:0-30': 1.4076001190620582, 'spring-ai-alibaba-jmanus/src/test/java/com/alibaba/cloud/ai/example/manus/tool/BrowserUseToolSpringTest.java:25-55': 2.928187127731471, 'spring-ai-alibaba-jmanus/src/test/java/com/alibaba/cloud/ai/example/manus/tool/BrowserUseToolSpringTest.java:150-180': 1.970225178300757, 'spring-ai-alibaba-jmanus/src/test/java/com/alibaba/cloud/ai/example/manus/tool/BrowserUseToolSpringTest.java:175-205': 1.390544851406706, 'spring-ai-alibaba-jmanus/src/test/java/com/alibaba/cloud/ai/example/manus/tool/BrowserUseToolSpringTest.java:200-230': 1.825296211129989, 'spring-ai-alibaba-jmanus/src/test/java/com/alibaba/cloud/ai/example/manus/tool/BrowserUseToolSpringTest.java:225-255': 2.0448227535674666, 'spring-ai-alibaba-jmanus/src/test/java/com/alibaba/cloud/ai/example/manus/tool/BrowserUseToolSpringTest.java:250-280': 1.924789599043932, 'spring-ai-alibaba-jmanus/src/test/java/com/alibaba/cloud/ai/example/manus/tool/BrowserUseToolSpringTest.java:300-330': 2.639711346676289, 'spring-ai-alibaba-jmanus/src/test/java/com/alibaba/cloud/ai/example/manus/tool/BrowserUseToolSpringTest.java:350-380': 2.4656341079501094, 'spring-ai-alibaba-jmanus/src/test/java/com/alibaba/cloud/ai/example/manus/tool/BrowserUseToolSpringTest.java:375-405': 2.2843904665566943, 'spring-ai-alibaba-jmanus/src/test/java/com/alibaba/cloud/ai/example/manus/tool/BrowserUseToolSpringTest.java:400-430': 2.547739006255517, 'spring-ai-alibaba-jmanus/src/test/java/com/alibaba/cloud/ai/example/manus/tool/BrowserUseToolSpringTest.java:425-455': 2.119175947628611, 'spring-ai-alibaba-jmanus/src/test/java/com/alibaba/cloud/ai/example/manus/tool/BrowserUseToolSpringTest.java:475-505': 1.8108952680574137, 'spring-ai-alibaba-jmanus/src/test/java/com/alibaba/cloud/ai/example/manus/tool/BrowserUseToolSpringTest.java:550-580': 2.2074947222987906, 'spring-ai-alibaba-jmanus/src/test/java/com/alibaba/cloud/ai/example/manus/tool/BrowserUseToolSpringTest.java:575-605': 4.573491444897113, 'spring-ai-alibaba-jmanus/src/test/java/com/alibaba/cloud/ai/example/manus/tool/BrowserUseToolSpringTest.java:600-630': 3.8555748464250983, 'spring-ai-alibaba-jmanus/src/test/java/com/alibaba/cloud/ai/example/manus/tool/BrowserUseToolSpringTest.java:625-655': 1.638744057033641, 'spring-ai-alibaba-jmanus/src/test/java/com/alibaba/cloud/ai/example/manus/tool/innerStorage/InnerStorageToolTest.java:0-30': 3.8321431602588425, 'spring-ai-alibaba-jmanus/src/test/java/com/alibaba/cloud/ai/example/manus/tool/mapreduce/MapReduceToolTest.java:0-30': 3.9363005139949134, 'spring-ai-alibaba-jmanus/src/test/java/com/alibaba/cloud/ai/example/manus/tool/mapreduce/MapReduceToolTest.java:50-80': 2.269007401657337, 'spring-ai-alibaba-jmanus/src/test/java/com/alibaba/cloud/ai/example/manus/tool/mapreduce/MapReduceToolTest.java:100-130': 2.534645878649833, 'spring-ai-alibaba-jmanus/src/test/java/com/alibaba/cloud/ai/example/manus/tool/mapreduce/MapReduceToolTest.java:800-830': 4.725487523651341, 'spring-ai-alibaba-jmanus/src/test/java/com/alibaba/cloud/ai/example/manus/tool/mapreduce/MapReduceToolTest.java:825-855': 4.623578940116165, 'spring-ai-alibaba-jmanus/src/main/resources/static/ui/assets/notFound-DkYQ5be1.js:0-30': 3.3489438352162257, 'spring-ai-alibaba-jmanus/src/main/resources/static/ui/assets/index-CeLWPe5d.js:0-30': 0.3664367934444216, 'spring-ai-alibaba-jmanus/src/main/resources/static/ui/assets/index-BfNjyMkn.js:0-30': 0.8717121675741066, 'spring-ai-alibaba-jmanus/src/main/resources/static/ui/assets/index-CJEIDQCi.js:0-30': 2.370948960573387, 'spring-ai-alibaba-jmanus/src/main/resources/tool/extract-interactive-elements.js:0-30': 4.097154333921666, 'spring-ai-alibaba-jmanus/src/main/resources/tool/extract-interactive-elements.js:75-105': 1.7579816852101768, 'spring-ai-alibaba-jmanus/src/main/resources/tool/extract-interactive-elements.js:175-205': 1.65648406863643, 'spring-ai-alibaba-jmanus/src/main/resources/tool/extract-interactive-elements.js:225-255': 1.65648406863643, 'spring-ai-alibaba-jmanus/src/main/resources/tool/extract-interactive-elements.js:250-280': 2.0914025469442454, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/OpenManusSpringBootApplication.java:0-30': 1.5660667218567792, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/llm/LlmService.java:0-30': 1.3821712726179565, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/llm/LlmService.java:25-55': 1.4475475537922684, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/dynamic/agent/ToolCallbackProvider.java:0-30': 1.6100069440831988, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/dynamic/agent/DynamicAgent.java:0-30': 1.3536415319003627, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/dynamic/agent/DynamicAgent.java:100-130': 1.2302952180021771, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/dynamic/agent/DynamicAgent.java:125-155': 1.3863454179444579, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/dynamic/agent/DynamicAgent.java:200-230': 1.1677049645758644, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/dynamic/agent/DynamicAgent.java:225-255': 1.236925271703581, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/dynamic/agent/DynamicAgent.java:425-455': 1.5244612091889669, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/dynamic/agent/DynamicAgent.java:525-555': 1.3821712726179565, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/dynamic/agent/DynamicAgent.java:575-605': 1.2270067732655228, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/dynamic/agent/repository/DynamicAgentRepository.java:0-30': 1.4250789533148378, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/dynamic/agent/entity/DynamicAgentEntity.java:0-30': 4.857811098768327, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/dynamic/agent/entity/DynamicAgentEntity.java:25-55': 3.378916338986646, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/dynamic/agent/entity/DynamicAgentEntity.java:50-80': 1.7784132932580297, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/dynamic/agent/annotation/DynamicAgentDefinition.java:0-30': 1.539801608104721, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/dynamic/agent/controller/AgentController.java:0-30': 1.3148693254696644, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/dynamic/agent/startupAgent/StartupAgentConfigLoader.java:0-30': 1.4250789533148378, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/dynamic/agent/startupAgent/StartupAgentConfigLoader.java:25-55': 1.4707360092264137, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/dynamic/agent/startupAgent/StartupAgentConfigLoader.java:175-205': 1.8279854392172998, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/dynamic/agent/startupAgent/StartupAgentConfigLoader.java:200-230': 1.6043800433693445, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/dynamic/agent/model/Tool.java:0-30': 2.2725913165823757, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/dynamic/agent/model/Tool.java:25-55': 1.9441085760502244, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/dynamic/agent/service/DynamicAgentLoader.java:0-30': 1.3224450553019844, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/dynamic/agent/service/AgentService.java:0-30': 1.4802207498974258, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/dynamic/agent/service/AgentService.java:25-55': 1.5987923371942092, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/dynamic/agent/service/AgentConfig.java:0-30': 1.6156734531778587, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/dynamic/agent/service/AgentConfig.java:25-55': 1.9607158913940002, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/dynamic/agent/service/AgentServiceImpl.java:0-30': 1.3657230586340428, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/dynamic/agent/service/AgentServiceImpl.java:175-205': 1.0900662555389968, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/dynamic/agent/service/AgentServiceImpl.java:200-230': 1.0747537114785533, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/dynamic/agent/service/DynamicAgentScanner.java:0-30': 1.3821712726179565, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/dynamic/agent/service/DynamicAgentScanner.java:50-80': 2.489029535139341, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/dynamic/agent/service/DynamicAgentScanner.java:75-105': 1.307379897570568, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/dynamic/agent/service/DynamicAgentScanner.java:125-155': 1.2963043732383113, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/dynamic/agent/service/DynamicAgentScanner.java:150-180': 2.319010587193168, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/dynamic/agent/service/DynamicAgentScanner.java:175-205': 1.5044765819016563, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/dynamic/mcp/repository/McpConfigRepository.java:0-30': 1.4802207498974258, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/dynamic/mcp/controller/McpController.java:0-30': 1.2538173069550065, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/dynamic/mcp/model/vo/McpServerConfig.java:0-30': 1.4946794771655387, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/dynamic/mcp/model/vo/McpServiceEntity.java:0-30': 1.4995620278218964, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/dynamic/mcp/model/vo/McpConfigRequestVO.java:0-30': 1.6100069440831988, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/dynamic/mcp/model/vo/McpTool.java:0-30': 3.8777467059015924, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/dynamic/mcp/model/vo/McpConfigVO.java:0-30': 1.5295406001183258, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/dynamic/mcp/model/vo/McpConfigVO.java:50-80': 1.6505282088323439, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/dynamic/mcp/model/vo/McpState.java:0-30': 1.6446150239729562, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/dynamic/mcp/model/vo/McpServersConfig.java:0-30': 1.4567346345301488, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/dynamic/mcp/model/po/McpConfigType.java:0-30': 1.7715501919595864, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/dynamic/mcp/model/po/McpConfigEntity.java:0-30': 4.744131278893166, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/dynamic/mcp/model/po/McpConfigEntity.java:25-55': 5.58420768998938, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/dynamic/mcp/service/McpStateHolderService.java:0-30': 1.4707360092264137, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/dynamic/mcp/service/McpService.java:0-30': 1.1797083017415622, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/dynamic/mcp/service/McpService.java:175-205': 1.3378614571544742, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/dynamic/mcp/service/McpService.java:200-230': 1.2607040145492494, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/dynamic/mcp/service/McpService.java:225-255': 1.3148693254696644, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/dynamic/prompt/repository/PromptRepository.java:0-30': 1.4295166990043104, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/dynamic/prompt/controller/PromptController.java:0-30': 1.4754631369960434, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/dynamic/prompt/model/vo/PromptVO.java:0-30': 1.582260333065363, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/dynamic/prompt/model/enums/PromptType.java:0-30': 1.7715501919595864, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/dynamic/prompt/model/enums/PromptEnum.java:0-30': 1.1797083017415622, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/dynamic/prompt/model/po/PromptEntity.java:0-30': 4.681528675637577, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/dynamic/prompt/model/po/PromptEntity.java:25-55': 3.5540371838937097, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/dynamic/prompt/service/PromptServiceImpl.java:0-30': 1.3224450553019844, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/dynamic/prompt/service/PromptService.java:0-30': 1.539801608104721, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/dynamic/prompt/service/PromptDataInitializer.java:0-30': 1.3821712726179565, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/dynamic/model/repository/DynamicModelRepository.java:0-30': 1.5044765819016563, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/dynamic/model/entity/DynamicModelEntity.java:0-30': 4.66613532954322, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/dynamic/model/entity/DynamicModelEntity.java:25-55': 3.8382160536048167, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/dynamic/model/entity/DynamicModelEntity.java:50-80': 1.8727292772502506, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/dynamic/model/entity/DynamicModelEntity.java:100-130': 1.4995620278218964, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/dynamic/model/entity/DynamicModelEntity.java:125-155': 2.133886349869392, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/dynamic/model/controller/ModelController.java:0-30': 1.3990205075566946, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/dynamic/model/model/vo/ModelConfig.java:0-30': 1.6685256744000492, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/dynamic/model/model/enums/ModelType.java:0-30': 8.849332201321994, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/dynamic/model/model/enums/ModelType.java:25-55': 14.80226427770058, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/dynamic/model/model/enums/ModelType.java:50-80': 9.057191651913982, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/dynamic/model/model/enums/ModelType.java:75-105': 2.4531822838022386, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/dynamic/model/service/ModelService.java:0-30': 1.4802207498974258, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/dynamic/model/service/ModelServiceImpl.java:0-30': 1.3036670873065823, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/dynamic/model/service/ModelDataInitialization.java:0-30': 1.4475475537922684, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/config/McpServerConfigurationLoader.java:0-30': 1.4206686751462156, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/config/McpServerConfigurationLoader.java:50-80': 1.2172460758505306, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/config/ConfigProperty.java:0-30': 1.544983913259478, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/config/ConfigController.java:0-30': 1.3863454179444579, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/config/ConfigCacheEntry.java:0-30': 1.582260333065363, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/config/ConfigOption.java:0-30': 1.5932434174609875, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/config/ManusProperties.java:0-30': 1.5346539525074083, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/config/ManusProperties.java:150-180': 1.3496617171995993, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/config/ManusProperties.java:250-280': 1.2818256526239191, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/config/ManusProperties.java:275-305': 1.2641758158808274, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/config/ConfigService.java:0-30': 1.4707360092264137, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/config/repository/ConfigRepository.java:0-30': 1.5244612091889669, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/config/repository/ConfigRepository.java:25-55': 2.1401539252526356, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/config/entity/ConfigEntity.java:0-30': 4.650842881485016, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/config/entity/ConfigEntity.java:25-55': 3.965032795221346, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/config/entity/ConfigEntity.java:100-130': 1.7993254061418673, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/config/entity/ConfigInputType.java:0-30': 1.744619444156759, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/config/startUp/AppStartupListener.java:0-30': 1.4946794771655387, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/config/startUp/ConfigAppStartupListener.java:0-30': 1.4707360092264137, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/config/startUp/ConfigAppStartupListener.java:50-80': 1.4076001190620582, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/config/startUp/ConfigAppStartupListener.java:75-105': 2.2270570566895747, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/planning/PlanningFactory.java:0-30': 1.1415705577595903, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/planning/PlanningFactory.java:25-55': 2.5509566934417762, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/planning/PlanningFactory.java:225-255': 2.2581701435669403, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/planning/creator/PlanCreator.java:0-30': 1.2641758158808274, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/planning/creator/PlanCreator.java:50-80': 1.3417718832367183, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/planning/creator/PlanCreator.java:100-130': 1.307379897570568, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/planning/repository/PlanTemplateRepository.java:0-30': 1.5244612091889669, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/planning/repository/PlanTemplateVersionRepository.java:0-30': 1.5144029672636237, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/planning/repository/PlanTemplateVersionRepository.java:25-55': 1.3863454179444579, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/planning/repository/PlanTemplateVersionRepository.java:50-80': 1.9441085760502244, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/planning/coordinator/PlanIdDispatcher.java:0-30': 2.720836156126353, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/planning/coordinator/PlanIdDispatcher.java:50-80': 2.120383154394645, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/planning/coordinator/PlanIdDispatcher.java:75-105': 1.5987923371942092, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/planning/coordinator/PlanIdDispatcher.java:150-180': 2.3268469320318435, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/planning/coordinator/PlanIdDispatcher.java:175-205': 7.03951019312406, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/planning/coordinator/EnhancedPlanningCoordinator.java:0-30': 1.3301085874193976, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/planning/coordinator/EnhancedPlanningCoordinator.java:75-105': 1.4295166990043104, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/planning/coordinator/EnhancedPlanningCoordinator.java:100-130': 1.3457052358459312, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/planning/coordinator/PlanningCoordinator.java:0-30': 1.3301085874193976, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/planning/coordinator/PlanningCoordinator.java:50-80': 1.4754631369960434, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/planning/coordinator/PlanningCoordinator.java:75-105': 1.4429973314407667, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/planning/coordinator/PlanningCoordinator.java:100-130': 1.3148693254696644, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/planning/finalizer/PlanFinalizer.java:0-30': 1.2999753052429273, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/planning/finalizer/PlanFinalizer.java:50-80': 5.746927085394178, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/planning/finalizer/PlanFinalizer.java:75-105': 1.849360979440481, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/planning/finalizer/PlanFinalizer.java:100-130': 5.706705539929781, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/planning/finalizer/PlanFinalizer.java:125-155': 3.361557255043659, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/planning/controller/PlanTemplateController.java:0-30': 1.4162856102690216, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/planning/controller/PlanTemplateController.java:75-105': 1.3657230586340428, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/planning/controller/PlanTemplateController.java:100-130': 4.1923763136237016, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/planning/controller/PlanTemplateController.java:125-155': 2.9528192552035555, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/planning/controller/PlanTemplateController.java:150-180': 4.27641179519967, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/planning/controller/PlanTemplateController.java:250-280': 2.1057931452632648, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/planning/controller/PlanTemplateController.java:300-330': 1.3821712726179565, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/planning/controller/PlanTemplateController.java:375-405': 1.4475475537922684, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/planning/controller/PlanTemplateController.java:450-480': 1.4206686751462156, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/planning/controller/PlanTemplateController.java:475-505': 1.3262657509484723, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/planning/controller/PlanTemplateController.java:575-605': 1.4995620278218964, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/planning/controller/ManusController.java:0-30': 1.2336013365262284, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/planning/controller/ManusController.java:50-80': 1.4475475537922684, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/planning/controller/ManusController.java:75-105': 1.390544851406706, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/planning/controller/ManusController.java:100-130': 2.3112268476059525, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/planning/controller/ManusController.java:125-155': 1.8623209615718375, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/planning/model/vo/UserInputWaitState.java:0-30': 1.6156734531778587, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/planning/model/vo/AbstractExecutionPlan.java:0-30': 1.6271269812152453, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/planning/model/vo/AbstractExecutionPlan.java:150-180': 2.5292797652830648, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/planning/model/vo/PlanType.java:0-30': 1.7784132932580297, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/planning/model/vo/ExecutionContext.java:0-30': 5.4860450722653935, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/planning/model/vo/ExecutionContext.java:25-55': 7.742200362236661, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/planning/model/vo/ExecutionContext.java:50-80': 4.416303564664628, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/planning/model/vo/ExecutionContext.java:100-130': 4.836555478110112, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/planning/model/vo/ExecutionContext.java:125-155': 5.901341535154519, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/planning/model/vo/ExecutionContext.java:200-230': 1.693141844245956, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/planning/model/vo/PlanInterface.java:0-30': 1.3339737577322137, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/planning/model/vo/PlanInterface.java:25-55': 1.3262657509484723, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/planning/model/vo/PlanInterface.java:150-180': 2.6288847087989495, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/planning/model/vo/ExecutionPlan.java:0-30': 1.5194154424953983, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/planning/model/vo/ExecutionPlan.java:100-130': 1.4754631369960434, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/planning/model/vo/ExecutionPlan.java:125-155': 2.923053667873056, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/planning/model/vo/ExecutionPlan.java:150-180': 2.7801571596768144, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/planning/model/vo/ExecutionStep.java:0-30': 1.5295406001183258, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/planning/model/vo/ExecutionResult.java:0-30': 1.5987923371942092, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/planning/model/vo/ExecutionResult.java:25-55': 1.7184952272002534, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/planning/model/vo/mapreduce/ExecutionNode.java:0-30': 1.3738979381611398, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/planning/model/vo/mapreduce/MapReduceStepType.java:0-30': 1.731458800865721, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/planning/model/vo/mapreduce/MapReduceNode.java:0-30': 1.4802207498974258, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/planning/model/vo/mapreduce/SequentialNode.java:0-30': 1.5144029672636237, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/planning/model/vo/mapreduce/AbstractExecutionNode.java:0-30': 1.5607422602136187, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/planning/model/vo/mapreduce/MapReduceExecutionPlan.java:0-30': 1.452126563470489, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/planning/model/vo/mapreduce/MapReduceExecutionPlan.java:175-205': 2.0914025469442454, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/planning/model/vo/mapreduce/MapReduceExecutionPlan.java:200-230': 1.3821712726179565, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/planning/model/vo/mapreduce/MapReduceExecutionPlan.java:225-255': 2.7339399057931555, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/planning/model/vo/mapreduce/MapReduceExecutionPlan.java:250-280': 2.8039309787449804, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/planning/model/vo/mapreduce/MapReduceExecutionPlan.java:375-405': 2.468661647832403, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/planning/model/po/PlanTemplate.java:0-30': 1.5714276365890276, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/planning/model/po/PlanTemplate.java:50-80': 1.6624830670303476, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/planning/model/po/PlanTemplateVersion.java:0-30': 4.744131278893166, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/planning/model/po/PlanTemplateVersion.java:25-55': 3.3320075938941764, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/planning/model/po/PlanTemplateVersion.java:50-80': 1.6746123679952611, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/planning/executor/AbstractPlanExecutor.java:0-30': 1.2641758158808274, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/planning/executor/AbstractPlanExecutor.java:25-55': 2.1009743252108466, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/planning/executor/PlanExecutor.java:0-30': 1.3036670873065823, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/planning/executor/PlanExecutor.java:25-55': 1.4429973314407667, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/planning/executor/PlanExecutorInterface.java:0-30': 1.65648406863643, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/planning/executor/MapReducePlanExecutor.java:0-30': 1.1444164674175241, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/planning/executor/MapReducePlanExecutor.java:675-705': 2.641566698398496, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/planning/executor/MapReducePlanExecutor.java:850-880': 2.5185388473191925, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/planning/executor/factory/PlanExecutorFactory.java:0-30': 1.2044708402952395, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/planning/service/UserInputService.java:0-30': 1.4032971997212973, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/planning/service/UserInputService.java:25-55': 3.273867961790798, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/planning/service/UserInputService.java:50-80': 3.4470304138715786, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/planning/service/UserInputService.java:75-105': 1.2963043732383113, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/planning/service/PlanTemplateService.java:0-30': 1.4295166990043104, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/planning/service/PlanTemplateService.java:25-55': 1.394769803506785, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/planning/service/PlanTemplateService.java:50-80': 1.916752790562206, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/planning/service/PlanTemplateService.java:75-105': 1.5502012189992864, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/planning/service/PlanTemplateService.java:175-205': 1.3262657509484723, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/planning/service/PlanTemplateService.java:200-230': 1.307379897570568, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/planning/service/PlanTemplateService.java:300-330': 2.0573778674580008, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/planning/factory/PlanningToolFactory.java:0-30': 1.4250789533148378, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/agent/AgentState.java:0-30': 1.6043800433693445, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/agent/BaseAgent.java:0-30': 1.31111391621134, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/agent/BaseAgent.java:25-55': 2.616721730045128, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/agent/BaseAgent.java:75-105': 2.115497405994602, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/agent/BaseAgent.java:100-130': 2.6795436889457362, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/agent/BaseAgent.java:125-155': 2.408225095645459, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/agent/BaseAgent.java:150-180': 2.0725182748781217, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/agent/BaseAgent.java:175-205': 1.2108247615986183, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/agent/BaseAgent.java:300-330': 1.4946794771655387, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/agent/BaseAgent.java:325-355': 1.4339821696206667, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/agent/BaseAgent.java:375-405': 1.587732881718793, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/agent/ReActAgent.java:0-30': 2.0090266618268062, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/agent/ReActAgent.java:25-55': 2.291994199336079, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/prompt/PromptLoader.java:0-30': 1.4850091437813338, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/workflow/SummaryWorkflow.java:0-30': 1.31111391621134, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/recorder/PlanExecutionRecorder.java:0-30': 2.026766665296864, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/recorder/PlanExecutionRecorder.java:25-55': 1.5607422602136187, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/recorder/PlanExecutionRecorder.java:50-80': 4.5744083094950145, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/recorder/PlanExecutionRecorder.java:75-105': 1.9127595016868155, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/recorder/PlanExecutionRecorder.java:100-130': 5.31206588538254, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/recorder/PlanExecutionRecorder.java:125-155': 3.7270240997578457, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/recorder/RepositoryPlanExecutionRecorder.java:0-30': 1.2302952180021771, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/recorder/RepositoryPlanExecutionRecorder.java:50-80': 3.0107125208319188, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/recorder/RepositoryPlanExecutionRecorder.java:75-105': 4.4981830753256755, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/recorder/RepositoryPlanExecutionRecorder.java:200-230': 1.0798098678034935, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/recorder/RepositoryPlanExecutionRecorder.java:225-255': 2.4642443332029242, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/recorder/RepositoryPlanExecutionRecorder.java:250-280': 1.7554943441733541, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/recorder/RepositoryPlanExecutionRecorder.java:325-355': 1.214026927770339, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/recorder/RepositoryPlanExecutionRecorder.java:350-380': 3.272205291400792, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/recorder/RepositoryPlanExecutionRecorder.java:375-405': 3.760250059030743, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/recorder/RepositoryPlanExecutionRecorder.java:400-430': 3.984964920434274, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/recorder/RepositoryPlanExecutionRecorder.java:425-455': 4.142711687981537, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/recorder/RepositoryPlanExecutionRecorder.java:450-480': 1.936971978195703, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/recorder/RepositoryPlanExecutionRecorder.java:475-505': 1.2402671679441073, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/recorder/RepositoryPlanExecutionRecorder.java:500-530': 1.2237358609737488, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/recorder/RepositoryPlanExecutionRecorder.java:600-630': 1.4339821696206667, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/recorder/repository/PlanExecutionRecordRepository.java:0-30': 1.452126563470489, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/recorder/converter/StringAttributeConverter.java:0-30': 1.4250789533148378, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/recorder/entity/PlanExecutionRecordEntity.java:0-30': 2.1501782175023574, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/recorder/entity/PlanExecutionRecordEntity.java:25-55': 3.822932075694056, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/recorder/entity/ThinkActRecord.java:0-30': 2.3668367208316585, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/recorder/entity/ThinkActRecord.java:150-180': 1.8727292772502506, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/recorder/entity/PlanExecutionRecord.java:0-30': 2.058577366497676, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/recorder/entity/PlanExecutionRecord.java:75-105': 1.4995620278218964, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/recorder/entity/PlanExecutionRecord.java:125-155': 1.6271269812152453, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/recorder/entity/PlanExecutionRecord.java:150-180': 2.354172872838646, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/recorder/entity/PlanExecutionRecord.java:175-205': 2.3010703669032884, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/recorder/entity/AgentExecutionRecord.java:0-30': 2.0961775092284123, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/recorder/entity/AgentExecutionRecord.java:25-55': 1.4032971997212973, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/recorder/entity/AgentExecutionRecord.java:125-155': 2.4289098004589276, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/recorder/entity/AgentExecutionRecord.java:275-305': 1.5932434174609875, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/MapReducePlanningTool.java:0-30': 3.4838845570259034, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/TerminateTool.java:0-30': 4.097154333921666, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/TerminateTool.java:75-105': 1.544983913259478, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/ToolCallBiFunctionDef.java:0-30': 4.285760421051207, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/AbstractBaseTool.java:0-30': 4.230124094778663, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/FormInputTool.java:0-30': 4.660612315962176, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/FormInputTool.java:25-55': 1.7853297775722856, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/FormInputTool.java:50-80': 1.8575732562866811, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/FormInputTool.java:75-105': 1.9607158913940002, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/FormInputTool.java:100-130': 1.865120477714578, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/FormInputTool.java:150-180': 1.390544851406706, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/FormInputTool.java:175-205': 2.0772072989769175, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/FormInputTool.java:275-305': 1.4850091437813338, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/howToCreateNewTool.md:0-30': 2.312662850110104, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/PlanningToolInterface.java:0-30': 4.5237534613628, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/DocLoaderTool.java:0-30': 3.924448712718318, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/DocLoaderTool.java:75-105': 1.3990205075566946, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/PlanningTool.java:0-30': 3.701548949793365, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/PlanningTool.java:175-205': 1.3496617171995993, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/TerminableTool.java:0-30': 1.587732881718793, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/innerStorage/InnerStorageTool.java:0-30': 4.033755775300269, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/innerStorage/SmartContentSavingService.java:0-30': 1.5346539525074083, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/innerStorage/InnerStorageContentTool.java:0-30': 3.9009579359946454, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/textOperator/TextFileService.java:0-30': 1.4429973314407667, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/textOperator/TextFileService.java:25-55': 1.4946794771655387, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/textOperator/TextFileService.java:50-80': 2.446732034231192, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/textOperator/TextFileService.java:100-130': 1.31111391621134, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/textOperator/TextFileOperator.java:0-30': 3.8434431915854126, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/textOperator/TextFileOperator.java:400-430': 1.8002428076383057, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/textOperator/TextFileOperator.java:450-480': 1.893040068168556, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/textOperator/TextFileOperator.java:575-605': 1.825296211129989, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/textOperator/FileState.java:0-30': 1.5932434174609875, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/code/CodeExecutionResult.java:0-30': 5.860802752928148, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/code/ExecuteCommandResult.java:0-30': 5.779485553040935, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/code/ExecuteCommandResult.java:25-55': 5.901436724445677, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/code/IpUtils.java:0-30': 4.636394010944299, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/code/LogIdGenerator.java:0-30': 4.271714585686365, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/code/LogIdGenerator.java:50-80': 1.2076394433123816, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/code/LogIdGenerator.java:75-105': 5.260558613690828, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/code/PythonExecute.java:0-30': 4.058878288354842, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/code/PythonExecute.java:25-55': 5.863895207020927, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/code/PythonExecute.java:50-80': 5.9243138825071515, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/code/PythonExecute.java:75-105': 4.548852455429811, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/code/PythonExecute.java:100-130': 5.103968123066489, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/code/PythonExecute.java:125-155': 5.5675905147381215, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/code/PythonExecute.java:200-230': 5.758098172579443, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/code/ToolExecuteResult.java:0-30': 4.5237534613628, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/code/CodeUtils.java:0-30': 4.136159153724666, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/code/CodeUtils.java:25-55': 5.510435677966226, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/code/CodeUtils.java:50-80': 5.748128458963221, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/code/CodeUtils.java:75-105': 5.386277192827254, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/code/CodeUtils.java:100-130': 5.2967690049695735, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/code/CodeUtils.java:125-155': 5.111692898922858, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/code/CodeUtils.java:150-180': 5.52978708495298, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/code/CodeUtils.java:175-205': 5.28041173060775, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/searchAPI/GoogleSearch.java:0-30': 3.6910667399150583, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/searchAPI/GoogleSearch.java:50-80': 1.4250789533148378, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/searchAPI/serpapi/SerpApiService.java:0-30': 1.3148693254696644, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/searchAPI/serpapi/SerpApiProperties.java:0-30': 1.4802207498974258, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/filesystem/UnifiedDirectoryManager.java:0-30': 2.067850372817686, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/filesystem/UnifiedDirectoryManager.java:25-55': 1.4898286183391802, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/filesystem/UnifiedDirectoryManager.java:50-80': 1.4850091437813338, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/filesystem/UnifiedDirectoryManager.java:225-255': 1.9744622807253107, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/filesystem/UnifiedDirectoryManager.java:250-280': 2.003502595550171, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/bash/Bash.java:0-30': 3.924448712718318, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/bash/Bash.java:50-80': 3.3892720238676044, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/bash/ShellCommandExecutor.java:0-30': 1.5714276365890276, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/bash/MacShellExecutor.java:0-30': 1.5607422602136187, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/bash/MacShellExecutor.java:175-205': 2.5811351192751717, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/bash/MacShellExecutor.java:200-230': 5.572632978942769, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/bash/WindowsShellExecutor.java:0-30': 1.5607422602136187, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/bash/WindowsShellExecutor.java:75-105': 1.539801608104721, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/bash/WindowsShellExecutor.java:100-130': 1.5094234550964287, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/bash/WindowsShellExecutor.java:125-155': 5.163679300420393, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/bash/WindowsShellExecutor.java:150-180': 5.041043171715009, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/bash/ShellExecutorFactory.java:0-30': 1.5244612091889669, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/bash/LinuxShellExecutor.java:0-30': 1.5607422602136187, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/bash/LinuxShellExecutor.java:125-155': 5.260185394346563, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/browser/BrowserUseTool.java:0-30': 1.122038729414997, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/browser/BrowserUseTool.java:25-55': 3.1397986630840338, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/browser/BrowserUseTool.java:175-205': 3.750590792999995, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/browser/BrowserUseTool.java:250-280': 2.5717894403001007, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/browser/BrowserUseTool.java:275-305': 2.7606292505729213, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/browser/BrowserUseTool.java:300-330': 1.3738979381611398, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/browser/BrowserUseTool.java:375-405': 1.4850091437813338, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/browser/BrowserUseTool.java:400-430': 2.0090266618268062, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/browser/BrowserUseTool.java:475-505': 1.7184952272002534, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/browser/BrowserUseTool.java:500-530': 1.2270067732655228, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/browser/BrowserUseTool.java:525-555': 2.2953175978159734, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/browser/InteractiveElementRegistry.java:0-30': 1.4995620278218964, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/browser/InteractiveElementRegistry.java:25-55': 2.8417445983796066, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/browser/InteractiveElementRegistry.java:75-105': 1.587732881718793, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/browser/InteractiveElementRegistry.java:100-130': 1.8279854392172998, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/browser/InteractiveElementRegistry.java:200-230': 1.65648406863643, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/browser/InteractiveElementRegistry.java:250-280': 1.6329148574925245, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/browser/InteractiveElementRegistry.java:275-305': 1.4898286183391802, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/browser/ChromeDriverService.java:0-30': 1.3417718832367183, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/browser/DriverWrapper.java:0-30': 1.3657230586340428, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/browser/DriverWrapper.java:175-205': 1.7923002701749313, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/browser/WebElementWrapper.java:0-30': 2.4040212343890612, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/browser/WebElementWrapper.java:125-155': 1.8207351761199357, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/browser/InteractiveElement.java:0-30': 1.5194154424953983, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/browser/InteractiveElement.java:100-130': 1.7853297775722856, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/browser/InteractiveTextProcessor.java:0-30': 2.0914025469442454, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/browser/InteractiveTextProcessor.java:25-55': 2.125291522339446, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/browser/actions/MoveToAndClickAction.java:0-30': 4.967162001034845, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/browser/actions/MoveToAndClickAction.java:25-55': 2.6858146068742257, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/browser/actions/MoveToAndClickAction.java:50-80': 1.5194154424953983, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/browser/actions/MoveToAndClickAction.java:75-105': 2.7441268728358663, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/browser/actions/GetTextAction.java:0-30': 3.984432318996469, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/browser/actions/KeyEnterAction.java:0-30': 4.084315692107012, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/browser/actions/NavigateAction.java:0-30': 4.021310781213186, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/browser/actions/GetElementPositionByNameAction.java:0-30': 4.021310781213186, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/browser/actions/GetElementPositionByNameAction.java:25-55': 4.216439986955546, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/browser/actions/GetElementPositionByNameAction.java:50-80': 1.9691264049738522, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/browser/actions/GetElementPositionByNameAction.java:75-105': 2.0046400786841847, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/browser/actions/GetElementPositionByNameAction.java:100-130': 2.3112268476059525, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/browser/actions/GetElementPositionByNameAction.java:125-155': 1.7225578398328647, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/browser/actions/GetElementPositionByNameAction.java:150-180': 1.2782563680468169, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/browser/actions/GetHtmlAction.java:0-30': 4.071557260027324, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/browser/actions/InputTextAction.java:0-30': 3.984432318996469, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/browser/actions/InputTextAction.java:25-55': 1.390544851406706, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/browser/actions/InputTextAction.java:50-80': 1.4384756257963185, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/browser/actions/InputTextAction.java:75-105': 2.039100362400001, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/browser/actions/ExecuteJsAction.java:0-30': 4.097154333921666, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/browser/actions/ExecuteJsAction.java:25-55': 3.402931460859665, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/browser/actions/CloseTabAction.java:0-30': 3.984432318996469, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/browser/actions/ClickByElementAction.java:0-30': 3.809741270310325, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/browser/actions/ClickByElementAction.java:25-55': 1.3863454179444579, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/browser/actions/ClickByElementAction.java:50-80': 2.2123530160457276, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/browser/actions/ScrollAction.java:0-30': 4.021310781213186, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/browser/actions/BrowserRequestVO.java:0-30': 2.0539719812595294, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/browser/actions/BrowserRequestVO.java:25-55': 5.618322324524328, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/browser/actions/BrowserRequestVO.java:50-80': 2.3602248319108736, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/browser/actions/BrowserRequestVO.java:75-105': 1.9277802266559592, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/browser/actions/RefreshAction.java:0-30': 4.097154333921666, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/browser/actions/ScreenShotAction.java:0-30': 4.071557260027324, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/browser/actions/BrowserAction.java:0-30': 3.94822411678681, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/browser/actions/BrowserAction.java:25-55': 2.5120236472675495, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/browser/actions/BrowserAction.java:100-130': 1.7554943441733541, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/browser/actions/BrowserAction.java:125-155': 2.1356072277464047, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/browser/actions/BrowserAction.java:150-180': 2.0528391903777012, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/browser/actions/SwitchTabAction.java:0-30': 3.984432318996469, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/browser/actions/NewTabAction.java:0-30': 4.071557260027324, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/mapreduce/MapReduceSharedStateManager.java:0-30': 1.5346539525074083, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/mapreduce/MapReduceSharedStateManager.java:150-180': 1.6043800433693445, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/mapreduce/MapReduceTool.java:0-30': 3.8209093801456966, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/mapreduce/MapReduceTool.java:25-55': 3.680643730360892, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/mapreduce/MapReduceTool.java:75-105': 2.4569235998635683, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/mapreduce/MapReduceTool.java:100-130': 2.5738656573192134, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/mapreduce/MapReduceTool.java:250-280': 5.406106908110421, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/mapreduce/MapReduceTool.java:575-605': 1.3262657509484723, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/mapreduce/MapReduceTool.java:600-630': 1.394769803506785, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/mapreduce/MapReduceTool.java:650-680': 1.9829913947974505, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/mapreduce/MapReduceTool.java:750-780': 1.5660667218567792, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/mapreduce/MapReduceTool.java:775-805': 1.8073304681325844, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/mapreduce/MapReduceTool.java:950-980': 1.4802207498974258, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/mapreduce/MapReduceTool.java:1125-1155': 2.2958150794856977, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/mapreduce/MapReduceTool.java:1150-1180': 2.026766665296864, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/mapreduce/MapReduceTool.java:1175-1205': 1.307379897570568, 'spring-ai-alibaba-jmanus/src/main/java/com/alibaba/cloud/ai/example/manus/tool/mapreduce/MapReduceTool.java:1200-1230': 1.3697983017004274, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/advisor/RetrievalRerankAdvisorTests.java:0-30': 1.3186463096802645, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/advisor/RetrievalRerankAdvisorTests.java:25-55': 2.7341708737462205, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/advisor/RetrievalRerankAdvisorTests.java:100-130': 5.495940876478198, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/advisor/DocumentRetrievalAdvisorTests.java:0-30': 1.307379897570568, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/advisor/DocumentRetrievalAdvisorTests.java:25-55': 4.007312602295719, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/advisor/DocumentRetrievalAdvisorTests.java:50-80': 7.880605941076722, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/advisor/DocumentRetrievalAdvisorTests.java:100-130': 5.623521590375723, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/advisor/DocumentRetrievalAdvisorTests.java:125-155': 5.610376282861103, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/advisor/DocumentRetrievalAdvisorTests.java:200-230': 5.590773148658851, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/advisor/DocumentRetrievalAdvisorTests.java:250-280': 5.564847743864093, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/transformer/splitter/SentenceSplitterTests.java:0-30': 2.0772072989769175, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/transformer/splitter/SentenceSplitterTests.java:25-55': 1.6624830670303476, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/transformer/splitter/SentenceSplitterTests.java:50-80': 1.4707360092264137, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/transformer/splitter/SentenceSplitterTests.java:75-105': 2.0632034503977734, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/transformer/splitter/SentenceSplitterTests.java:100-130': 1.2890243568454003, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/document/DocumentWithScoreTests.java:0-30': 1.555453881109404, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/document/DocumentWithScoreTests.java:25-55': 2.0493871560742165, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/document/DocumentWithScoreTests.java:50-80': 5.783722637843548, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/document/DocumentWithScoreTests.java:75-105': 4.5968550546595734, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/document/DocumentWithScoreTests.java:100-130': 5.780191619420252, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/document/DocumentWithScoreTests.java:125-155': 2.0866492893536632, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/document/JsonDocumentParserTests.java:0-30': 1.4898286183391802, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/document/JsonDocumentParserTests.java:25-55': 2.278230591642821, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/document/TextDocumentParserTests.java:0-30': 1.4946794771655387, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/model/RerankResponseMetadataTests.java:0-30': 2.067850372817686, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/model/RerankResponseMetadataTests.java:25-55': 1.6100069440831988, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/model/RerankResponseMetadataTests.java:50-80': 2.3112268476059525, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/model/RerankResponseMetadataTests.java:75-105': 2.593584664922611, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/model/RerankResponseTests.java:0-30': 1.5094234550964287, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/model/RerankResponseTests.java:25-55': 1.5194154424953983, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/model/RerankResponseTests.java:50-80': 2.276837018004281, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/model/RerankResponseTests.java:75-105': 1.461372044515705, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/model/RerankResultMetadataTests.java:0-30': 2.1501782175023574, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/model/RerankRequestTests.java:0-30': 2.0914025469442454, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/model/RerankRequestTests.java:25-55': 1.6156734531778587, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/model/RerankRequestTests.java:50-80': 2.5137217075165728, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/model/RerankRequestTests.java:75-105': 1.9048226335943024, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/evaluation/AnswerCorrectnessEvaluatorTests.java:0-30': 1.4429973314407667, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/evaluation/AnswerCorrectnessEvaluatorTests.java:25-55': 10.211641224098475, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/evaluation/AnswerCorrectnessEvaluatorTests.java:50-80': 6.991611855498702, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/evaluation/AnswerCorrectnessEvaluatorTests.java:100-130': 2.120383154394645, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/evaluation/AnswerCorrectnessEvaluatorTests.java:125-155': 2.155225660967689, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/evaluation/AnswerFaithfulnessEvaluatorTests.java:0-30': 1.4429973314407667, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/evaluation/AnswerFaithfulnessEvaluatorTests.java:25-55': 2.5619577475009625, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/evaluation/AnswerFaithfulnessEvaluatorTests.java:50-80': 1.4076001190620582, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/evaluation/AnswerFaithfulnessEvaluatorTests.java:100-130': 1.4076001190620582, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/evaluation/AnswerFaithfulnessEvaluatorTests.java:125-155': 2.3151121749054853, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/evaluation/AnswerFaithfulnessEvaluatorTests.java:150-180': 2.067850372817686, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/evaluation/AnswerFaithfulnessEvaluatorTests.java:175-205': 1.3990205075566946, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/evaluation/AnswerRelevancyEvaluatorTests.java:0-30': 1.4429973314407667, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/evaluation/AnswerRelevancyEvaluatorTests.java:25-55': 2.0539719812595294, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/evaluation/AnswerRelevancyEvaluatorTests.java:50-80': 1.3863454179444579, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/evaluation/AnswerRelevancyEvaluatorTests.java:100-130': 2.51631677511543, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/evaluation/AnswerRelevancyEvaluatorTests.java:125-155': 1.390544851406706, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/evaluation/AnswerRelevancyEvaluatorTests.java:150-180': 1.924789599043932, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/evaluation/AnswerRelevancyEvaluatorTests.java:175-205': 2.0090266618268062, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/evaluation/AnswerRelevancyEvaluatorTests.java:200-230': 1.3657230586340428, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/chat/DashScopeChatModelTests.java:0-30': 5.370142688165851, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/chat/DashScopeChatModelTests.java:25-55': 4.746513403705746, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/chat/DashScopeChatModelTests.java:50-80': 3.7591299335832646, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/chat/DashScopeChatModelTests.java:75-105': 5.359401305403674, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/chat/DashScopeChatModelTests.java:100-130': 4.715403581484187, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/chat/DashScopeChatModelTests.java:125-155': 4.928225948403371, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/chat/DashScopeChatModelTests.java:150-180': 4.2300576116792685, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/chat/DashScopeChatModelTests.java:175-205': 4.389602912720025, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/chat/DashScopeChatModelTests.java:200-230': 4.584381169003541, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/chat/DashScopeChatModelTests.java:225-255': 2.910977435025154, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/chat/DashScopeChatModelTests.java:250-280': 4.870732627568632, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/chat/DashScopeChatModelTests.java:275-305': 3.8643163940691183, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/chat/DashScopeChatModelTests.java:300-330': 4.36236355003082, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/chat/DashScopeChatModelTests.java:325-355': 3.917877427004722, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/chat/DashScopeChatModelTests.java:350-380': 4.591189975474088, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/chat/DashScopeChatModelTests.java:375-405': 4.407671301346782, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/chat/DashScopeChatModelTests.java:400-430': 5.426275287448578, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/chat/DashScopeChatModelTests.java:425-455': 1.854796333956173, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/chat/DashScopeChatModelTests.java:450-480': 1.4946794771655387, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/chat/DashScopeMultiModalChatTests.java:0-30': 5.2851396518524645, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/chat/DashScopeMultiModalChatTests.java:25-55': 4.0557411788799245, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/chat/DashScopeMultiModalChatTests.java:75-105': 1.9288333297066318, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/chat/DashScopeMultiModalChatTests.java:100-130': 5.698639325476593, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/chat/DashScopeMultiModalChatTests.java:125-155': 4.6973840650998255, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/chat/DashScopeMultiModalChatTests.java:150-180': 5.585878833946919, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/chat/DashScopeMultiModalChatTests.java:175-205': 5.743824044541935, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/chat/DashScopeMultiModalChatTests.java:200-230': 4.7601041670551325, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/chat/DashScopeMultiModalChatTests.java:225-255': 5.568608598625571, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/chat/DashScopeMultiModalChatTests.java:250-280': 1.3224450553019844, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/chat/DashScopeMultiModalChatTests.java:275-305': 1.2572512301815688, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/chat/DashScopeMultiModalChatTests.java:300-330': 1.191960976798107, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/chat/DashScopeMultiModalChatTests.java:325-355': 1.2641758158808274, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/chat/DashScopeMultiModalChatTests.java:350-380': 1.2854149260948562, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/chat/DashScopeMultiModalChatTests.java:375-405': 1.2436271712225597, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/chat/DashScopeMultiModalChatTests.java:425-455': 1.8623209615718375, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/chat/DashScopeChatIT.java:0-30': 4.35136516744078, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/chat/DashScopeChatIT.java:25-55': 3.2529769695004, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/chat/DashScopeChatIT.java:75-105': 2.9169325453263277, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/chat/DashScopeChatIT.java:100-130': 6.26922478580262, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/chat/DashScopeChatOptionsTests.java:0-30': 1.4754631369960434, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/chat/DashScopeChatOptionsTests.java:50-80': 1.8325829169733974, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/chat/DashScopeChatOptionsTests.java:75-105': 1.77930948892196, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/chat/DashScopeChatOptionsTests.java:125-155': 1.893040068168556, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/chat/DashScopeChatOptionsTests.java:175-205': 7.034432597417877, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/chat/DashScopeChatOptionsTests.java:200-230': 5.013059467541663, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/chat/observation/DashScopeChatModelObservationConventionTests.java:0-30': 1.4076001190620582, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/chat/observation/DashScopeChatModelObservationConventionTests.java:25-55': 5.007085832038423, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/embedding/DashScopeEmbeddingOptionsTests.java:0-30': 2.0866492893536632, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/embedding/DashScopeEmbeddingOptionsTests.java:25-55': 2.387351539087376, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/embedding/DashScopeEmbeddingOptionsTests.java:50-80': 1.8623209615718375, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/embedding/DashScopeEmbeddingModelTests.java:0-30': 1.2890243568454003, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/embedding/DashScopeEmbeddingModelTests.java:25-55': 1.4850091437813338, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/embedding/DashScopeEmbeddingModelTests.java:50-80': 1.4567346345301488, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/embedding/DashScopeEmbeddingModelTests.java:125-155': 2.344614356902089, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/agent/DashScopeAgentOptionsTests.java:0-30': 1.461372044515705, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/agent/DashScopeAgentOptionsTests.java:25-55': 1.5346539525074083, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/agent/DashScopeAgentTests.java:0-30': 1.2607040145492494, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/agent/DashScopeAgentTests.java:50-80': 1.4660390745170708, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/agent/DashScopeAgentTests.java:125-155': 1.5094234550964287, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/agent/DashScopeAgentTests.java:150-180': 1.394769803506785, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/agent/DashScopeAgentTests.java:175-205': 1.4339821696206667, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/protocol/DashScopeWebSocketClientOptionsTests.java:0-30': 2.1009743252108466, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/protocol/DashScopeWebSocketClientTests.java:0-30': 1.461372044515705, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/protocol/DashScopeWebSocketClientTests.java:25-55': 1.5144029672636237, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/protocol/DashScopeWebSocketClientTests.java:175-205': 4.116275948129087, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/rag/DashScopeDocumentTransformerOptionsTests.java:0-30': 2.1057931452632648, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/rag/DashScopeDocumentTransformerOptionsTests.java:75-105': 2.3886559126509526, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/rag/DashScopeCloudStoreTests.java:0-30': 1.4206686751462156, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/rag/DashScopeDocumentTransformerTests.java:0-30': 1.4295166990043104, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/rag/DashScopeDocumentTransformerTests.java:25-55': 1.5044765819016563, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/rag/DashScopeDocumentTransformerTests.java:125-155': 1.182747793947089, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/rag/DashScopeDocumentCloudReaderTests.java:0-30': 1.4475475537922684, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/rag/DashScopeDocumentCloudReaderTests.java:25-55': 1.539801608104721, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/rag/DashScopeDocumentCloudReaderTests.java:50-80': 1.587732881718793, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/rag/DashScopeDocumentCloudReaderTests.java:75-105': 1.3339737577322137, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/rag/DashScopeDocumentRetrievalAdvisorTests.java:0-30': 5.958800242926026, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/rag/DashScopeDocumentRetrievalAdvisorTests.java:25-55': 3.967163001334268, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/rag/DashScopeDocumentRetrievalAdvisorTests.java:50-80': 5.01223061693222, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/rag/DashScopeDocumentRetrievalAdvisorTests.java:75-105': 2.1009743252108466, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/rag/DashScopeDocumentRetrievalAdvisorTests.java:100-130': 9.85091281710332, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/rag/DashScopeDocumentRetrievalAdvisorTests.java:150-180': 9.730321102255314, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/rag/DashScopeDocumentRetrievalAdvisorTests.java:175-205': 8.860662072411415, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/rag/DashScopeDocumentRetrievalAdvisorTests.java:200-230': 5.495833104361528, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/rag/DashScopeDocumentRetrievalAdvisorTests.java:225-255': 9.925194392573111, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/rag/DashScopeDocumentRetrievalAdvisorTests.java:250-280': 9.863190558816937, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/rag/DashScopeDocumentRetrievalAdvisorTests.java:275-305': 5.316827747759985, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/rag/DashScopeDocumentRetrievalAdvisorTests.java:300-330': 9.925194392573111, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/rag/DashScopeStoreOptionsTests.java:0-30': 2.115497405994602, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/rag/DashScopeStoreOptionsTests.java:50-80': 2.7503196223892896, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/rag/DashScopeStoreOptionsTests.java:75-105': 1.3186463096802645, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/rag/DashScopeStoreOptionsTests.java:100-130': 1.2747069058480733, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/rag/DashScopeStoreOptionsTests.java:125-155': 1.2676667917202797, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/rag/DashScopeDocumentRetrieverTests.java:0-30': 1.3990205075566946, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/rag/DashScopeDocumentRetrieverTests.java:25-55': 1.5144029672636237, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/rag/DashScopeDocumentRetrieverOptionsTests.java:0-30': 2.120383154394645, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/rag/DashScopeDocumentRetrieverOptionsTests.java:25-55': 1.4475475537922684, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/rag/DashScopeDocumentRetrieverOptionsTests.java:75-105': 1.6754069765960544, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/rag/DashScopeDocumentCloudReaderOptionsTests.java:0-30': 2.115497405994602, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/image/DashScopeImageModelTests.java:0-30': 1.1472766021153649, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/image/DashScopeImageModelTests.java:25-55': 4.66613532954322, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/image/DashScopeImageModelTests.java:50-80': 6.146971912966391, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/image/DashScopeImageModelTests.java:75-105': 5.730944005563573, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/image/DashScopeImageModelTests.java:100-130': 4.9076789385183295, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/image/DashScopeImageModelTests.java:125-155': 4.542230559473646, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/image/DashScopeImageModelTests.java:150-180': 7.58320062546963, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/image/DashScopeImageModelTests.java:175-205': 2.961929330334095, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/image/DashScopeImageOptionsTests.java:0-30': 2.115497405994602, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/image/DashScopeImageOptionsTests.java:50-80': 1.7065487452037036, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/image/DashScopeImageOptionsTests.java:75-105': 1.6877262922473488, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/image/DashScopeImageOptionsTests.java:125-155': 1.4802207498974258, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/image/observation/DashScopeImageModelObservationITests.java:0-30': 1.1359209911700505, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/image/observation/DashScopeImageModelObservationITests.java:25-55': 3.782575595458065, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/image/observation/DashScopeImageModelObservationITests.java:75-105': 9.563807673800756, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/image/observation/DashScopeImageModelObservationITests.java:100-130': 7.0769997781260425, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/common/RequestIdGeneratorTests.java:0-30': 4.620556737207821, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/common/RequestIdGeneratorTests.java:25-55': 3.676530692580515, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/common/RequestIdGeneratorTests.java:50-80': 3.6074558932079857, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/common/RequestIdGeneratorTests.java:75-105': 4.248028124264188, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/api/DashScopeApiUploadFileTests.java:0-30': 1.4707360092264137, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/api/DashScopeImageApiTests.java:0-30': 1.4660390745170708, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/api/DashScopeImageApiTests.java:50-80': 2.7017588861717816, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/api/DashScopeAiStreamFunctionCallingHelperTests.java:0-30': 5.519535708802024, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/api/DashScopeAiStreamFunctionCallingHelperTests.java:25-55': 2.910977435025154, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/api/DashScopeAiStreamFunctionCallingHelperTests.java:50-80': 4.098628326032619, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/api/DashScopeAiStreamFunctionCallingHelperTests.java:75-105': 4.854556829061183, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/api/DashScopeAiStreamFunctionCallingHelperTests.java:100-130': 3.834804721920537, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/api/DashScopeAiStreamFunctionCallingHelperTests.java:125-155': 4.681519986413668, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/api/DashScopeAiStreamFunctionCallingHelperTests.java:150-180': 3.960097779413393, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/api/DashScopeAiStreamFunctionCallingHelperTests.java:175-205': 4.364671289599222, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/api/DashScopeAiStreamFunctionCallingHelperTests.java:200-230': 5.502796802646314, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/api/DashScopeAiStreamFunctionCallingHelperTests.java:225-255': 5.719259550658212, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/api/DashScopeAiStreamFunctionCallingHelperTests.java:250-280': 4.404155022470401, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/api/DashScopeResponseFormatTests.java:0-30': 1.5932434174609875, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/api/DashScopeResponseFormatTests.java:50-80': 5.03255130683139, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/api/DashScopeResponseFormatTests.java:75-105': 4.8691424015921445, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/api/ApiUtilsTests.java:0-30': 1.4898286183391802, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/api/DashScopeApiTests.java:0-30': 1.3780221876928578, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/api/DashScopeAudioTranscriptionApiTests.java:0-30': 1.4162856102690216, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/api/DashScopeAudioTranscriptionApiTests.java:50-80': 2.6243960441808696, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/api/DashScopeAudioTranscriptionApiTests.java:75-105': 2.728523270149891, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/api/DashScopeSpeechSynthesisApiTests.java:0-30': 1.461372044515705, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/api/DashScopeSpeechSynthesisApiTests.java:25-55': 2.2730789946949215, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/api/DashScopeSpeechSynthesisApiTests.java:50-80': 2.5235328881189565, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/api/DashScopeAgentApiTests.java:0-30': 1.3657230586340428, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/api/DashScopeAgentApiTests.java:25-55': 1.390544851406706, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/api/DashScopeAgentApiTests.java:100-130': 2.1823445925661273, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/api/DashScopeAgentApiTests.java:125-155': 2.5738656573192134, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/api/DashScopeAgentApiTests.java:150-180': 2.334736417217366, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/api/DashScopeAgentApiTests.java:175-205': 1.4076001190620582, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/api/DashScopeAgentApiTests.java:200-230': 1.3186463096802645, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/api/DashScopeAgentApiTests.java:225-255': 3.680643730360892, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/api/DashScopeAgentApiTests.java:250-280': 2.446516666690098, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/api/DashScopeAgentApiTests.java:275-305': 1.5244612091889669, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/rerank/DashScopeRerankModelTests.java:0-30': 1.2436271712225597, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/rerank/DashScopeRerankModelTests.java:25-55': 1.4754631369960434, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/rerank/DashScopeRerankModelTests.java:125-155': 1.2237358609737488, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/rerank/DashScopeRerankOptionsTests.java:0-30': 2.130222667276, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/rerank/DashScopeRerankOptionsTests.java:25-55': 1.452126563470489, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/rerank/DashScopeRerankOptionsTests.java:50-80': 2.5232323264412573, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/metadata/DashScopeAiUsageTests.java:0-30': 2.058577366497676, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/metadata/DashScopeAiUsageTests.java:25-55': 2.2379232895537484, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/metadata/DashScopeAiUsageTests.java:50-80': 6.202637352291081, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/metadata/audio/DashScopeAudioTranscriptionResponseMetadataTests.java:0-30': 2.026766665296864, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/metadata/audio/DashScopeAudioTranscriptionResponseMetadataTests.java:25-55': 1.4206686751462156, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/metadata/audio/DashScopeAudioTranscriptionResponseMetadataTests.java:75-105': 1.2999753052429273, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/metadata/audio/DashScopeAudioSpeechResponseMetadataTests.java:0-30': 1.9872836441506343, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/metadata/audio/DashScopeAudioSpeechResponseMetadataTests.java:25-55': 1.4206686751462156, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/metadata/audio/DashScopeAudioSpeechResponseMetadataTests.java:75-105': 1.2270067732655228, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/dashscope/metadata/audio/DashScopeAudioSpeechResponseMetadataTests.java:100-130': 1.4995620278218964, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/tool/MockWeatherService.java:0-30': 1.390544851406706, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/tool/MockWeatherService.java:50-80': 1.6505282088323439, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/tool/MockWeatherService.java:75-105': 1.731458800865721, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/tool/observation/ArmsToolCallingObservationIT.java:0-30': 6.37813307226756, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/tool/observation/ArmsToolCallingObservationIT.java:25-55': 2.5123615082633224, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/tool/observation/ArmsToolCallingObservationIT.java:200-230': 4.460789850547407, 'spring-ai-alibaba-core/src/test/java/com/alibaba/cloud/ai/tool/observation/ArmsToolCallingObservationIT.java:225-255': 4.744131278893166, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/advisor/RetrievalRerankAdvisor.java:0-30': 1.4995620278218964, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/advisor/RetrievalRerankAdvisor.java:50-80': 2.1501782175023574, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/advisor/DocumentRetrievalAdvisor.java:0-30': 1.3821712726179565, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/advisor/DocumentRetrievalAdvisor.java:25-55': 2.1756546538314656, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/transformer/splitter/SentenceSplitter.java:0-30': 1.4802207498974258, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/agent/Agent.java:0-30': 1.6446150239729562, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/document/DocumentParser.java:0-30': 1.712085963376952, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/document/DocumentWithScore.java:0-30': 1.587732881718793, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/document/DocumentWithScore.java:100-130': 3.2710996706369686, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/document/JsonDocumentParser.java:0-30': 1.4475475537922684, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/document/TextDocumentParser.java:0-30': 1.6156734531778587, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/model/SpringAIAlibabaModels.java:0-30': 1.65648406863643, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/model/RerankModel.java:0-30': 2.186015073891746, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/model/RerankRequest.java:0-30': 1.6869199579366625, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/model/RerankOptions.java:0-30': 1.65648406863643, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/model/RerankResponse.java:0-30': 1.6271269812152453, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/model/RerankResponseMetadata.java:0-30': 1.582260333065363, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/model/RerankResultMetadata.java:0-30': 1.6685256744000492, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/model/SpringAIAlibabaModelProperties.java:0-30': 1.6271269812152453, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/evaluation/AnswerCorrectnessEvaluator.java:0-30': 1.5714276365890276, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/evaluation/LaajEvaluator.java:0-30': 1.5502012189992864, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/evaluation/AnswerRelevancyEvaluator.java:0-30': 1.5044765819016563, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/evaluation/AnswerFaithfulnessEvaluator.java:0-30': 1.5044765819016563, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/chat/MessageFormat.java:0-30': 1.7380142090139563, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/chat/DashScopeChatOptions.java:0-30': 1.3657230586340428, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/chat/DashScopeChatOptions.java:25-55': 4.681528675637577, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/chat/DashScopeChatOptions.java:50-80': 7.266732800235015, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/chat/DashScopeChatOptions.java:75-105': 7.632886162405645, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/chat/DashScopeChatOptions.java:100-130': 7.787778129115279, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/chat/DashScopeChatOptions.java:125-155': 5.822794939547386, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/chat/DashScopeChatOptions.java:150-180': 2.031023532524664, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/chat/DashScopeChatOptions.java:175-205': 4.239088699245535, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/chat/DashScopeChatOptions.java:625-655': 2.1532791445206025, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/chat/DashScopeChatOptions.java:650-680': 3.2838214095309017, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/chat/DashScopeChatModel.java:0-30': 5.550135696805258, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/chat/DashScopeChatModel.java:25-55': 9.54489010418803, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/chat/DashScopeChatModel.java:50-80': 3.287638331014354, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/chat/DashScopeChatModel.java:75-105': 4.977249209830301, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/chat/DashScopeChatModel.java:100-130': 2.4569235998635683, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/chat/DashScopeChatModel.java:150-180': 1.8740107926745204, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/chat/DashScopeChatModel.java:175-205': 7.640747079033532, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/chat/DashScopeChatModel.java:200-230': 1.3863454179444579, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/chat/DashScopeChatModel.java:225-255': 8.61620793220942, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/chat/DashScopeChatModel.java:250-280': 4.101226700758587, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/chat/DashScopeChatModel.java:275-305': 4.496773049233023, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/chat/DashScopeChatModel.java:300-330': 8.642737994194379, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/chat/DashScopeChatModel.java:325-355': 7.946189445184245, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/chat/DashScopeChatModel.java:350-380': 9.440273419851225, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/chat/DashScopeChatModel.java:375-405': 1.2436271712225597, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/chat/DashScopeChatModel.java:425-455': 3.9511946195120937, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/chat/DashScopeChatModel.java:450-480': 3.87283191396943, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/chat/DashScopeChatModel.java:475-505': 3.2452743472534906, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/chat/DashScopeChatModel.java:550-580': 3.285722706467054, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/chat/observation/DashScopeChatModelObservationConvention.java:0-30': 5.275944194972263, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/chat/observation/DashScopeChatModelObservationConvention.java:25-55': 5.245559990820889, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/chat/observation/DashScopeChatModelObservationConvention.java:50-80': 8.405555765743602, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/chat/observation/package-info.java:0-30': 1.6869199579366625, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/embedding/DashScopeEmbeddingOptions.java:0-30': 1.544983913259478, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/embedding/DashScopeEmbeddingModel.java:0-30': 1.4339821696206667, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/embedding/DashScopeEmbeddingModel.java:25-55': 3.6506738932737735, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/embedding/DashScopeEmbeddingModel.java:100-130': 1.188874008839169, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/embedding/DashScopeEmbeddingModel.java:125-155': 5.243687928423426, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/embedding/DashScopeEmbeddingModel.java:150-180': 3.8120575639215595, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/embedding/DashScopeEmbeddingModel.java:175-205': 1.8206238737843274, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/agent/DashScopeAgent.java:0-30': 3.5028459605858036, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/agent/DashScopeAgent.java:25-55': 4.324570288352781, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/agent/DashScopeAgent.java:150-180': 6.118653692323445, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/agent/DashScopeAgentRagOptions.java:0-30': 1.5660667218567792, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/agent/DashScopeAgentOptions.java:0-30': 1.6043800433693445, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/agent/DashScopeAgentFlowStreamMode.java:0-30': 1.576825380051758, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/protocol/DashScopeWebSocketClientOptions.java:0-30': 1.5660667218567792, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/protocol/DashScopeWebSocketClient.java:0-30': 1.3990205075566946, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/protocol/DashScopeWebSocketClient.java:150-180': 5.386277192827254, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/protocol/DashScopeWebSocketClient.java:175-205': 4.522982997988956, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/protocol/DashScopeWebSocketClient.java:300-330': 3.982883508195211, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/rag/DashScopeDocumentTransformer.java:0-30': 4.058878288354842, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/rag/DashScopeDocumentTransformer.java:75-105': 3.711518350541833, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/rag/DashScopeDocumentRetriever.java:0-30': 1.5346539525074083, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/rag/DashScopeDocumentRetrieverOptions.java:0-30': 1.5660667218567792, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/rag/DashScopeDocumentRetrievalAdvisor.java:0-30': 3.563177886304448, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/rag/DashScopeDocumentRetrievalAdvisor.java:25-55': 6.0421332777199055, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/rag/DashScopeDocumentRetrievalAdvisor.java:150-180': 3.446824327177951, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/rag/DashScopeStoreOptions.java:0-30': 1.4995620278218964, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/rag/DashScopeDocumentCloudReader.java:0-30': 3.94822411678681, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/rag/DashScopeDocumentCloudReader.java:25-55': 2.534645878649833, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/rag/DashScopeDocumentCloudReader.java:75-105': 4.002582128729521, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/rag/DashScopeCloudStore.java:0-30': 1.4475475537922684, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/rag/DashScopeDocumentCloudReaderOptions.java:0-30': 1.6043800433693445, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/rag/DashScopeDocumentTransformerOptions.java:0-30': 1.5295406001183258, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/image/DashScopeImageModel.java:0-30': 4.135779519117024, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/image/DashScopeImageModel.java:25-55': 6.421800321736432, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/image/DashScopeImageModel.java:50-80': 3.4043711802850622, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/image/DashScopeImageModel.java:150-180': 6.94499932104874, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/image/DashScopeImageModel.java:175-205': 2.9080967501634416, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/image/DashScopeImageModel.java:225-255': 3.6365620282606477, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/image/DashScopeImageModel.java:250-280': 4.075447684927768, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/image/DashScopeImageModel.java:275-305': 4.758778404496858, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/image/DashScopeImageOptions.java:0-30': 1.6271269812152453, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/image/DashScopeImageOptions.java:25-55': 5.696283313564711, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/image/DashScopeImageOptions.java:50-80': 5.836858945934454, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/image/DashScopeImageOptions.java:75-105': 1.7784132932580297, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/image/observation/DashScopeImageModelObservationConvention.java:0-30': 1.31111391621134, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/image/observation/DashScopeImageModelObservationConvention.java:25-55': 2.408225095645459, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/image/observation/DashScopeImagePromptContentObservationHandler.java:0-30': 1.3863454179444579, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/common/ErrorCodeEnum.java:0-30': 4.033755775300269, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/common/ErrorCodeEnum.java:25-55': 5.366714334934096, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/common/IdGenerator.java:0-30': 1.7647398580192688, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/common/DashScopeApiConstants.java:0-30': 1.539801608104721, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/common/DashScopeApiConstants.java:50-80': 5.296049109645082, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/common/RequestIdGenerator.java:0-30': 1.65648406863643, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/common/DashScopeException.java:0-30': 1.6271269812152453, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/common/DashScopeException.java:25-55': 5.117424113155469, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/audio/DashScopeAudioSpeechOptions.java:0-30': 1.461372044515705, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/audio/DashScopeAudioSpeechOptions.java:75-105': 1.638744057033641, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/audio/DashScopeAudioTranscriptionModel.java:0-30': 1.2538173069550065, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/audio/DashScopeAudioTranscriptionModel.java:200-230': 3.983697875463445, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/audio/DashScopeAudioTranscriptionModel.java:225-255': 4.809055810535696, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/audio/DashScopeAudioSpeechModel.java:0-30': 1.2782563680468169, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/audio/DashScopeAudioTranscriptionOptions.java:0-30': 1.587732881718793, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/audio/transcription/AudioTranscriptionModel.java:0-30': 1.4995620278218964, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/audio/synthesis/SpeechSynthesisResult.java:0-30': 1.5295406001183258, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/audio/synthesis/SpeechSynthesisOptions.java:0-30': 1.6505282088323439, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/audio/synthesis/SpeechSynthesisResponseMetadata.java:0-30': 1.4384756257963185, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/audio/synthesis/SpeechSynthesisOutput.java:0-30': 1.6329148574925245, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/audio/synthesis/SpeechSynthesisPrompt.java:0-30': 1.5502012189992864, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/audio/synthesis/SpeechSynthesisModel.java:0-30': 1.5346539525074083, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/audio/synthesis/SpeechSynthesisResponse.java:0-30': 1.576825380051758, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/audio/synthesis/SpeechSynthesisResponse.java:50-80': 3.750590792999995, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/audio/synthesis/SpeechSynthesisMessage.java:0-30': 1.6271269812152453, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/audio/synthesis/SpeechSynthesisMessage.java:50-80': 4.177370416971692, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/audio/synthesis/SpeechSynthesisResultMetadata.java:0-30': 1.5144029672636237, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/api/ApiUtils.java:0-30': 1.555453881109404, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/api/DashScopeAudioTranscriptionApi.java:0-30': 1.2747069058480733, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/api/DashScopeAudioTranscriptionApi.java:25-55': 1.3536415319003627, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/api/DashScopeAudioTranscriptionApi.java:225-255': 4.547341788678065, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/api/DashScopeResponseFormat.java:0-30': 1.452126563470489, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/api/DashScopeResponseFormat.java:75-105': 3.3622795054565056, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/api/DashScopeResponseFormat.java:100-130': 3.734004562449868, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/api/DashScopeAiStreamFunctionCallingHelper.java:0-30': 5.57420250014179, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/api/DashScopeAiStreamFunctionCallingHelper.java:25-55': 5.767277543004763, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/api/DashScopeAiStreamFunctionCallingHelper.java:50-80': 5.979445641239311, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/api/DashScopeAiStreamFunctionCallingHelper.java:75-105': 4.506161532214464, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/api/DashScopeAiStreamFunctionCallingHelper.java:125-155': 4.1323581737931585, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/api/DashScopeAiStreamFunctionCallingHelper.java:150-180': 4.750176049799807, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/api/DashScopeAiStreamFunctionCallingHelper.java:175-205': 6.395052724707257, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/api/DashScopeAiStreamFunctionCallingHelper.java:200-230': 4.941102225886529, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/api/DashScopeAudioSpeechApi.java:0-30': 1.3496617171995993, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/api/DashScopeApi.java:0-30': 3.5993320129563373, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/api/DashScopeApi.java:100-130': 2.350146107290704, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/api/DashScopeApi.java:175-205': 6.767820907994323, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/api/DashScopeApi.java:200-230': 6.533822353715026, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/api/DashScopeApi.java:225-255': 6.1135984229595906, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/api/DashScopeApi.java:325-355': 2.702496221332811, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/api/DashScopeApi.java:350-380': 3.6161461063144897, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/api/DashScopeApi.java:525-555': 3.1624293864175734, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/api/DashScopeApi.java:575-605': 3.0816364396861706, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/api/DashScopeApi.java:600-630': 4.314957231660678, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/api/DashScopeApi.java:650-680': 3.8042143687962344, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/api/DashScopeApi.java:675-705': 3.6395266877472263, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/api/DashScopeApi.java:700-730': 3.70337894027893, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/api/DashScopeApi.java:800-830': 4.8273854408060615, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/api/DashScopeApi.java:825-855': 2.8622633013362844, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/api/DashScopeApi.java:850-880': 2.952335544232879, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/api/DashScopeApi.java:925-955': 2.9783705466721755, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/api/DashScopeApi.java:950-980': 3.7095073737017907, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/api/DashScopeApi.java:975-1005': 2.886727361150984, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/api/DashScopeApi.java:1000-1030': 2.125291522339446, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/api/DashScopeApi.java:1025-1055': 2.574818752880137, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/api/DashScopeApi.java:1050-1080': 5.349494067852039, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/api/DashScopeApi.java:1075-1105': 6.086194032652308, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/api/DashScopeApi.java:1100-1130': 2.4665286976770133, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/api/DashScopeApi.java:1125-1155': 1.756922227347611, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/api/DashScopeApi.java:1150-1180': 5.251173942449708, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/api/DashScopeApi.java:1175-1205': 4.956172144239385, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/api/DashScopeApi.java:1200-1230': 2.3580291500730404, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/api/DashScopeApi.java:1225-1255': 5.409058433541043, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/api/DashScopeApi.java:1300-1330': 3.906990306404133, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/api/DashScopeApi.java:1325-1355': 4.018164421738391, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/api/DashScopeApi.java:1350-1380': 2.85621760082616, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/api/DashScopeApi.java:1375-1405': 4.7418046723436635, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/api/DashScopeApi.java:1400-1430': 3.9958199849949576, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/api/DashScopeApi.java:1425-1455': 2.3519415806773196, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/api/DashScopeApi.java:1450-1480': 4.3064009829768155, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/api/DashScopeApi.java:1475-1505': 4.3961681158819585, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/api/DashScopeApi.java:1500-1530': 1.4339821696206667, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/api/DashScopeApi.java:1525-1555': 1.5044765819016563, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/api/DashScopeApi.java:1550-1580': 10.309324085771701, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/api/DashScopeApi.java:1575-1605': 8.23287667035346, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/api/DashScopeApi.java:1600-1630': 4.639389531847411, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/api/DashScopeApi.java:1625-1655': 7.635881825686364, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/api/DashScopeApi.java:1650-1680': 8.96590011218402, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/api/DashScopeApi.java:1675-1705': 7.41693447740361, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/api/DashScopeApi.java:1775-1805': 4.076661575387917, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/api/DashScopeImageApi.java:0-30': 1.4162856102690216, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/api/DashScopeImageApi.java:125-155': 2.962694734054195, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/api/DashScopeImageApi.java:150-180': 3.882165413782098, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/api/DashScopeAgentApi.java:0-30': 1.307379897570568, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/api/DashScopeAgentApi.java:75-105': 2.92308145432499, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/api/DashScopeAgentApi.java:125-155': 4.140396888126947, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/rerank/DashScopeRerankOptions.java:0-30': 1.6271269812152453, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/rerank/DashScopeRerankModel.java:0-30': 1.394769803506785, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/rerank/DashScopeRerankModel.java:100-130': 1.1617944313296826, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/metadata/DashScopeImageGenMetadata.java:0-30': 6.208962830004177, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/metadata/DashScopeImageGenMetadata.java:25-55': 3.3552980225194133, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/metadata/DashScopeImageGenMetadata.java:50-80': 3.734004562449868, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/metadata/DashScopeAiUsage.java:0-30': 1.555453881109404, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/metadata/DashScopeAiUsage.java:25-55': 2.612120723914129, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/metadata/DashScopeAiUsage.java:50-80': 3.0284814283423276, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/metadata/audio/DashScopeAudioSpeechResponseMetadata.java:0-30': 1.5044765819016563, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/metadata/audio/DashScopeAudioTranscriptionMetadata.java:0-30': 1.5987923371942092, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/metadata/audio/DashScopeAudioTranscriptionResponseMetadata.java:0-30': 1.4567346345301488, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/dashscope/observation/conventions/AiProvider.java:0-30': 1.5714276365890276, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/aot/DashScopeAIRuntimeHints.java:0-30': 1.4946794771655387, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/tool/ObservableToolCallingManager.java:0-30': 5.133831974190652, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/tool/ObservableToolCallingManager.java:25-55': 2.5255800536841195, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/tool/ObservableToolCallingManager.java:100-130': 4.933796860815442, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/tool/ObservableToolCallingManager.java:125-155': 5.1357812244274745, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/tool/ObservableToolCallingManager.java:150-180': 1.214026927770339, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/tool/ObservableToolCallingManager.java:175-205': 1.2676667917202797, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/tool/ObservableToolCallingManager.java:200-230': 3.852166489065573, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/tool/observation/ArmsToolCallingObservationContext.java:0-30': 1.5295406001183258, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/tool/observation/ArmsToolCallingObservationConvention.java:0-30': 6.564206300501154, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/tool/observation/ArmsToolCallingObservationConvention.java:75-105': 2.3886559126509526, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/tool/observation/ArmsToolCallingObservationConvention.java:100-130': 2.4394292921683594, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/tool/observation/ArmsToolCallingObservationDocumentation.java:0-30': 7.895704346668676, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/tool/observation/ArmsToolCallingObservationDocumentation.java:75-105': 6.594834584188094, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/tool/observation/ArmsToolCallingObservationDocumentation.java:100-130': 6.647175438238448, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/tool/observation/ArmsToolCallingObservationDocumentation.java:125-155': 7.450444522738641, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/tool/observation/ArmsToolCallingObservationDocumentation.java:150-180': 7.494892921008127, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/tool/observation/ArmsToolCallingObservationDocumentation.java:175-205': 7.19952978211379, 'spring-ai-alibaba-core/src/main/java/com/alibaba/cloud/ai/tool/observation/package-info.java:0-30': 1.693141844245956, 'tools/scripts/new-line-check.py:0-30': 1.7579816852101768, 'tools/scripts/new-line-check.py:50-80': 2.155225660967689, 'tools/scripts/new-line-check.py:75-105': 2.971766417054207, 'spring-ai-alibaba-studio/ui/README.md:0-30': 7.361994601915077, 'spring-ai-alibaba-studio/ui/src/app.ts:0-30': 2.0725182748781217, 'spring-ai-alibaba-studio/ui/src/app.ts:25-55': 1.6271269812152453, 'spring-ai-alibaba-studio/ui/src/app.ts:50-80': 3.9804424028900343, 'spring-ai-alibaba-studio/ui/src/typings.d.ts:0-30': 2.0725182748781217, 'spring-ai-alibaba-studio/ui/src/typings.d.ts:25-55': 2.1846625150837045, 'spring-ai-alibaba-studio/ui/src/types/chat_model.ts:0-30': 2.0725182748781217, 'spring-ai-alibaba-studio/ui/src/types/chat_model.ts:25-55': 1.6624830670303476, 'spring-ai-alibaba-studio/ui/src/types/traces.ts:0-30': 2.0725182748781217, 'spring-ai-alibaba-studio/ui/src/types/traces.ts:25-55': 4.3018605400347125, 'spring-ai-alibaba-studio/ui/src/types/chat_clients.ts:0-30': 2.0725182748781217, 'spring-ai-alibaba-studio/ui/src/types/chat_clients.ts:25-55': 1.576825380051758, 'spring-ai-alibaba-studio/ui/src/types/menu.ts:0-30': 2.0725182748781217, 'spring-ai-alibaba-studio/ui/src/types/menu.ts:25-55': 2.1501782175023574, 'spring-ai-alibaba-studio/ui/src/types/options.ts:0-30': 2.0725182748781217, 'spring-ai-alibaba-studio/ui/src/types/options.ts:25-55': 1.6869199579366625, 'spring-ai-alibaba-studio/ui/src/mock/tracemock.ts:0-30': 2.0725182748781217, 'spring-ai-alibaba-studio/ui/src/mock/tracemock.ts:25-55': 2.1964746382642875, 'spring-ai-alibaba-studio/ui/src/mock/tracemock.ts:200-230': 5.517577404187734, 'spring-ai-alibaba-studio/ui/src/mock/tracemock.ts:225-255': 5.944643044291574, 'spring-ai-alibaba-studio/ui/src/mock/tracemock.ts:350-380': 4.063611468780057, 'spring-ai-alibaba-studio/ui/src/mock/tracemock.ts:375-405': 1.970225178300757, 'spring-ai-alibaba-studio/ui/src/mock/tracemock.ts:775-805': 5.304186613958609, 'spring-ai-alibaba-studio/ui/src/mock/tracemock.ts:800-830': 5.731696488894743, 'spring-ai-alibaba-studio/ui/src/mock/tracemock.ts:900-930': 4.051900554269078, 'spring-ai-alibaba-studio/ui/src/mock/tracemock.ts:950-980': 5.289574268843581, 'spring-ai-alibaba-studio/ui/src/mock/tracemock.ts:975-1005': 5.748857589682863, 'spring-ai-alibaba-studio/ui/src/mock/tracemock.ts:1075-1105': 4.087237561785575, 'spring-ai-alibaba-studio/ui/src/utils/trace_util.ts:0-30': 2.0725182748781217, 'spring-ai-alibaba-studio/ui/src/utils/trace_util.ts:25-55': 2.0402786375861277, 'spring-ai-alibaba-studio/ui/src/utils/trace_util.ts:50-80': 4.506402668803027, 'spring-ai-alibaba-studio/ui/src/utils/trace_util.ts:75-105': 3.110626978986474, 'spring-ai-alibaba-studio/ui/src/utils/trace_util.ts:100-130': 3.766966467139168, 'spring-ai-alibaba-studio/ui/src/utils/trace_util.ts:125-155': 2.1891404899345344, 'spring-ai-alibaba-studio/ui/src/components/Chat/types.ts:0-30': 2.0725182748781217, 'spring-ai-alibaba-studio/ui/src/components/Chat/types.ts:25-55': 1.9277802266559592, 'spring-ai-alibaba-studio/ui/src/components/right_panel/types.ts:0-30': 2.0725182748781217, 'spring-ai-alibaba-studio/ui/src/components/right_panel/types.ts:25-55': 1.865120477714578, 'spring-ai-alibaba-studio/ui/src/services/graphs.ts:0-30': 2.0725182748781217, 'spring-ai-alibaba-studio/ui/src/services/graphs.ts:25-55': 1.7579816852101768, 'spring-ai-alibaba-studio/ui/src/services/chat_clients.ts:0-30': 2.0725182748781217, 'spring-ai-alibaba-studio/ui/src/services/chat_clients.ts:25-55': 1.4850091437813338, 'spring-ai-alibaba-studio/ui/src/services/trace_clients.ts:0-30': 2.0725182748781217, 'spring-ai-alibaba-studio/ui/src/services/trace_clients.ts:25-55': 2.1351767481159443, 'spring-ai-alibaba-studio/ui/src/services/chat_models.ts:0-30': 2.0725182748781217, 'spring-ai-alibaba-studio/ui/src/services/chat_models.ts:25-55': 1.6043800433693445, 'spring-ai-alibaba-studio/src/test/java/com/alibaba/cloud/ai/graph/GraphStudioApplication.java:0-30': 1.3457052358459312, 'spring-ai-alibaba-studio/src/test/java/com/alibaba/cloud/ai/graph/GraphStudioTests.java:0-30': 1.4206686751462156, 'spring-ai-alibaba-studio/src/main/java/com/alibaba/cloud/ai/StudioApplication.java:0-30': 1.5714276365890276, 'spring-ai-alibaba-studio/src/main/java/com/alibaba/cloud/ai/vo/TelemetryResult.java:0-30': 1.6624830670303476, 'spring-ai-alibaba-studio/src/main/java/com/alibaba/cloud/ai/vo/ActionResult.java:0-30': 1.6271269812152453, 'spring-ai-alibaba-studio/src/main/java/com/alibaba/cloud/ai/vo/ChatModelRunResult.java:0-30': 1.6100069440831988, 'spring-ai-alibaba-studio/src/main/java/com/alibaba/cloud/ai/vo/ChatClientRunResult.java:0-30': 1.5714276365890276, 'spring-ai-alibaba-studio/src/main/java/com/alibaba/cloud/ai/tracing/TraceIdInterceptor.java:0-30': 1.544983913259478, 'spring-ai-alibaba-studio/src/main/java/com/alibaba/cloud/ai/tracing/GlobalResponseBodyAdvice.java:0-30': 1.3990205075566946, 'spring-ai-alibaba-studio/src/main/java/com/alibaba/cloud/ai/config/ObserverConfiguration.java:0-30': 1.4754631369960434, 'spring-ai-alibaba-studio/src/main/java/com/alibaba/cloud/ai/config/SwaggerConfiguration.java:0-30': 1.555453881109404, 'spring-ai-alibaba-studio/src/main/java/com/alibaba/cloud/ai/config/GraphAutoConfiguration.java:0-30': 1.4339821696206667, 'spring-ai-alibaba-studio/src/main/java/com/alibaba/cloud/ai/config/WebConfig.java:0-30': 1.4475475537922684, 'spring-ai-alibaba-studio/src/main/java/com/alibaba/cloud/ai/config/FileSpanExporterAutoConfiguration.java:0-30': 1.9576222845269413, 'spring-ai-alibaba-studio/src/main/java/com/alibaba/cloud/ai/config/FileSpanExporterAutoConfiguration.java:25-55': 1.3738979381611398, 'spring-ai-alibaba-studio/src/main/java/com/alibaba/cloud/ai/graph/InitDataSerializer.java:0-30': 1.5295406001183258, 'spring-ai-alibaba-studio/src/main/java/com/alibaba/cloud/ai/graph/GraphInitData.java:0-30': 1.4707360092264137, 'spring-ai-alibaba-studio/src/main/java/com/alibaba/cloud/ai/graph/NodeOutputSerializer.java:0-30': 1.3821712726179565, 'spring-ai-alibaba-studio/src/main/java/com/alibaba/cloud/ai/graph/PersistentConfig.java:0-30': 1.6869199579366625, 'spring-ai-alibaba-studio/src/main/java/com/alibaba/cloud/ai/utils/JsonUtil.java:0-30': 1.5144029672636237, 'spring-ai-alibaba-studio/src/main/java/com/alibaba/cloud/ai/utils/FileUtils.java:0-30': 1.6685256744000492, 'spring-ai-alibaba-studio/src/main/java/com/alibaba/cloud/ai/utils/SpringApplicationUtil.java:0-30': 1.4206686751462156, 'spring-ai-alibaba-studio/src/main/java/com/alibaba/cloud/ai/utils/SpringApplicationUtil.java:25-55': 1.5932434174609875, 'spring-ai-alibaba-studio/src/main/java/com/alibaba/cloud/ai/utils/SpringApplicationUtil.java:50-80': 1.5607422602136187, 'spring-ai-alibaba-studio/src/main/java/com/alibaba/cloud/ai/utils/SpringApplicationUtil.java:100-130': 1.461372044515705, 'spring-ai-alibaba-studio/src/main/java/com/alibaba/cloud/ai/function/AgentFunctionCallbackWrapper.java:0-30': 1.2999753052429273, 'spring-ai-alibaba-studio/src/main/java/com/alibaba/cloud/ai/function/AgentFunctionCallbackWrapper.java:100-130': 1.4850091437813338, 'spring-ai-alibaba-studio/src/main/java/com/alibaba/cloud/ai/function/AgentFunctionCallbackWrapper.java:250-280': 2.8903834918255944, 'spring-ai-alibaba-studio/src/main/java/com/alibaba/cloud/ai/param/ModelRunActionParam.java:0-30': 1.4898286183391802, 'spring-ai-alibaba-studio/src/main/java/com/alibaba/cloud/ai/param/ClientRunActionParam.java:0-30': 1.5295406001183258, 'spring-ai-alibaba-studio/src/main/java/com/alibaba/cloud/ai/param/GraphStreamParam.java:0-30': 1.6807436320551328, 'spring-ai-alibaba-studio/src/main/java/com/alibaba/cloud/ai/oltp/StudioObservabilityProperties.java:0-30': 1.582260333065363, 'spring-ai-alibaba-studio/src/main/java/com/alibaba/cloud/ai/oltp/OtlpFileSpanExporter.java:0-30': 3.9966497529732257, 'spring-ai-alibaba-studio/src/main/java/com/alibaba/cloud/ai/oltp/OtlpFileSpanExporter.java:25-55': 3.5036089265602053, 'spring-ai-alibaba-studio/src/main/java/com/alibaba/cloud/ai/oltp/OtlpFileSpanExporter.java:50-80': 7.089323517215386, 'spring-ai-alibaba-studio/src/main/java/com/alibaba/cloud/ai/oltp/OtlpFileSpanExporter.java:75-105': 6.749009814083234, 'spring-ai-alibaba-studio/src/main/java/com/alibaba/cloud/ai/oltp/OtlpFileSpanExporterProvider.java:0-30': 2.375000193454486, 'spring-ai-alibaba-studio/src/main/java/com/alibaba/cloud/ai/oltp/OtlpFileSpanExporterProvider.java:25-55': 2.5432926470278674, 'spring-ai-alibaba-studio/src/main/java/com/alibaba/cloud/ai/controller/GraphAPIController.java:0-30': 1.4429973314407667, 'spring-ai-alibaba-studio/src/main/java/com/alibaba/cloud/ai/controller/ChatClientAPIController.java:0-30': 1.4076001190620582, 'spring-ai-alibaba-studio/src/main/java/com/alibaba/cloud/ai/controller/ChatModelAPIController.java:0-30': 1.4076001190620582, 'spring-ai-alibaba-studio/src/main/java/com/alibaba/cloud/ai/controller/ObservationApiController.java:0-30': 1.2926541151579503, 'spring-ai-alibaba-studio/src/main/java/com/alibaba/cloud/ai/common/R.java:0-30': 4.879385439276703, 'spring-ai-alibaba-studio/src/main/java/com/alibaba/cloud/ai/common/R.java:25-55': 5.88147697739519, 'spring-ai-alibaba-studio/src/main/java/com/alibaba/cloud/ai/common/R.java:50-80': 5.708592466117572, 'spring-ai-alibaba-studio/src/main/java/com/alibaba/cloud/ai/common/R.java:75-105': 5.2223480880461715, 'spring-ai-alibaba-studio/src/main/java/com/alibaba/cloud/ai/common/R.java:100-130': 5.539255283029591, 'spring-ai-alibaba-studio/src/main/java/com/alibaba/cloud/ai/common/ModelType.java:0-30': 1.806405830545199, 'spring-ai-alibaba-studio/src/main/java/com/alibaba/cloud/ai/common/ReturnCode.java:0-30': 6.957486389902649, 'spring-ai-alibaba-studio/src/main/java/com/alibaba/cloud/ai/common/ReturnCode.java:25-55': 5.94995342756166, 'spring-ai-alibaba-studio/src/main/java/com/alibaba/cloud/ai/model/ChatClient.java:0-30': 1.5144029672636237, 'spring-ai-alibaba-studio/src/main/java/com/alibaba/cloud/ai/model/ChatClient.java:100-130': 2.467969387073607, 'spring-ai-alibaba-studio/src/main/java/com/alibaba/cloud/ai/model/ChatModelConfig.java:0-30': 1.4162856102690216, 'spring-ai-alibaba-studio/src/main/java/com/alibaba/cloud/ai/model/Retriever.java:0-30': 1.8426605816446973, 'spring-ai-alibaba-studio/src/main/java/com/alibaba/cloud/ai/model/EmbeddingModel.java:0-30': 1.8279854392172998, 'spring-ai-alibaba-studio/src/main/java/com/alibaba/cloud/ai/model/Prompt.java:0-30': 1.8426605816446973, 'spring-ai-alibaba-studio/src/main/java/com/alibaba/cloud/ai/model/FunctionCalling.java:0-30': 1.8279854392172998, 'spring-ai-alibaba-studio/src/main/java/com/alibaba/cloud/ai/api/ChatModelAPI.java:0-30': 1.3863454179444579, 'spring-ai-alibaba-studio/src/main/java/com/alibaba/cloud/ai/api/ChatModelAPI.java:75-105': 2.2074947222987906, 'spring-ai-alibaba-studio/src/main/java/com/alibaba/cloud/ai/api/ChatClientAPI.java:0-30': 1.3301085874193976, 'spring-ai-alibaba-studio/src/main/java/com/alibaba/cloud/ai/api/GraphAPI.java:0-30': 1.394769803506785, 'spring-ai-alibaba-studio/src/main/java/com/alibaba/cloud/ai/api/PromptsAPI.java:0-30': 1.7249526580211665, 'spring-ai-alibaba-studio/src/main/java/com/alibaba/cloud/ai/service/GraphService.java:0-30': 1.4384756257963185, 'spring-ai-alibaba-studio/src/main/java/com/alibaba/cloud/ai/service/ChatClientDelegate.java:0-30': 1.4850091437813338, 'spring-ai-alibaba-studio/src/main/java/com/alibaba/cloud/ai/service/ChatModelDelegate.java:0-30': 1.4802207498974258, 'spring-ai-alibaba-studio/src/main/java/com/alibaba/cloud/ai/service/ChatModelDelegate.java:25-55': 1.6505282088323439, 'spring-ai-alibaba-studio/src/main/java/com/alibaba/cloud/ai/service/StudioObservabilityService.java:0-30': 5.350254323590637, 'spring-ai-alibaba-studio/src/main/java/com/alibaba/cloud/ai/service/StudioObservabilityService.java:25-55': 3.853287256944784, 'spring-ai-alibaba-studio/src/main/java/com/alibaba/cloud/ai/service/impl/GraphServiceImpl.java:0-30': 1.2782563680468169, 'spring-ai-alibaba-studio/src/main/java/com/alibaba/cloud/ai/service/impl/GraphServiceImpl.java:25-55': 2.0668517652813416, 'spring-ai-alibaba-studio/src/main/java/com/alibaba/cloud/ai/service/impl/GraphServiceImpl.java:50-80': 2.482481443059243, 'spring-ai-alibaba-studio/src/main/java/com/alibaba/cloud/ai/service/impl/GraphServiceImpl.java:75-105': 2.391091434672131, 'spring-ai-alibaba-studio/src/main/java/com/alibaba/cloud/ai/service/impl/GraphServiceImpl.java:175-205': 2.350146107290704, 'spring-ai-alibaba-studio/src/main/java/com/alibaba/cloud/ai/service/impl/ChatModelDelegateImpl.java:0-30': 1.2302952180021771, 'spring-ai-alibaba-studio/src/main/java/com/alibaba/cloud/ai/service/impl/ChatModelDelegateImpl.java:250-280': 1.1530399753898717, 'spring-ai-alibaba-studio/src/main/java/com/alibaba/cloud/ai/service/impl/ChatModelDelegateImpl.java:275-305': 1.8352936750219124, 'spring-ai-alibaba-studio/src/main/java/com/alibaba/cloud/ai/service/impl/StudioObservabilityServiceImpl.java:0-30': 1.2818256526239191, 'spring-ai-alibaba-studio/src/main/java/com/alibaba/cloud/ai/service/impl/StudioObservabilityServiceImpl.java:25-55': 2.5732688452296046, 'spring-ai-alibaba-studio/src/main/java/com/alibaba/cloud/ai/service/impl/StudioObservabilityServiceImpl.java:50-80': 2.4254336861032075, 'spring-ai-alibaba-studio/src/main/java/com/alibaba/cloud/ai/service/impl/StudioObservabilityServiceImpl.java:75-105': 4.1188865574145925, 'spring-ai-alibaba-studio/src/main/java/com/alibaba/cloud/ai/service/impl/StudioObservabilityServiceImpl.java:100-130': 1.3780221876928578, 'spring-ai-alibaba-studio/src/main/java/com/alibaba/cloud/ai/service/impl/StudioObservabilityServiceImpl.java:125-155': 1.4567346345301488, 'spring-ai-alibaba-studio/src/main/java/com/alibaba/cloud/ai/service/impl/StudioObservabilityServiceImpl.java:200-230': 1.3378614571544742, 'spring-ai-alibaba-studio/src/main/java/com/alibaba/cloud/ai/service/impl/ChatClientDelegateImpl.java:0-30': 1.2818256526239191, 'spring-ai-alibaba-studio/src/main/java/com/alibaba/cloud/ai/exception/RestExceptionHandler.java:0-30': 3.9966497529732257, 'spring-ai-alibaba-studio/src/main/java/com/alibaba/cloud/ai/exception/RestExceptionHandler.java:25-55': 5.626347667980785, 'spring-ai-alibaba-studio/src/main/java/com/alibaba/cloud/ai/exception/RestExceptionHandler.java:50-80': 5.520362890055638, 'spring-ai-alibaba-studio/src/main/java/com/alibaba/cloud/ai/exception/ServiceInternalException.java:0-30': 6.9131501412407586, 'spring-ai-alibaba-studio/src/main/java/com/alibaba/cloud/ai/exception/ServiceInternalException.java:25-55': 6.042659792873709, 'spring-ai-alibaba-studio/src/main/java/com/alibaba/cloud/ai/exception/NotFoundException.java:0-30': 6.9131501412407586, 'spring-ai-alibaba-studio/src/main/java/com/alibaba/cloud/ai/exception/NotFoundException.java:25-55': 6.042659792873709, 'spring-ai-alibaba-deepresearch/README-zh.md:75-105': 5.507002134693233, 'spring-ai-alibaba-deepresearch/ui-vue3/env.d.ts:0-30': 1.8727292772502506, 'spring-ai-alibaba-deepresearch/ui-vue3/README.md:0-30': 5.004411811238316, 'spring-ai-alibaba-deepresearch/ui-vue3/README.md:25-55': 2.7117070081607375, 'spring-ai-alibaba-deepresearch/ui-vue3/README.md:75-105': 9.605104129922168, 'spring-ai-alibaba-deepresearch/ui-vue3/README.md:100-130': 9.064278289983491, 'spring-ai-alibaba-deepresearch/ui-vue3/README.md:125-155': 3.3622795054565056, 'spring-ai-alibaba-deepresearch/ui-vue3/README.md:150-180': 9.792751502204833, 'spring-ai-alibaba-deepresearch/ui-vue3/README.md:175-205': 8.868029948035216, 'spring-ai-alibaba-deepresearch/ui-vue3/vite.config.ts:0-30': 1.5932434174609875, 'spring-ai-alibaba-deepresearch/ui-vue3/vitest.config.ts:0-30': 1.5346539525074083, 'spring-ai-alibaba-deepresearch/ui-vue3/cypress.config.ts:0-30': 1.7249526580211665, 'spring-ai-alibaba-deepresearch/ui-vue3/cypress/support/commands.ts:0-30': 1.8959327741568117, 'spring-ai-alibaba-deepresearch/ui-vue3/cypress/support/e2e.ts:0-30': 1.8207351761199357, 'spring-ai-alibaba-deepresearch/ui-vue3/src/main.ts:0-30': 1.4206686751462156, 'spring-ai-alibaba-deepresearch/ui-vue3/src/utils/stream.ts:0-30': 1.6505282088323439, 'spring-ai-alibaba-deepresearch/ui-vue3/src/utils/request.ts:0-30': 1.5607422602136187, 'spring-ai-alibaba-deepresearch/ui-vue3/src/utils/request.ts:25-55': 3.2584761210481337, 'spring-ai-alibaba-deepresearch/ui-vue3/src/utils/scroll.ts:0-30': 1.461372044515705, 'spring-ai-alibaba-deepresearch/ui-vue3/src/base/constants.ts:0-30': 1.3616719918917644, 'spring-ai-alibaba-deepresearch/ui-vue3/src/base/enums/Storage.ts:0-30': 1.6446150239729562, 'spring-ai-alibaba-deepresearch/ui-vue3/src/base/enums/ProvideInject.ts:0-30': 1.4295166990043104, 'spring-ai-alibaba-deepresearch/ui-vue3/src/base/i18n/en.ts:0-30': 1.576825380051758, 'spring-ai-alibaba-deepresearch/ui-vue3/src/base/i18n/zh.ts:0-30': 1.693141844245956, 'spring-ai-alibaba-deepresearch/ui-vue3/src/base/i18n/sortI18n.ts:0-30': 1.587732881718793, 'spring-ai-alibaba-deepresearch/ui-vue3/src/base/i18n/type.ts:0-30': 1.8352936750219124, 'spring-ai-alibaba-deepresearch/ui-vue3/src/base/i18n/index.ts:0-30': 1.6100069440831988, 'spring-ai-alibaba-deepresearch/ui-vue3/src/store/RouterStore.ts:0-30': 1.394769803506785, 'spring-ai-alibaba-deepresearch/ui-vue3/src/store/MessageStore.ts:0-30': 1.452126563470489, 'spring-ai-alibaba-deepresearch/ui-vue3/src/store/AuthStore.ts:0-30': 1.5502012189992864, 'spring-ai-alibaba-deepresearch/ui-vue3/src/store/ConfigStore.ts:0-30': 1.4162856102690216, 'spring-ai-alibaba-deepresearch/ui-vue3/src/store/ConversationStore.ts:0-30': 1.394769803506785, 'spring-ai-alibaba-deepresearch/ui-vue3/src/router/defaultRoutes.ts:0-30': 1.6156734531778587, 'spring-ai-alibaba-deepresearch/ui-vue3/src/router/index.ts:0-30': 1.6329148574925245, 'spring-ai-alibaba-deepresearch/src/test/java/com/alibaba/cloud/ai/example/deepresearch/tool/PythonReplToolNetworkTest.java:0-30': 4.94670553640388, 'spring-ai-alibaba-deepresearch/src/test/java/com/alibaba/cloud/ai/example/deepresearch/tool/PythonReplToolNetworkTest.java:25-55': 5.261608975980517, 'spring-ai-alibaba-deepresearch/src/test/java/com/alibaba/cloud/ai/example/deepresearch/tool/PythonReplToolNetworkTest.java:50-80': 3.9992957313595947, 'spring-ai-alibaba-deepresearch/src/test/java/com/alibaba/cloud/ai/example/deepresearch/tool/PythonReplToolBasisTest.java:0-30': 4.299898929426872, 'spring-ai-alibaba-deepresearch/src/test/java/com/alibaba/cloud/ai/example/deepresearch/tool/PythonReplToolBasisTest.java:25-55': 5.214285512399558, 'spring-ai-alibaba-deepresearch/src/test/java/com/alibaba/cloud/ai/example/deepresearch/tool/PythonReplToolBasisTest.java:50-80': 4.576548654203534, 'spring-ai-alibaba-deepresearch/src/test/java/com/alibaba/cloud/ai/example/deepresearch/tool/PythonReplToolBasisTest.java:75-105': 5.723425738006437, 'spring-ai-alibaba-deepresearch/src/test/java/com/alibaba/cloud/ai/example/deepresearch/tool/PythonReplToolBasisTest.java:100-130': 5.816300727958155, 'spring-ai-alibaba-deepresearch/src/test/java/com/alibaba/cloud/ai/example/deepresearch/tool/PythonReplToolBasisTest.java:125-155': 3.9804424028900343, 'spring-ai-alibaba-deepresearch/src/test/java/com/alibaba/cloud/ai/example/deepresearch/tool/SearchFilterServiceTest.java:0-30': 1.3697983017004274, 'spring-ai-alibaba-deepresearch/src/main/resources/prompts/bginvestigation.md:0-30': 2.6845893594622274, 'spring-ai-alibaba-deepresearch/src/main/resources/prompts/researcher.md:0-30': 7.118007635765663, 'spring-ai-alibaba-deepresearch/src/main/resources/prompts/researcher.md:25-55': 6.789500813452165, 'spring-ai-alibaba-deepresearch/src/main/resources/prompts/researcher.md:50-80': 1.6513001205104032, 'spring-ai-alibaba-deepresearch/src/main/resources/prompts/coordinator.md:0-30': 5.392792712561127, 'spring-ai-alibaba-deepresearch/src/main/resources/prompts/planner.md:0-30': 2.5583882040950137, 'spring-ai-alibaba-deepresearch/src/main/resources/prompts/planner.md:25-55': 1.885265672889272, 'spring-ai-alibaba-deepresearch/src/main/resources/prompts/planner.md:50-80': 2.783528707303553, 'spring-ai-alibaba-deepresearch/src/main/resources/prompts/planner.md:75-105': 2.783528707303553, 'spring-ai-alibaba-deepresearch/src/main/resources/prompts/planner.md:100-130': 2.7240652758531145, 'spring-ai-alibaba-deepresearch/src/main/resources/prompts/planner.md:125-155': 2.515117356077084, 'spring-ai-alibaba-deepresearch/src/main/resources/prompts/planner.md:150-180': 1.4567346345301488, 'spring-ai-alibaba-deepresearch/src/main/resources/prompts/planner.md:175-205': 2.180822559135374, 'spring-ai-alibaba-deepresearch/src/main/resources/prompts/coder.md:0-30': 9.112413623443517, 'spring-ai-alibaba-deepresearch/src/main/resources/prompts/coder.md:25-55': 5.361048766708908, 'spring-ai-alibaba-deepresearch/src/main/resources/prompts/reporter.md:0-30': 2.906213781627919, 'spring-ai-alibaba-deepresearch/src/main/resources/prompts/reporter.md:25-55': 2.2176969475324566, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/DeepResearchApplication.java:0-30': 1.5714276365890276, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/repository/ModelParamRepository.java:0-30': 1.6746123679952611, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/repository/ModelParamRepositoryImpl.java:0-30': 1.4850091437813338, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/repository/ModelParamRepositoryImpl.java:25-55': 1.4660390745170708, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/serializer/DeepResearchStateSerializer.java:0-30': 1.3301085874193976, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/serializer/DeepResearchDeserializer.java:0-30': 1.3780221876928578, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/serializer/MessageDeserializer.java:0-30': 1.4032971997212973, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/util/ReflectionProcessor.java:0-30': 2.0725182748781217, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/util/ReflectionProcessor.java:25-55': 2.1451543605784527, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/util/ReflectionProcessor.java:50-80': 1.2854149260948562, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/util/ReflectionProcessor.java:125-155': 2.3900973514792576, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/util/ReflectionProcessor.java:150-180': 3.550520818826627, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/util/StateUtil.java:0-30': 1.4802207498974258, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/util/ResourceUtil.java:0-30': 1.5502012189992864, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/util/TemplateUtil.java:0-30': 1.4339821696206667, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/util/SearchBeanUtil.java:0-30': 1.4850091437813338, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/util/ReflectionUtil.java:0-30': 1.587732881718793, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/util/ReflectionUtil.java:50-80': 2.120383154394645, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/util/ReflectionUtil.java:75-105': 1.576825380051758, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/util/ReflectionUtil.java:100-130': 2.381998806823414, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/util/Mcp/McpClientUtil.java:0-30': 1.3036670873065823, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/util/Mcp/McpConfigMergeUtil.java:0-30': 1.3780221876928578, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/util/Mcp/McpConfigMergeUtil.java:100-130': 2.397881862486783, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/util/export/FormatConversionUtil.java:0-30': 1.4206686751462156, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/util/export/FormatConversionUtil.java:125-155': 1.4567346345301488, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/util/export/FormatConversionUtil.java:150-180': 5.86365442043353, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/util/export/FormatConversionUtil.java:175-205': 4.302668759055283, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/util/export/FileOperationUtil.java:0-30': 1.4475475537922684, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/util/export/HtmlGenerationUtil.java:0-30': 1.539801608104721, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/util/export/HtmlGenerationUtil.java:25-55': 4.305177314788365, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/util/export/HtmlGenerationUtil.java:100-130': 3.3908506265900726, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/util/export/HtmlGenerationUtil.java:125-155': 3.3320075938941764, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/util/export/AsyncExportUtil.java:0-30': 1.6213799903421466, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/config/McpAssignNodeProperties.java:0-30': 1.5660667218567792, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/config/ReflectionProperties.java:0-30': 1.6624830670303476, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/config/ObservationProperties.java:0-30': 1.5987923371942092, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/config/DeepResearchConfiguration.java:0-30': 1.8473322675131334, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/config/DeepResearchConfiguration.java:25-55': 1.040644351365811, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/config/DeepResearchConfiguration.java:75-105': 1.5932434174609875, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/config/DeepResearchConfiguration.java:100-130': 1.6505282088323439, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/config/DeepResearchConfiguration.java:175-205': 1.8843112837068046, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/config/DeepResearchConfiguration.java:200-230': 1.1858029889787507, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/config/PythonCoderProperties.java:0-30': 1.555453881109404, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/config/PythonCoderProperties.java:50-80': 6.305880195930458, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/config/PythonCoderProperties.java:100-130': 5.497957131806549, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/config/RedisConfig.java:0-30': 1.4707360092264137, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/config/DeepResearchProperties.java:0-30': 1.5660667218567792, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/config/rag/RagAdvisorConfiguration.java:0-30': 1.2607040145492494, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/config/rag/RagProperties.java:0-30': 4.776064660248332, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/config/rag/RagProperties.java:25-55': 4.0657845924618465, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/config/rag/RagProperties.java:125-155': 1.8207351761199357, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/config/rag/RagProperties.java:200-230': 2.021144748148701, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/config/rag/RagProperties.java:375-405': 2.1743148813729136, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/config/rag/RagVectorStoreConfiguration.java:0-30': 1.3536415319003627, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/config/rag/RagDataAutoConfiguration.java:0-30': 1.3616719918917644, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/config/rag/RagDataAutoConfiguration.java:25-55': 6.915508563829076, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/config/export/ExportProperties.java:0-30': 1.5987923371942092, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/config/export/ExportConfiguration.java:0-30': 1.6156734531778587, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/dispatcher/InformationDispatcher.java:0-30': 1.587732881718793, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/dispatcher/ResearchTeamDispatcher.java:0-30': 1.582260333065363, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/dispatcher/HumanFeedbackDispatcher.java:0-30': 1.587732881718793, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/dispatcher/CoordinatorDispatcher.java:0-30': 1.5932434174609875, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/dispatcher/RewriteAndMultiQueryDispatcher.java:0-30': 2.1756546538314656, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/dispatcher/RewriteAndMultiQueryDispatcher.java:25-55': 2.259948689173584, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/agents/AgentModelsConfiguration.java:0-30': 1.2747069058480733, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/agents/AgentModelsConfiguration.java:50-80': 1.191960976798107, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/agents/AgentsConfiguration.java:0-30': 1.2782563680468169, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/agents/AgentsConfiguration.java:75-105': 1.3576448869712883, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/agents/AgentsConfiguration.java:100-130': 1.7488066641486304, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/agents/ObservationConfiguration.java:0-30': 1.3536415319003627, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/agents/McpAssignNodeConfiguration.java:0-30': 1.2963043732383113, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/rag/post/DocumentSelectFirstProcess.java:0-30': 1.5044765819016563, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/rag/retriever/RrfHybridElasticsearchRetriever.java:0-30': 1.3148693254696644, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/rag/retriever/RrfHybridElasticsearchRetriever.java:25-55': 1.5094234550964287, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/rag/retriever/RrfHybridElasticsearchRetriever.java:100-130': 1.539801608104721, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/rag/retriever/RrfHybridElasticsearchRetriever.java:125-155': 1.461372044515705, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/rag/retriever/RrfHybridElasticsearchRetriever.java:150-180': 2.0949338052906126, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/controller/ChatController.java:0-30': 1.2436271712225597, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/controller/ChatController.java:75-105': 1.9963002848973372, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/controller/ChatController.java:100-130': 1.0623180817251892, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/controller/ReportController.java:0-30': 1.3186463096802645, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/controller/ReportController.java:150-180': 1.390544851406706, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/controller/ReportController.java:175-205': 1.6807436320551328, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/controller/ReportController.java:200-230': 1.544983913259478, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/controller/McpController.java:0-30': 1.3990205075566946, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/controller/RagDataController.java:0-30': 1.5346539525074083, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/controller/RagDataController.java:25-55': 1.9829913947974505, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/controller/RagDataController.java:50-80': 2.3053458731572034, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/controller/graph/GraphProcess.java:0-30': 1.3339737577322137, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/controller/graph/GraphProcess.java:25-55': 2.3115085009570144, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/controller/graph/GraphProcess.java:75-105': 2.6024507430623003, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/controller/request/ChatRequestProcess.java:0-30': 1.587732881718793, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/model/ParallelEnum.java:0-30': 1.70572432962589, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/model/dto/McpServerInfo.java:0-30': 1.6213799903421466, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/model/dto/ReflectionResult.java:0-30': 1.6807436320551328, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/model/dto/ExportData.java:0-30': 1.6746123679952611, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/model/dto/Plan.java:0-30': 1.6100069440831988, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/model/response/ReportResponse.java:0-30': 1.731458800865721, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/model/req/ChatRequest.java:0-30': 1.6505282088323439, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/model/req/ExportRequest.java:0-30': 1.7184952272002534, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/model/req/FeedbackRequest.java:0-30': 1.6329148574925245, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/node/ResearchTeamNode.java:0-30': 1.4707360092264137, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/node/ReporterNode.java:0-30': 1.3224450553019844, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/node/HumanFeedbackNode.java:0-30': 1.5044765819016563, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/node/CoderNode.java:0-30': 1.31111391621134, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/node/CoderNode.java:100-130': 2.7841609496337165, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/node/CoderNode.java:150-180': 6.577796965630627, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/node/CoderNode.java:175-205': 8.018678793678014, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/node/RagNode.java:0-30': 1.452126563470489, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/node/ResearcherNode.java:0-30': 1.271177101356609, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/node/ResearcherNode.java:100-130': 1.2270067732655228, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/node/ResearcherNode.java:125-155': 2.8232962511933524, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/node/ResearcherNode.java:175-205': 2.3724225804488492, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/node/PlannerNode.java:0-30': 1.3378614571544742, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/node/BackgroundInvestigationNode.java:0-30': 1.3339737577322137, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/node/BackgroundInvestigationNode.java:50-80': 1.236925271703581, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/node/BackgroundInvestigationNode.java:75-105': 1.5607422602136187, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/node/ParallelExecutorNode.java:0-30': 1.3738979381611398, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/node/InformationNode.java:0-30': 1.3990205075566946, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/node/CoordinatorNode.java:0-30': 1.3738979381611398, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/node/RewriteAndMultiQueryNode.java:0-30': 1.3457052358459312, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/node/RewriteAndMultiQueryNode.java:25-55': 2.873391979879379, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/node/RewriteAndMultiQueryNode.java:50-80': 2.7861932095196886, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/node/RewriteAndMultiQueryNode.java:75-105': 1.5660667218567792, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/service/ReportService.java:0-30': 1.7579816852101768, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/service/McpProviderFactory.java:0-30': 1.271177101356609, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/service/InfoCheckService.java:0-30': 1.3417718832367183, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/service/VectorStoreDataIngestionService.java:0-30': 1.461372044515705, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/service/VectorStoreDataIngestionService.java:25-55': 1.555453881109404, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/service/ReportRedisService.java:0-30': 1.576825380051758, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/service/SearchFilterService.java:0-30': 1.4384756257963185, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/service/SearchFilterService.java:25-55': 2.1401539252526356, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/service/SearchFilterService.java:50-80': 1.4384756257963185, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/service/SearchFilterService.java:125-155': 2.2693333565209644, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/service/SearchFilterService.java:150-180': 1.693141844245956, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/service/LocalConfigSearchFilterService.java:0-30': 1.4850091437813338, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/service/ReportMemoryService.java:0-30': 1.5987923371942092, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/service/McpService.java:0-30': 1.4339821696206667, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/service/ExportService.java:0-30': 1.4119295075815892, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/tool/PlannerTool.java:0-30': 1.6505282088323439, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/tool/SearchFilterTool.java:0-30': 1.5346539525074083, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/tool/SearchFilterTool.java:25-55': 1.9410671124612497, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/tool/SearchFilterTool.java:50-80': 2.376967611632042, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/tool/PythonReplTool.java:0-30': 1.2963043732383113, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/tool/PythonReplTool.java:50-80': 6.880498014569668, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/tool/PythonReplTool.java:75-105': 5.424307359342876, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/tool/PythonReplTool.java:100-130': 3.411593750509827, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/tool/PythonReplTool.java:125-155': 5.187718228140874, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/tool/PythonReplTool.java:150-180': 5.10745686621668, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/tool/PythonReplTool.java:200-230': 1.916752790562206, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/tool/PythonReplTool.java:225-255': 4.110312280032471, 'spring-ai-alibaba-deepresearch/src/main/java/com/alibaba/cloud/ai/example/deepresearch/tool/PythonReplTool.java:250-280': 7.717818812238896, 'spring-ai-alibaba-mcp/spring-ai-alibaba-mcp-nacos-common/src/main/java/com/alibaba/cloud/ai/mcp/nacos/NacosMcpProperties.java:0-30': 1.3301085874193976, 'spring-ai-alibaba-mcp/spring-ai-alibaba-mcp-nacos-common/src/main/java/com/alibaba/cloud/ai/mcp/nacos/service/NacosMcpOperationService.java:0-30': 1.2336013365262284, 'spring-ai-alibaba-mcp/spring-ai-alibaba-mcp-nacos-common/src/main/java/com/alibaba/cloud/ai/mcp/nacos/service/NacosMcpOperationService.java:50-80': 1.2436271712225597, 'spring-ai-alibaba-mcp/spring-ai-alibaba-mcp-nacos-common/src/main/java/com/alibaba/cloud/ai/mcp/nacos/service/NacosMcpOperationService.java:75-105': 2.632050102314832, 'spring-ai-alibaba-mcp/spring-ai-alibaba-mcp-nacos-common/src/main/java/com/alibaba/cloud/ai/mcp/nacos/service/NacosMcpOperationService.java:100-130': 2.280245018182879, 'spring-ai-alibaba-mcp/spring-ai-alibaba-mcp-nacos-common/src/main/java/com/alibaba/cloud/ai/mcp/nacos/service/NacosMcpOperationService.java:125-155': 3.072306390357195, 'spring-ai-alibaba-mcp/spring-ai-alibaba-mcp-nacos-common/src/main/java/com/alibaba/cloud/ai/mcp/nacos/service/NacosMcpOperationService.java:150-180': 2.150833109710313, 'spring-ai-alibaba-mcp/spring-ai-alibaba-mcp-nacos-common/src/main/java/com/alibaba/cloud/ai/mcp/nacos/service/NacosMcpSubscriber.java:0-30': 1.5295406001183258, 'spring-ai-alibaba-mcp/spring-ai-alibaba-mcp-nacos-common/src/main/java/com/alibaba/cloud/ai/mcp/nacos/service/model/NacosMcpServerEndpoint.java:0-30': 1.587732881718793, 'spring-ai-alibaba-mcp/spring-ai-alibaba-mcp-gateway/src/main/java/com/alibaba/cloud/ai/mcp/nacos/gateway/tools/NacosMcpGatewayToolsInitializer.java:0-30': 1.2076394433123816, 'spring-ai-alibaba-mcp/spring-ai-alibaba-mcp-gateway/src/main/java/com/alibaba/cloud/ai/mcp/nacos/gateway/watcher/NacosMcpGatewayToolsWatcher.java:0-30': 1.182747793947089, 'spring-ai-alibaba-mcp/spring-ai-alibaba-mcp-gateway/src/main/java/com/alibaba/cloud/ai/mcp/nacos/gateway/provider/NacosMcpGatewayToolCallbackProvider.java:0-30': 1.4295166990043104, 'spring-ai-alibaba-mcp/spring-ai-alibaba-mcp-gateway/src/main/java/com/alibaba/cloud/ai/mcp/nacos/gateway/provider/NacosMcpSyncGatewayToolsProvider.java:0-30': 1.31111391621134, 'spring-ai-alibaba-mcp/spring-ai-alibaba-mcp-gateway/src/main/java/com/alibaba/cloud/ai/mcp/nacos/gateway/provider/NacosMcpAsyncGatewayToolsProvider.java:0-30': 1.31111391621134, 'spring-ai-alibaba-mcp/spring-ai-alibaba-mcp-gateway/src/main/java/com/alibaba/cloud/ai/mcp/nacos/gateway/provider/NacosMcpGatewayToolsProvider.java:0-30': 1.5607422602136187, 'spring-ai-alibaba-mcp/spring-ai-alibaba-mcp-gateway/src/main/java/com/alibaba/cloud/ai/mcp/nacos/gateway/utils/SpringBeanUtils.java:0-30': 1.4754631369960434, 'spring-ai-alibaba-mcp/spring-ai-alibaba-mcp-gateway/src/main/java/com/alibaba/cloud/ai/mcp/nacos/gateway/jsontemplate/RequestTemplateInfo.java:0-30': 1.6329148574925245, 'spring-ai-alibaba-mcp/spring-ai-alibaba-mcp-gateway/src/main/java/com/alibaba/cloud/ai/mcp/nacos/gateway/jsontemplate/RequestTemplateParser.java:0-30': 1.4384756257963185, 'spring-ai-alibaba-mcp/spring-ai-alibaba-mcp-gateway/src/main/java/com/alibaba/cloud/ai/mcp/nacos/gateway/jsontemplate/ResponseTemplateParser.java:0-30': 1.5094234550964287, 'spring-ai-alibaba-mcp/spring-ai-alibaba-mcp-gateway/src/main/java/com/alibaba/cloud/ai/mcp/nacos/gateway/properties/NacosMcpGatewayProperties.java:0-30': 1.5244612091889669, 'spring-ai-alibaba-mcp/spring-ai-alibaba-mcp-gateway/src/main/java/com/alibaba/cloud/ai/mcp/nacos/gateway/definition/NacosMcpGatewayToolDefinition.java:0-30': 1.3457052358459312, 'spring-ai-alibaba-mcp/spring-ai-alibaba-mcp-gateway/src/main/java/com/alibaba/cloud/ai/mcp/nacos/gateway/callback/DynamicNacosToolCallback.java:0-30': 1.1858029889787507, 'spring-ai-alibaba-mcp/spring-ai-alibaba-mcp-gateway/src/main/java/com/alibaba/cloud/ai/mcp/nacos/gateway/callback/DynamicNacosToolCallback.java:100-130': 1.9127595016868155, 'spring-ai-alibaba-mcp/spring-ai-alibaba-mcp-gateway/src/main/java/com/alibaba/cloud/ai/mcp/nacos/gateway/callback/DynamicNacosToolCallback.java:125-155': 1.4339821696206667, 'spring-ai-alibaba-mcp/spring-ai-alibaba-mcp-gateway/src/main/java/com/alibaba/cloud/ai/mcp/nacos/gateway/callback/DynamicNacosToolCallback.java:150-180': 2.0632034503977734, 'spring-ai-alibaba-mcp/spring-ai-alibaba-mcp-gateway/src/main/java/com/alibaba/cloud/ai/mcp/nacos/gateway/callback/DynamicNacosToolCallback.java:175-205': 2.1830004502966376, 'spring-ai-alibaba-mcp/spring-ai-alibaba-mcp-nacos2/src/main/java/com/alibaba/cloud/ai/mcp/nacos2/NacosMcpProperties.java:0-30': 1.3262657509484723, 'spring-ai-alibaba-mcp/spring-ai-alibaba-mcp-nacos2/src/main/java/com/alibaba/cloud/ai/mcp/nacos2/registry/NacosMcpRegister.java:0-30': 1.1858029889787507, 'spring-ai-alibaba-mcp/spring-ai-alibaba-mcp-nacos2/src/main/java/com/alibaba/cloud/ai/mcp/nacos2/registry/NacosMcpRegistryProperties.java:0-30': 1.4567346345301488, 'spring-ai-alibaba-mcp/spring-ai-alibaba-mcp-nacos2/src/main/java/com/alibaba/cloud/ai/mcp/nacos2/registry/utils/MD5Utils.java:0-30': 1.6100069440831988, 'spring-ai-alibaba-mcp/spring-ai-alibaba-mcp-nacos2/src/main/java/com/alibaba/cloud/ai/mcp/nacos2/registry/utils/JsonUtils.java:0-30': 1.5144029672636237, 'spring-ai-alibaba-mcp/spring-ai-alibaba-mcp-nacos2/src/main/java/com/alibaba/cloud/ai/mcp/nacos2/registry/model/McpToolsInfo.java:0-30': 1.6446150239729562, 'spring-ai-alibaba-mcp/spring-ai-alibaba-mcp-nacos2/src/main/java/com/alibaba/cloud/ai/mcp/nacos2/registry/model/McpNacosConstant.java:0-30': 1.576825380051758, 'spring-ai-alibaba-mcp/spring-ai-alibaba-mcp-nacos2/src/main/java/com/alibaba/cloud/ai/mcp/nacos2/registry/model/McpServerInfo.java:0-30': 1.693141844245956, 'spring-ai-alibaba-mcp/spring-ai-alibaba-mcp-nacos2/src/main/java/com/alibaba/cloud/ai/mcp/nacos2/registry/model/ToolMetaInfo.java:0-30': 1.6807436320551328, 'spring-ai-alibaba-mcp/spring-ai-alibaba-mcp-nacos2/src/main/java/com/alibaba/cloud/ai/mcp/nacos2/registry/model/ToolMetaInfo.java:25-55': 4.743672854150852, 'spring-ai-alibaba-mcp/spring-ai-alibaba-mcp-nacos2/src/main/java/com/alibaba/cloud/ai/mcp/nacos2/registry/model/ServiceRefInfo.java:0-30': 1.65648406863643, 'spring-ai-alibaba-mcp/spring-ai-alibaba-mcp-nacos2/src/main/java/com/alibaba/cloud/ai/mcp/nacos2/registry/model/RemoteServerConfigInfo.java:0-30': 1.6100069440831988, 'spring-ai-alibaba-mcp/spring-ai-alibaba-mcp-nacos2/src/main/java/com/alibaba/cloud/ai/mcp/nacos2/client/transport/LoadbalancedMcpSyncClient.java:0-30': 1.2538173069550065, 'spring-ai-alibaba-mcp/spring-ai-alibaba-mcp-nacos2/src/main/java/com/alibaba/cloud/ai/mcp/nacos2/client/transport/LoadbalancedMcpSyncClient.java:125-155': 1.4850091437813338, 'spring-ai-alibaba-mcp/spring-ai-alibaba-mcp-nacos2/src/main/java/com/alibaba/cloud/ai/mcp/nacos2/client/transport/LoadbalancedMcpSyncClient.java:150-180': 1.4206686751462156, 'spring-ai-alibaba-mcp/spring-ai-alibaba-mcp-nacos2/src/main/java/com/alibaba/cloud/ai/mcp/nacos2/client/transport/LoadbalancedMcpSyncClient.java:175-205': 1.949309549025362, 'spring-ai-alibaba-mcp/spring-ai-alibaba-mcp-nacos2/src/main/java/com/alibaba/cloud/ai/mcp/nacos2/client/transport/LoadbalancedMcpSyncClient.java:200-230': 1.307379897570568, 'spring-ai-alibaba-mcp/spring-ai-alibaba-mcp-nacos2/src/main/java/com/alibaba/cloud/ai/mcp/nacos2/client/transport/LoadbalancedMcpAsyncClient.java:0-30': 1.2538173069550065, 'spring-ai-alibaba-mcp/spring-ai-alibaba-mcp-nacos2/src/main/java/com/alibaba/cloud/ai/mcp/nacos2/client/transport/LoadbalancedMcpAsyncClient.java:125-155': 1.4660390745170708, 'spring-ai-alibaba-mcp/spring-ai-alibaba-mcp-nacos2/src/main/java/com/alibaba/cloud/ai/mcp/nacos2/client/transport/LoadbalancedMcpAsyncClient.java:175-205': 1.9534570733186172, 'spring-ai-alibaba-mcp/spring-ai-alibaba-mcp-nacos2/src/main/java/com/alibaba/cloud/ai/mcp/nacos2/client/transport/LoadbalancedMcpAsyncClient.java:225-255': 1.236925271703581, 'spring-ai-alibaba-mcp/spring-ai-alibaba-mcp-nacos2/src/main/java/com/alibaba/cloud/ai/mcp/nacos2/client/tool/LoadbalancedAsyncMcpToolCallback.java:0-30': 1.4660390745170708, 'spring-ai-alibaba-mcp/spring-ai-alibaba-mcp-nacos2/src/main/java/com/alibaba/cloud/ai/mcp/nacos2/client/tool/LoadbalancedSyncMcpToolCallback.java:0-30': 1.4567346345301488, 'spring-ai-alibaba-mcp/spring-ai-alibaba-mcp-nacos2/src/main/java/com/alibaba/cloud/ai/mcp/nacos2/client/tool/LoadbalancedAsyncMcpToolCallbackProvider.java:0-30': 1.4339821696206667, 'spring-ai-alibaba-mcp/spring-ai-alibaba-mcp-nacos2/src/main/java/com/alibaba/cloud/ai/mcp/nacos2/client/tool/LoadbalancedSyncMcpToolCallbackProvider.java:0-30': 1.4295166990043104, 'spring-ai-alibaba-mcp/spring-ai-alibaba-mcp-nacos2/src/main/java/com/alibaba/cloud/ai/mcp/nacos2/gateway/tools/NacosMcpGatewayToolsInitializer.java:0-30': 1.2044708402952395, 'spring-ai-alibaba-mcp/spring-ai-alibaba-mcp-nacos2/src/main/java/com/alibaba/cloud/ai/mcp/nacos2/gateway/tools/NacosHelper.java:0-30': 1.3616719918917644, 'spring-ai-alibaba-mcp/spring-ai-alibaba-mcp-nacos2/src/main/java/com/alibaba/cloud/ai/mcp/nacos2/gateway/tools/ToolConfig.java:0-30': 1.6213799903421466, 'spring-ai-alibaba-mcp/spring-ai-alibaba-mcp-nacos2/src/main/java/com/alibaba/cloud/ai/mcp/nacos2/gateway/tools/ToolConfig.java:25-55': 1.8959327741568117, 'spring-ai-alibaba-mcp/spring-ai-alibaba-mcp-nacos2/src/main/java/com/alibaba/cloud/ai/mcp/nacos2/gateway/tools/ToolConfig.java:75-105': 1.9691264049738522, 'spring-ai-alibaba-mcp/spring-ai-alibaba-mcp-nacos2/src/main/java/com/alibaba/cloud/ai/mcp/nacos2/gateway/tools/ToolConfig.java:175-205': 2.441829756043897, 'spring-ai-alibaba-mcp/spring-ai-alibaba-mcp-nacos2/src/main/java/com/alibaba/cloud/ai/mcp/nacos2/gateway/tools/ToolConfig.java:200-230': 2.5362668510000503, 'spring-ai-alibaba-mcp/spring-ai-alibaba-mcp-nacos2/src/main/java/com/alibaba/cloud/ai/mcp/nacos2/gateway/tools/ToolConfig.java:225-255': 1.7853297775722856, 'spring-ai-alibaba-mcp/spring-ai-alibaba-mcp-nacos2/src/main/java/com/alibaba/cloud/ai/mcp/nacos2/gateway/tools/NacosMcpGatewayToolsInfo.java:0-30': 1.5346539525074083, 'spring-ai-alibaba-mcp/spring-ai-alibaba-mcp-nacos2/src/main/java/com/alibaba/cloud/ai/mcp/nacos2/gateway/watcher/NacosMcpGatewayToolsWatcher.java:0-30': 1.1617944313296826, 'spring-ai-alibaba-mcp/spring-ai-alibaba-mcp-nacos2/src/main/java/com/alibaba/cloud/ai/mcp/nacos2/gateway/watcher/NacosMcpGatewayToolsWatcher.java:175-205': 1.3536415319003627, 'spring-ai-alibaba-mcp/spring-ai-alibaba-mcp-nacos2/src/main/java/com/alibaba/cloud/ai/mcp/nacos2/gateway/watcher/NacosMcpGatewayToolsWatcher.java:325-355': 1.5194154424953983, 'spring-ai-alibaba-mcp/spring-ai-alibaba-mcp-nacos2/src/main/java/com/alibaba/cloud/ai/mcp/nacos2/gateway/provider/NacosMcpGatewayToolCallbackProvider.java:0-30': 1.4250789533148378, 'spring-ai-alibaba-mcp/spring-ai-alibaba-mcp-nacos2/src/main/java/com/alibaba/cloud/ai/mcp/nacos2/gateway/provider/NacosMcpGatewaySyncGatewayToolsProvider.java:0-30': 1.3036670873065823, 'spring-ai-alibaba-mcp/spring-ai-alibaba-mcp-nacos2/src/main/java/com/alibaba/cloud/ai/mcp/nacos2/gateway/provider/NacosMcpGatewayAsyncGatewayToolsProvider.java:0-30': 1.3496617171995993, 'spring-ai-alibaba-mcp/spring-ai-alibaba-mcp-nacos2/src/main/java/com/alibaba/cloud/ai/mcp/nacos2/gateway/provider/NacosMcpGatewayToolsProvider.java:0-30': 1.6043800433693445, 'spring-ai-alibaba-mcp/spring-ai-alibaba-mcp-nacos2/src/main/java/com/alibaba/cloud/ai/mcp/nacos2/gateway/utils/SpringBeanUtils.java:0-30': 1.4707360092264137, 'spring-ai-alibaba-mcp/spring-ai-alibaba-mcp-nacos2/src/main/java/com/alibaba/cloud/ai/mcp/nacos2/gateway/jsontemplate/RequestTemplateInfo.java:0-30': 1.6271269812152453, 'spring-ai-alibaba-mcp/spring-ai-alibaba-mcp-nacos2/src/main/java/com/alibaba/cloud/ai/mcp/nacos2/gateway/jsontemplate/RequestTemplateParser.java:0-30': 1.4339821696206667, 'spring-ai-alibaba-mcp/spring-ai-alibaba-mcp-nacos2/src/main/java/com/alibaba/cloud/ai/mcp/nacos2/gateway/jsontemplate/ResponseTemplateParser.java:0-30': 1.5044765819016563, 'spring-ai-alibaba-mcp/spring-ai-alibaba-mcp-nacos2/src/main/java/com/alibaba/cloud/ai/mcp/nacos2/gateway/properties/NacosMcpGatewayProperties.java:0-30': 1.5244612091889669, 'spring-ai-alibaba-mcp/spring-ai-alibaba-mcp-nacos2/src/main/java/com/alibaba/cloud/ai/mcp/nacos2/gateway/definition/NacosMcpGatewayToolDefinitionV3.java:0-30': 1.4250789533148378, 'spring-ai-alibaba-mcp/spring-ai-alibaba-mcp-nacos2/src/main/java/com/alibaba/cloud/ai/mcp/nacos2/gateway/definition/NacosMcpGatewayToolDefinition.java:0-30': 1.4295166990043104, 'spring-ai-alibaba-mcp/spring-ai-alibaba-mcp-nacos2/src/main/java/com/alibaba/cloud/ai/mcp/nacos2/gateway/callback/NacosMcpGatewayToolCallbackV3.java:0-30': 1.2044708402952395, 'spring-ai-alibaba-mcp/spring-ai-alibaba-mcp-nacos2/src/main/java/com/alibaba/cloud/ai/mcp/nacos2/gateway/callback/NacosMcpGatewayToolCallbackV3.java:100-130': 1.936971978195703, 'spring-ai-alibaba-mcp/spring-ai-alibaba-mcp-nacos2/src/main/java/com/alibaba/cloud/ai/mcp/nacos2/gateway/callback/NacosMcpGatewayToolCallbackV3.java:150-180': 2.000272609463373, 'spring-ai-alibaba-mcp/spring-ai-alibaba-mcp-nacos2/src/main/java/com/alibaba/cloud/ai/mcp/nacos2/gateway/callback/NacosMcpGatewayToolCallbackV3.java:175-205': 2.106211857018519, 'spring-ai-alibaba-mcp/spring-ai-alibaba-mcp-nacos2/src/main/java/com/alibaba/cloud/ai/mcp/nacos2/gateway/callback/NacosMcpGatewayToolCallback.java:0-30': 1.2890243568454003, 'spring-ai-alibaba-mcp/spring-ai-alibaba-mcp-nacos/src/main/java/com/alibaba/cloud/ai/mcp/nacos/registry/NacosMcpRegister.java:0-30': 1.2044708402952395, 'spring-ai-alibaba-mcp/spring-ai-alibaba-mcp-nacos/src/main/java/com/alibaba/cloud/ai/mcp/nacos/registry/NacosMcpRegistryProperties.java:0-30': 1.461372044515705, 'spring-ai-alibaba-mcp/spring-ai-alibaba-mcp-nacos/src/main/java/com/alibaba/cloud/ai/mcp/nacos/registry/utils/JsonSchemaUtils.java:0-30': 1.576825380051758, 'spring-ai-alibaba-mcp/spring-ai-alibaba-mcp-nacos/src/main/java/com/alibaba/cloud/ai/mcp/nacos/client/transport/LoadbalancedMcpSyncClient.java:0-30': 1.2402671679441073, 'spring-ai-alibaba-mcp/spring-ai-alibaba-mcp-nacos/src/main/java/com/alibaba/cloud/ai/mcp/nacos/client/transport/LoadbalancedMcpSyncClient.java:125-155': 1.4660390745170708, 'spring-ai-alibaba-mcp/spring-ai-alibaba-mcp-nacos/src/main/java/com/alibaba/cloud/ai/mcp/nacos/client/transport/LoadbalancedMcpSyncClient.java:150-180': 1.3457052358459312, 'spring-ai-alibaba-mcp/spring-ai-alibaba-mcp-nacos/src/main/java/com/alibaba/cloud/ai/mcp/nacos/client/transport/LoadbalancedMcpSyncClient.java:175-205': 1.3738979381611398, 'spring-ai-alibaba-mcp/spring-ai-alibaba-mcp-nacos/src/main/java/com/alibaba/cloud/ai/mcp/nacos/client/transport/LoadbalancedMcpAsyncClient.java:0-30': 1.2402671679441073, 'spring-ai-alibaba-mcp/spring-ai-alibaba-mcp-nacos/src/main/java/com/alibaba/cloud/ai/mcp/nacos/client/transport/LoadbalancedMcpAsyncClient.java:125-155': 1.3863454179444579, 'spring-ai-alibaba-mcp/spring-ai-alibaba-mcp-nacos/src/main/java/com/alibaba/cloud/ai/mcp/nacos/client/transport/LoadbalancedMcpAsyncClient.java:175-205': 1.8737230773897195, 'spring-ai-alibaba-mcp/spring-ai-alibaba-mcp-nacos/src/main/java/com/alibaba/cloud/ai/mcp/nacos/client/utils/NacosMcpClientUtils.java:0-30': 1.4946794771655387, 'spring-ai-alibaba-mcp/spring-ai-alibaba-mcp-nacos/src/main/java/com/alibaba/cloud/ai/mcp/nacos/client/utils/ApplicationContextHolder.java:0-30': 1.576825380051758, 'spring-ai-alibaba-mcp/spring-ai-alibaba-mcp-nacos/src/main/java/com/alibaba/cloud/ai/mcp/nacos/client/builder/WebFluxSseClientTransportBuilder.java:0-30': 1.4567346345301488, 'spring-ai-alibaba-mcp/spring-ai-alibaba-mcp-nacos/src/main/java/com/alibaba/cloud/ai/mcp/nacos/client/tool/LoadbalancedAsyncMcpToolCallback.java:0-30': 1.4754631369960434, 'spring-ai-alibaba-mcp/spring-ai-alibaba-mcp-nacos/src/main/java/com/alibaba/cloud/ai/mcp/nacos/client/tool/LoadbalancedSyncMcpToolCallback.java:0-30': 1.4660390745170708, 'spring-ai-alibaba-mcp/spring-ai-alibaba-mcp-nacos/src/main/java/com/alibaba/cloud/ai/mcp/nacos/client/tool/LoadbalancedAsyncMcpToolCallbackProvider.java:0-30': 1.4206686751462156, 'spring-ai-alibaba-mcp/spring-ai-alibaba-mcp-nacos/src/main/java/com/alibaba/cloud/ai/mcp/nacos/client/tool/LoadbalancedSyncMcpToolCallbackProvider.java:0-30': 1.4384756257963185, 'auto-configurations/spring-ai-alibaba-autoconfigure-nacos-mcp-client/src/main/java/com/alibaba/cloud/ai/autoconfigure/mcp/client/NacosMcpTransportBuilderAutoConfiguration.java:0-30': 1.4339821696206667, 'auto-configurations/spring-ai-alibaba-autoconfigure-nacos-mcp-client/src/main/java/com/alibaba/cloud/ai/autoconfigure/mcp/client/NacosMcpClientAutoConfiguration.java:0-30': 1.2436271712225597, 'auto-configurations/spring-ai-alibaba-autoconfigure-nacos-mcp-client/src/main/java/com/alibaba/cloud/ai/autoconfigure/mcp/client/NacosMcpSseClientProperties.java:0-30': 1.582260333065363, 'auto-configurations/spring-ai-alibaba-autoconfigure-nacos-mcp-client/src/main/java/com/alibaba/cloud/ai/autoconfigure/mcp/client/NacosMcpToolCallbackAutoConfiguration.java:0-30': 1.2172460758505306, 'auto-configurations/spring-ai-alibaba-autoconfigure-nacos-mcp-client/src/main/java/com/alibaba/cloud/ai/autoconfigure/mcp/client/NacosMcpAutoConfiguration.java:0-30': 1.4475475537922684, 'auto-configurations/spring-ai-alibaba-autoconfigure-nacos-prompt/src/test/java/com/alibaba/cloud/ai/autoconfigure/prompt/PromptTemplateAutoConfigurationTests.java:0-30': 1.4898286183391802, 'auto-configurations/spring-ai-alibaba-autoconfigure-nacos-prompt/src/test/java/com/alibaba/cloud/ai/autoconfigure/prompt/NacosPromptTmplPropertiesTests.java:0-30': 1.4475475537922684, 'auto-configurations/spring-ai-alibaba-autoconfigure-nacos-prompt/src/main/java/com/alibaba/cloud/ai/autoconfigure/prompt/PromptTmplNacosConfigCondition.java:0-30': 1.5346539525074083, 'auto-configurations/spring-ai-alibaba-autoconfigure-nacos-prompt/src/main/java/com/alibaba/cloud/ai/autoconfigure/prompt/PromptTemplateAutoConfiguration.java:0-30': 1.3990205075566946, 'auto-configurations/spring-ai-alibaba-autoconfigure-nacos-prompt/src/main/java/com/alibaba/cloud/ai/autoconfigure/prompt/NacosPromptTmplProperties.java:0-30': 1.555453881109404, 'auto-configurations/spring-ai-alibaba-autoconfigure-graph-observation/src/test/java/com/alibaba/cloud/ai/autoconfigure/graph/GraphObservationAutoConfigurationTest.java:0-30': 1.3697983017004274, 'auto-configurations/spring-ai-alibaba-autoconfigure-graph-observation/src/main/java/com/alibaba/cloud/ai/autoconfigure/graph/GraphObservationProperties.java:0-30': 1.5660667218567792, 'auto-configurations/spring-ai-alibaba-autoconfigure-graph-observation/src/main/java/com/alibaba/cloud/ai/autoconfigure/graph/GraphObservationAutoConfiguration.java:0-30': 1.2538173069550065, 'auto-configurations/spring-ai-alibaba-autoconfigure-graph-observation/src/main/java/com/alibaba/cloud/ai/autoconfigure/graph/GraphObservationAutoConfiguration.java:75-105': 1.4754631369960434, 'auto-configurations/spring-ai-alibaba-autoconfigure-arms-observation/src/main/java/com/alibaba/cloud/ai/autoconfigure/arms/ArmsCommonProperties.java:0-30': 1.582260333065363, 'auto-configurations/spring-ai-alibaba-autoconfigure-arms-observation/src/main/java/com/alibaba/cloud/ai/autoconfigure/arms/ArmsCommonProperties.java:25-55': 2.1743148813729136, 'auto-configurations/spring-ai-alibaba-autoconfigure-arms-observation/src/main/java/com/alibaba/cloud/ai/autoconfigure/arms/ArmsAutoConfiguration.java:0-30': 1.3148693254696644, 'auto-configurations/spring-ai-alibaba-autoconfigure-nacos2-mcp-client/src/main/java/com/alibaba/cloud/ai/autoconfigure/mcp/client/Nacos2McpSseClientProperties.java:0-30': 1.5932434174609875, 'auto-configurations/spring-ai-alibaba-autoconfigure-nacos2-mcp-client/src/main/java/com/alibaba/cloud/ai/autoconfigure/mcp/client/Nacos2McpClientAutoConfiguration.java:0-30': 1.2470054290998271, 'auto-configurations/spring-ai-alibaba-autoconfigure-nacos2-mcp-client/src/main/java/com/alibaba/cloud/ai/autoconfigure/mcp/client/Nacos2McpToolCallbackAutoConfiguration.java:0-30': 1.2044708402952395, 'auto-configurations/spring-ai-alibaba-autoconfigure-nacos2-mcp-client/src/main/java/com/alibaba/cloud/ai/autoconfigure/mcp/client/Nacos2McpAutoConfiguration.java:0-30': 1.3738979381611398, 'auto-configurations/spring-ai-alibaba-autoconfigure-memory/src/main/java/com/alibaba/cloud/ai/autoconfigure/memory/ElasticsearchChatMemoryProperties.java:0-30': 1.5932434174609875, 'auto-configurations/spring-ai-alibaba-autoconfigure-memory/src/main/java/com/alibaba/cloud/ai/autoconfigure/memory/OracleChatMemoryAutoConfiguration.java:0-30': 1.3262657509484723, 'auto-configurations/spring-ai-alibaba-autoconfigure-memory/src/main/java/com/alibaba/cloud/ai/autoconfigure/memory/RedisChatMemoryProperties.java:0-30': 1.6271269812152453, 'auto-configurations/spring-ai-alibaba-autoconfigure-memory/src/main/java/com/alibaba/cloud/ai/autoconfigure/memory/MysqlChatMemoryProperties.java:0-30': 1.5244612091889669, 'auto-configurations/spring-ai-alibaba-autoconfigure-memory/src/main/java/com/alibaba/cloud/ai/autoconfigure/memory/SqlServerChatMemoryProperties.java:0-30': 1.5144029672636237, 'auto-configurations/spring-ai-alibaba-autoconfigure-memory/src/main/java/com/alibaba/cloud/ai/autoconfigure/memory/RedisChatMemoryAutoConfiguration.java:0-30': 1.4206686751462156, 'auto-configurations/spring-ai-alibaba-autoconfigure-memory/src/main/java/com/alibaba/cloud/ai/autoconfigure/memory/OracleChatMemoryProperties.java:0-30': 1.5295406001183258, 'auto-configurations/spring-ai-alibaba-autoconfigure-memory/src/main/java/com/alibaba/cloud/ai/autoconfigure/memory/SQLiteChatMemoryAutoConfiguration.java:0-30': 1.3262657509484723, 'auto-configurations/spring-ai-alibaba-autoconfigure-memory/src/main/java/com/alibaba/cloud/ai/autoconfigure/memory/MysqlChatMemoryAutoConfiguration.java:0-30': 1.3262657509484723, 'auto-configurations/spring-ai-alibaba-autoconfigure-memory/src/main/java/com/alibaba/cloud/ai/autoconfigure/memory/PostgresChatMemoryProperties.java:0-30': 1.5194154424953983, 'auto-configurations/spring-ai-alibaba-autoconfigure-memory/src/main/java/com/alibaba/cloud/ai/autoconfigure/memory/PostgresChatMemoryAutoConfiguration.java:0-30': 1.3262657509484723, 'auto-configurations/spring-ai-alibaba-autoconfigure-memory/src/main/java/com/alibaba/cloud/ai/autoconfigure/memory/ChatMemoryAutoConfiguration.java:0-30': 1.3821712726179565, 'auto-configurations/spring-ai-alibaba-autoconfigure-memory/src/main/java/com/alibaba/cloud/ai/autoconfigure/memory/ElasticsearchChatMemoryAutoConfiguration.java:0-30': 1.3036670873065823, 'auto-configurations/spring-ai-alibaba-autoconfigure-memory/src/main/java/com/alibaba/cloud/ai/autoconfigure/memory/ElasticsearchChatMemoryAutoConfiguration.java:75-105': 1.1858029889787507, 'auto-configurations/spring-ai-alibaba-autoconfigure-memory/src/main/java/com/alibaba/cloud/ai/autoconfigure/memory/ElasticsearchChatMemoryAutoConfiguration.java:100-130': 2.133886349869392, 'auto-configurations/spring-ai-alibaba-autoconfigure-memory/src/main/java/com/alibaba/cloud/ai/autoconfigure/memory/SqlServerChatMemoryAutoConfiguration.java:0-30': 1.3224450553019844, 'auto-configurations/spring-ai-alibaba-autoconfigure-memory/src/main/java/com/alibaba/cloud/ai/autoconfigure/memory/SQLiteChatMemoryProperties.java:0-30': 1.5144029672636237, 'auto-configurations/spring-ai-alibaba-autoconfigure-nacos-mcp-server/src/main/java/com/alibaba/cloud/ai/autoconfigure/mcp/server/NacosMcpRegistryAutoConfiguration.java:0-30': 1.2676667917202797, 'auto-configurations/spring-ai-alibaba-autoconfigure-nacos-mcp-gateway/src/main/java/com/alibaba/cloud/ai/autoconfigure/mcp/gateway/NacosMcpGatewayAutoConfiguration.java:0-30': 1.1530399753898717, 'auto-configurations/spring-ai-alibaba-autoconfigure-nacos-mcp-gateway/src/main/java/com/alibaba/cloud/ai/autoconfigure/mcp/gateway/NacosMcpGatewayAutoConfiguration.java:150-180': 2.397881862486783, 'auto-configurations/spring-ai-alibaba-autoconfigure-nacos2-mcp-server/src/main/java/com/alibaba/cloud/ai/autoconfigure/mcp/server/Nacos2McpGatewayServerAutoConfiguration.java:0-30': 1.133117125648299, 'auto-configurations/spring-ai-alibaba-autoconfigure-nacos2-mcp-server/src/main/java/com/alibaba/cloud/ai/autoconfigure/mcp/server/Nacos2McpGatewayServerAutoConfiguration.java:150-180': 2.370948960573387, 'auto-configurations/spring-ai-alibaba-autoconfigure-nacos2-mcp-server/src/main/java/com/alibaba/cloud/ai/autoconfigure/mcp/server/Nacos2McpRegistryAutoConfiguration.java:0-30': 1.2336013365262284, 'auto-configurations/spring-ai-alibaba-autoconfigure-dashscope/src/test/java/com/alibaba/cloud/ai/autoconfigure/dashscope/DashScopeAutoConfigurationIT.java:0-30': 1.236925271703581, 'auto-configurations/spring-ai-alibaba-autoconfigure-dashscope/src/test/java/com/alibaba/cloud/ai/autoconfigure/dashscope/DashScopeAutoConfigurationIT.java:125-155': 1.9466615985637934, 'auto-configurations/spring-ai-alibaba-autoconfigure-dashscope/src/test/java/com/alibaba/cloud/ai/autoconfigure/dashscope/DashScopeAutoConfigurationIT.java:150-180': 1.1797083017415622, 'auto-configurations/spring-ai-alibaba-autoconfigure-dashscope/src/test/java/com/alibaba/cloud/ai/autoconfigure/dashscope/DashScopeAutoConfigurationIT.java:175-205': 1.0359475268251883, 'auto-configurations/spring-ai-alibaba-autoconfigure-dashscope/src/test/java/com/alibaba/cloud/ai/autoconfigure/dashscope/DashScopePropertiesTests.java:0-30': 1.2747069058480733, 'auto-configurations/spring-ai-alibaba-autoconfigure-dashscope/src/test/java/com/alibaba/cloud/ai/autoconfigure/dashscope/DashScopePropertiesTests.java:325-355': 1.6497241332839427, 'auto-configurations/spring-ai-alibaba-autoconfigure-dashscope/src/test/java/com/alibaba/cloud/ai/autoconfigure/dashscope/DashScopeModelConfigurationTests.java:0-30': 1.31111391621134, 'auto-configurations/spring-ai-alibaba-autoconfigure-dashscope/src/main/java/com/alibaba/cloud/ai/autoconfigure/dashscope/DashScopeImageAutoConfiguration.java:0-30': 1.3224450553019844, 'auto-configurations/spring-ai-alibaba-autoconfigure-dashscope/src/main/java/com/alibaba/cloud/ai/autoconfigure/dashscope/DashScopeAudioSpeechSynthesisProperties.java:0-30': 1.4250789533148378, 'auto-configurations/spring-ai-alibaba-autoconfigure-dashscope/src/main/java/com/alibaba/cloud/ai/autoconfigure/dashscope/DashScopeRerankAutoConfiguration.java:0-30': 1.31111391621134, 'auto-configurations/spring-ai-alibaba-autoconfigure-dashscope/src/main/java/com/alibaba/cloud/ai/autoconfigure/dashscope/DashScopeEmbeddingAutoConfiguration.java:0-30': 1.3301085874193976, 'auto-configurations/spring-ai-alibaba-autoconfigure-dashscope/src/main/java/com/alibaba/cloud/ai/autoconfigure/dashscope/DashScopeAudioTranscriptionProperties.java:0-30': 1.461372044515705, 'auto-configurations/spring-ai-alibaba-autoconfigure-dashscope/src/main/java/com/alibaba/cloud/ai/autoconfigure/dashscope/ResolvedConnectionProperties.java:0-30': 1.576825380051758, 'auto-configurations/spring-ai-alibaba-autoconfigure-dashscope/src/main/java/com/alibaba/cloud/ai/autoconfigure/dashscope/DashScopeAudioTranscriptionAutoConfiguration.java:0-30': 1.2782563680468169, 'auto-configurations/spring-ai-alibaba-autoconfigure-dashscope/src/main/java/com/alibaba/cloud/ai/autoconfigure/dashscope/DashScopeConnectionUtils.java:0-30': 1.5094234550964287, 'auto-configurations/spring-ai-alibaba-autoconfigure-dashscope/src/main/java/com/alibaba/cloud/ai/autoconfigure/dashscope/DashScopeChatProperties.java:0-30': 1.5094234550964287, 'auto-configurations/spring-ai-alibaba-autoconfigure-dashscope/src/main/java/com/alibaba/cloud/ai/autoconfigure/dashscope/DashScopeParentProperties.java:0-30': 1.6271269812152453, 'auto-configurations/spring-ai-alibaba-autoconfigure-dashscope/src/main/java/com/alibaba/cloud/ai/autoconfigure/dashscope/DashScopeRerankProperties.java:0-30': 1.5660667218567792, 'auto-configurations/spring-ai-alibaba-autoconfigure-dashscope/src/main/java/com/alibaba/cloud/ai/autoconfigure/dashscope/DashScopeAudioSpeechAutoConfiguration.java:0-30': 1.2999753052429273, 'auto-configurations/spring-ai-alibaba-autoconfigure-dashscope/src/main/java/com/alibaba/cloud/ai/autoconfigure/dashscope/ConditionalOnDashScopeEnabled.java:0-30': 1.6156734531778587, 'auto-configurations/spring-ai-alibaba-autoconfigure-dashscope/src/main/java/com/alibaba/cloud/ai/autoconfigure/dashscope/DashScopeAgentProperties.java:0-30': 1.4754631369960434, 'auto-configurations/spring-ai-alibaba-autoconfigure-dashscope/src/main/java/com/alibaba/cloud/ai/autoconfigure/dashscope/DashScopeChatAutoConfiguration.java:0-30': 1.2747069058480733, 'auto-configurations/spring-ai-alibaba-autoconfigure-dashscope/src/main/java/com/alibaba/cloud/ai/autoconfigure/dashscope/DashScopeAgentAutoConfiguration.java:0-30': 1.2963043732383113, 'auto-configurations/spring-ai-alibaba-autoconfigure-dashscope/src/main/java/com/alibaba/cloud/ai/autoconfigure/dashscope/DashScopeEmbeddingProperties.java:0-30': 1.3990205075566946, 'auto-configurations/spring-ai-alibaba-autoconfigure-dashscope/src/main/java/com/alibaba/cloud/ai/autoconfigure/dashscope/DashScopeConnectionProperties.java:0-30': 1.4995620278218964, 'auto-configurations/spring-ai-alibaba-autoconfigure-dashscope/src/main/java/com/alibaba/cloud/ai/autoconfigure/dashscope/DashScopeImageProperties.java:0-30': 1.5660667218567792, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-common/src/main/java/com/alibaba/cloud/ai/config/AnalyticDbVectorStoreAutoConfiguration.java:0-30': 1.2999753052429273, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-common/src/main/java/com/alibaba/cloud/ai/dbconnector/AbstractJdbcDdl.java:0-30': 1.4076001190620582, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-common/src/main/java/com/alibaba/cloud/ai/dbconnector/BizDataSourceTypeEnum.java:0-30': 6.85220507084877, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-common/src/main/java/com/alibaba/cloud/ai/dbconnector/BizDataSourceTypeEnum.java:25-55': 5.882811081180502, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-common/src/main/java/com/alibaba/cloud/ai/dbconnector/BizDataSourceTypeEnum.java:50-80': 5.843603894255781, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-common/src/main/java/com/alibaba/cloud/ai/dbconnector/BizDataSourceTypeEnum.java:75-105': 6.022721142664907, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-common/src/main/java/com/alibaba/cloud/ai/dbconnector/BizDataSourceTypeEnum.java:100-130': 5.3754510980742145, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-common/src/main/java/com/alibaba/cloud/ai/dbconnector/BizDataSourceTypeEnum.java:125-155': 4.565248844571051, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-common/src/main/java/com/alibaba/cloud/ai/dbconnector/ErrorCodeEnum.java:0-30': 5.128993183965175, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-common/src/main/java/com/alibaba/cloud/ai/dbconnector/ErrorCodeEnum.java:100-130': 6.07575567581308, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-common/src/main/java/com/alibaba/cloud/ai/dbconnector/ErrorCodeEnum.java:125-155': 6.044396694776738, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-common/src/main/java/com/alibaba/cloud/ai/dbconnector/ErrorCodeEnum.java:150-180': 5.5811785557053435, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-common/src/main/java/com/alibaba/cloud/ai/dbconnector/ResultSetConverter.java:0-30': 1.5094234550964287, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-common/src/main/java/com/alibaba/cloud/ai/dbconnector/ColumnTypeParser.java:0-30': 1.4898286183391802, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-common/src/main/java/com/alibaba/cloud/ai/dbconnector/DdlFactory.java:0-30': 1.4707360092264137, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-common/src/main/java/com/alibaba/cloud/ai/dbconnector/DbConfig.java:0-30': 1.6446150239729562, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-common/src/main/java/com/alibaba/cloud/ai/dbconnector/DbAccessTypeEnum.java:0-30': 1.6505282088323439, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-common/src/main/java/com/alibaba/cloud/ai/dbconnector/DbAccessTypeEnum.java:25-55': 5.929785024186182, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-common/src/main/java/com/alibaba/cloud/ai/dbconnector/DbAccessTypeEnum.java:50-80': 5.482962095444926, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-common/src/main/java/com/alibaba/cloud/ai/dbconnector/MdTableGenerator.java:0-30': 1.6156734531778587, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-common/src/main/java/com/alibaba/cloud/ai/dbconnector/SqlExecutor.java:0-30': 1.5660667218567792, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-common/src/main/java/com/alibaba/cloud/ai/dbconnector/SqlExecutor.java:25-55': 4.416456268730301, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-common/src/main/java/com/alibaba/cloud/ai/dbconnector/SqlExecutor.java:50-80': 5.016228048588586, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-common/src/main/java/com/alibaba/cloud/ai/dbconnector/SqlExecutor.java:75-105': 4.900603832016889, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-common/src/main/java/com/alibaba/cloud/ai/dbconnector/SqlExecutor.java:100-130': 4.58898514665252, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-common/src/main/java/com/alibaba/cloud/ai/dbconnector/MysqlJdbcConnectionPool.java:0-30': 6.031884483940312, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-common/src/main/java/com/alibaba/cloud/ai/dbconnector/MysqlJdbcConnectionPool.java:25-55': 5.150670192459443, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-common/src/main/java/com/alibaba/cloud/ai/dbconnector/DatabaseDialectEnum.java:0-30': 6.754044722068457, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-common/src/main/java/com/alibaba/cloud/ai/dbconnector/DatabaseDialectEnum.java:25-55': 6.006387162770322, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-common/src/main/java/com/alibaba/cloud/ai/dbconnector/AbstractDBConnectionPool.java:0-30': 1.4995620278218964, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-common/src/main/java/com/alibaba/cloud/ai/dbconnector/AbstractDBConnectionPool.java:25-55': 3.992299452656859, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-common/src/main/java/com/alibaba/cloud/ai/dbconnector/AbstractDBConnectionPool.java:50-80': 4.673180221998242, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-common/src/main/java/com/alibaba/cloud/ai/dbconnector/AbstractDBConnectionPool.java:75-105': 1.4295166990043104, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-common/src/main/java/com/alibaba/cloud/ai/dbconnector/AbstractDBConnectionPool.java:100-130': 4.492568793968834, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-common/src/main/java/com/alibaba/cloud/ai/dbconnector/ResultSetBuilder.java:0-30': 1.4707360092264137, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-common/src/main/java/com/alibaba/cloud/ai/dbconnector/DBConnectionPool.java:0-30': 4.754787255441469, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-common/src/main/java/com/alibaba/cloud/ai/dbconnector/MysqlJdbcDdl.java:0-30': 1.3780221876928578, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-common/src/main/java/com/alibaba/cloud/ai/dbconnector/MysqlJdbcDdl.java:25-55': 1.5094234550964287, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-common/src/main/java/com/alibaba/cloud/ai/dbconnector/MysqlJdbcDdl.java:50-80': 1.5660667218567792, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-common/src/main/java/com/alibaba/cloud/ai/dbconnector/MysqlJdbcDdl.java:75-105': 2.0725182748781217, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-common/src/main/java/com/alibaba/cloud/ai/dbconnector/MysqlJdbcDdl.java:100-130': 2.0046400786841847, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-common/src/main/java/com/alibaba/cloud/ai/dbconnector/MysqlJdbcDdl.java:125-155': 1.9787176467773766, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-common/src/main/java/com/alibaba/cloud/ai/dbconnector/MysqlJdbcDdl.java:150-180': 2.472273516629977, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-common/src/main/java/com/alibaba/cloud/ai/dbconnector/MysqlJdbcDdl.java:175-205': 1.5244612091889669, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-common/src/main/java/com/alibaba/cloud/ai/dbconnector/MysqlJdbcDdl.java:200-230': 1.4754631369960434, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-common/src/main/java/com/alibaba/cloud/ai/dbconnector/MysqlJdbcDdl.java:225-255': 1.8426605816446973, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-common/src/main/java/com/alibaba/cloud/ai/dbconnector/PostgreJdbcDdl.java:0-30': 1.4707360092264137, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-common/src/main/java/com/alibaba/cloud/ai/dbconnector/PostgreJdbcDdl.java:25-55': 1.5144029672636237, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-common/src/main/java/com/alibaba/cloud/ai/dbconnector/PostgreJdbcDdl.java:50-80': 1.6271269812152453, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-common/src/main/java/com/alibaba/cloud/ai/dbconnector/PostgreJdbcDdl.java:75-105': 2.455456959720817, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-common/src/main/java/com/alibaba/cloud/ai/dbconnector/PostgreJdbcDdl.java:100-130': 2.155225660967689, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-common/src/main/java/com/alibaba/cloud/ai/dbconnector/PostgreJdbcDdl.java:125-155': 2.4991926139612386, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-common/src/main/java/com/alibaba/cloud/ai/dbconnector/PostgreJdbcDdl.java:150-180': 2.6334291378382013, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-common/src/main/java/com/alibaba/cloud/ai/dbconnector/PostgreJdbcDdl.java:175-205': 1.4660390745170708, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-common/src/main/java/com/alibaba/cloud/ai/dbconnector/PostgreJdbcDdl.java:200-230': 2.5206414820836, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-common/src/main/java/com/alibaba/cloud/ai/dbconnector/PostgreJdbcDdl.java:225-255': 1.544983913259478, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-common/src/main/java/com/alibaba/cloud/ai/dbconnector/PostgreJdbcDdl.java:250-280': 1.5346539525074083, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-common/src/main/java/com/alibaba/cloud/ai/dbconnector/PostgreJdbcDdl.java:275-305': 2.1045380082733556, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-common/src/main/java/com/alibaba/cloud/ai/dbconnector/PostgreSqlJdbcConnectionPool.java:0-30': 4.386728511639085, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-common/src/main/java/com/alibaba/cloud/ai/dbconnector/PostgreSqlJdbcConnectionPool.java:25-55': 5.263037882416917, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-common/src/main/java/com/alibaba/cloud/ai/dbconnector/AbstractDdl.java:0-30': 1.744619444156759, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-common/src/main/java/com/alibaba/cloud/ai/dbconnector/DbAccessor.java:0-30': 1.5932434174609875, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-common/src/main/java/com/alibaba/cloud/ai/dbconnector/DbAccessor.java:25-55': 2.446516666690098, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-common/src/main/java/com/alibaba/cloud/ai/dbconnector/DbAccessor.java:75-105': 3.962606657079413, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-common/src/main/java/com/alibaba/cloud/ai/dbconnector/DbAccessor.java:100-130': 1.8814023722736681, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-common/src/main/java/com/alibaba/cloud/ai/dbconnector/bo/ResultSetBO.java:0-30': 1.5094234550964287, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-common/src/main/java/com/alibaba/cloud/ai/dbconnector/bo/CommentInfoBO.java:0-30': 1.6043800433693445, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-common/src/main/java/com/alibaba/cloud/ai/dbconnector/bo/SchemaInfoBO.java:0-30': 1.6505282088323439, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-common/src/main/java/com/alibaba/cloud/ai/dbconnector/bo/DbInstanceBO.java:0-30': 1.6624830670303476, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-common/src/main/java/com/alibaba/cloud/ai/dbconnector/bo/ForeignKeyInfoBO.java:0-30': 1.65648406863643, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-common/src/main/java/com/alibaba/cloud/ai/dbconnector/bo/ColumnInfoBO.java:0-30': 1.5244612091889669, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-common/src/main/java/com/alibaba/cloud/ai/dbconnector/bo/DbQueryParameter.java:0-30': 1.6213799903421466, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-common/src/main/java/com/alibaba/cloud/ai/dbconnector/bo/DbQueryParameter.java:200-230': 3.2838214095309017, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-common/src/main/java/com/alibaba/cloud/ai/dbconnector/bo/DatabaseInfoBO.java:0-30': 1.6869199579366625, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-common/src/main/java/com/alibaba/cloud/ai/dbconnector/bo/DdlBaseBO.java:0-30': 1.8279854392172998, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-common/src/main/java/com/alibaba/cloud/ai/dbconnector/bo/TableInfoBO.java:0-30': 1.6624830670303476, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-common/src/main/java/com/alibaba/cloud/ai/dbconnector/bo/TableInfoBO.java:100-130': 2.992833303999305, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-common/src/main/java/com/alibaba/cloud/ai/request/SchemaInitRequest.java:0-30': 1.5987923371942092, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-common/src/main/java/com/alibaba/cloud/ai/request/SchemaInitRequest.java:50-80': 3.8184358709992736, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-common/src/main/java/com/alibaba/cloud/ai/request/DeleteRequest.java:0-30': 1.6869199579366625, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-common/src/main/java/com/alibaba/cloud/ai/request/DeleteRequest.java:50-80': 3.961765995679038, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-common/src/main/java/com/alibaba/cloud/ai/request/EvidenceRequest.java:0-30': 1.6807436320551328, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-common/src/main/java/com/alibaba/cloud/ai/request/EvidenceRequest.java:50-80': 4.018328507009416, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-common/src/main/java/com/alibaba/cloud/ai/request/SearchRequest.java:0-30': 1.6685256744000492, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-common/src/main/java/com/alibaba/cloud/ai/request/SearchRequest.java:75-105': 3.2584761210481337, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-common/src/main/java/com/alibaba/cloud/ai/analyticdb/AnalyticDbConfig.java:0-30': 1.6807436320551328, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-common/src/main/java/com/alibaba/cloud/ai/analyticdb/AnalyticDbVectorStoreProperties.java:0-30': 1.5244612091889669, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-common/src/main/java/com/alibaba/cloud/ai/analyticdb/AnalyticDbVectorStore.java:0-30': 1.3536415319003627, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-common/src/main/java/com/alibaba/cloud/ai/analyticdb/AnalyticDbVectorStore.java:100-130': 2.287448280781081, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-common/src/main/java/com/alibaba/cloud/ai/analyticdb/AnalyticDbVectorStore.java:125-155': 2.3643100044971743, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-common/src/main/java/com/alibaba/cloud/ai/analyticdb/AnalyticDbVectorStore.java:150-180': 2.126168417038694, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-common/src/main/java/com/alibaba/cloud/ai/analyticdb/AnalyticDbVectorStore.java:375-405': 2.058577366497676, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-common/src/main/java/com/alibaba/cloud/ai/analyticdb/AdVectorFilterExpressionConverter.java:0-30': 1.5144029672636237, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-common/src/main/java/com/alibaba/cloud/ai/analyticdb/AdVectorFilterExpressionConverter.java:50-80': 1.6505282088323439, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-common/src/main/java/com/alibaba/cloud/ai/analyticdb/AdVectorFilterExpressionConverter.java:75-105': 2.039100362400001, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-management/src/test/java/com/alibaba/cloud/ai/controller/PythonReplToolTest.java:0-30': 4.021310781213186, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-management/src/test/java/com/alibaba/cloud/ai/controller/PythonReplToolTest.java:25-55': 4.872607799829622, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-management/src/test/java/com/alibaba/cloud/ai/controller/PythonReplToolTest.java:50-80': 4.455793327479779, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-management/src/test/java/com/alibaba/cloud/ai/controller/PythonReplToolTest.java:75-105': 5.75740856606614, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-management/src/test/java/com/alibaba/cloud/ai/controller/PythonReplToolTest.java:100-130': 5.759622533636136, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-management/src/test/java/com/alibaba/cloud/ai/controller/PythonReplToolTest.java:125-155': 3.9067737436138046, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-management/src/main/java/com/alibaba/cloud/ai/service/SimpleVectorStoreManagementService.java:0-30': 1.2926541151579503, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-management/src/main/java/com/alibaba/cloud/ai/service/VectorStoreManagementService.java:0-30': 1.4475475537922684, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-management/src/main/java/com/alibaba/cloud/ai/service/AnalyticDbVectorStoreManagementService.java:0-30': 1.3417718832367183, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-chat/README.md:550-580': 1.6746123679952611, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-chat/README.md:600-630': 1.5660667218567792, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-chat/README.md:625-655': 1.6329148574925245, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-chat/README.md:650-680': 1.5194154424953983, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-chat/src/main/java/com/alibaba/cloud/ai/util/StateUtils.java:0-30': 1.6624830670303476, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-chat/src/main/java/com/alibaba/cloud/ai/util/AiConfiguration.java:0-30': 1.2926541151579503, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-chat/src/main/java/com/alibaba/cloud/ai/util/MarkdownParser.java:0-30': 6.533962466596478, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-chat/src/main/java/com/alibaba/cloud/ai/util/MarkdownParser.java:25-55': 5.787528144130967, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-chat/src/main/java/com/alibaba/cloud/ai/util/DateTimeUtil.java:0-30': 1.4384756257963185, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-chat/src/main/java/com/alibaba/cloud/ai/util/StepResultUtils.java:0-30': 1.6807436320551328, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-chat/src/main/java/com/alibaba/cloud/ai/util/NewLineParser.java:0-30': 1.744619444156759, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-chat/src/main/java/com/alibaba/cloud/ai/util/ExceptionUtils.java:0-30': 1.712085963376952, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-chat/src/main/java/com/alibaba/cloud/ai/config/Nl2sqlConfiguration.java:0-30': 1.3378614571544742, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-chat/src/main/java/com/alibaba/cloud/ai/config/BaseDefaultConfiguration.java:0-30': 1.3457052358459312, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-chat/src/main/java/com/alibaba/cloud/ai/config/RestConfiguration.java:0-30': 1.4295166990043104, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-chat/src/main/java/com/alibaba/cloud/ai/config/PythonCoderProperties.java:0-30': 1.5660667218567792, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-chat/src/main/java/com/alibaba/cloud/ai/config/PythonCoderProperties.java:25-55': 4.704031418578175, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-chat/src/main/java/com/alibaba/cloud/ai/config/PythonCoderProperties.java:50-80': 6.0133167453323795, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-chat/src/main/java/com/alibaba/cloud/ai/config/PythonCoderProperties.java:100-130': 5.491997031578651, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-chat/src/main/java/com/alibaba/cloud/ai/dispatcher/QueryRewriteDispatcher.java:0-30': 1.5144029672636237, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-chat/src/main/java/com/alibaba/cloud/ai/dispatcher/SqlGenerateDispatcher.java:0-30': 1.5144029672636237, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-chat/src/main/java/com/alibaba/cloud/ai/dispatcher/PlanExecutorDispatcher.java:0-30': 1.539801608104721, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-chat/src/main/java/com/alibaba/cloud/ai/dispatcher/SQLExecutorDispatcher.java:0-30': 1.576825380051758, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-chat/src/main/java/com/alibaba/cloud/ai/dispatcher/SemanticConsistenceDispatcher.java:0-30': 1.5660667218567792, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-chat/src/main/java/com/alibaba/cloud/ai/dispatcher/SqlValidateDispatcher.java:0-30': 1.5660667218567792, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-chat/src/main/java/com/alibaba/cloud/ai/schema/ColumnDTO.java:0-30': 1.693141844245956, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-chat/src/main/java/com/alibaba/cloud/ai/schema/Plan.java:0-30': 1.555453881109404, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-chat/src/main/java/com/alibaba/cloud/ai/schema/ExecutionStep.java:0-30': 1.544983913259478, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-chat/src/main/java/com/alibaba/cloud/ai/schema/ExecutionStep.java:25-55': 2.233884771648712, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-chat/src/main/java/com/alibaba/cloud/ai/schema/TableDTO.java:0-30': 1.6043800433693445, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-chat/src/main/java/com/alibaba/cloud/ai/schema/SchemaDTO.java:0-30': 1.6446150239729562, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-chat/src/main/java/com/alibaba/cloud/ai/constant/Constant.java:0-30': 1.582260333065363, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-chat/src/main/java/com/alibaba/cloud/ai/prompt/PromptHelper.java:0-30': 1.4032971997212973, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-chat/src/main/java/com/alibaba/cloud/ai/prompt/PromptHelper.java:200-230': 2.087060421917937, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-chat/src/main/java/com/alibaba/cloud/ai/prompt/PromptHelper.java:225-255': 2.88702260087918, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-chat/src/main/java/com/alibaba/cloud/ai/prompt/PromptConstant.java:0-30': 1.6100069440831988, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-chat/src/main/java/com/alibaba/cloud/ai/prompt/PromptLoader.java:0-30': 1.6329148574925245, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-chat/src/main/java/com/alibaba/cloud/ai/node/PythonExecuteNode.java:0-30': 1.4567346345301488, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-chat/src/main/java/com/alibaba/cloud/ai/node/PlanExecutorNode.java:0-30': 1.539801608104721, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-chat/src/main/java/com/alibaba/cloud/ai/node/SelfReflectNode.java:0-30': 1.5607422602136187, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-chat/src/main/java/com/alibaba/cloud/ai/node/SqlValidateNode.java:0-30': 1.452126563470489, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-chat/src/main/java/com/alibaba/cloud/ai/node/SqlValidateNode.java:50-80': 1.3496617171995993, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-chat/src/main/java/com/alibaba/cloud/ai/node/SqlGenerateNode.java:0-30': 1.3616719918917644, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-chat/src/main/java/com/alibaba/cloud/ai/node/SchemaRecallNode.java:0-30': 1.4660390745170708, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-chat/src/main/java/com/alibaba/cloud/ai/node/PlannerNode.java:0-30': 1.394769803506785, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-chat/src/main/java/com/alibaba/cloud/ai/node/AbstractPlanBasedNode.java:0-30': 1.4384756257963185, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-chat/src/main/java/com/alibaba/cloud/ai/node/SqlExecuteNode.java:0-30': 1.3990205075566946, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-chat/src/main/java/com/alibaba/cloud/ai/node/SqlExecuteNode.java:50-80': 1.250402090744536, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-chat/src/main/java/com/alibaba/cloud/ai/node/SqlExecuteNode.java:75-105': 1.4898286183391802, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-chat/src/main/java/com/alibaba/cloud/ai/node/QueryRewriteNode.java:0-30': 1.4754631369960434, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-chat/src/main/java/com/alibaba/cloud/ai/node/SemanticConsistencNode.java:0-30': 1.3863454179444579, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-chat/src/main/java/com/alibaba/cloud/ai/node/ReportGeneratorNode.java:0-30': 1.3990205075566946, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-chat/src/main/java/com/alibaba/cloud/ai/node/ReportGeneratorNode.java:50-80': 2.1624339252477434, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-chat/src/main/java/com/alibaba/cloud/ai/node/ReportGeneratorNode.java:75-105': 2.125291522339446, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-chat/src/main/java/com/alibaba/cloud/ai/node/ReportGeneratorNode.java:100-130': 3.0332749304770665, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-chat/src/main/java/com/alibaba/cloud/ai/node/ReportGeneratorNode.java:125-155': 1.587732881718793, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-chat/src/main/java/com/alibaba/cloud/ai/node/KeywordExtractNode.java:0-30': 1.4567346345301488, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-chat/src/main/java/com/alibaba/cloud/ai/node/TableRelationNode.java:0-30': 1.4032971997212973, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-chat/src/main/java/com/alibaba/cloud/ai/service/LlmService.java:0-30': 1.555453881109404, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-chat/src/main/java/com/alibaba/cloud/ai/service/simple/SimpleVectorStoreService.java:0-30': 1.3224450553019844, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-chat/src/main/java/com/alibaba/cloud/ai/service/simple/SimpleVectorStoreService.java:225-255': 1.9872836441506343, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-chat/src/main/java/com/alibaba/cloud/ai/service/simple/SimpleSchemaService.java:0-30': 1.4339821696206667, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-chat/src/main/java/com/alibaba/cloud/ai/service/simple/SimpleSchemaService.java:25-55': 1.9048226335943024, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-chat/src/main/java/com/alibaba/cloud/ai/service/simple/SimpleSchemaService.java:50-80': 2.1846625150837045, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-chat/src/main/java/com/alibaba/cloud/ai/service/simple/SimpleNl2SqlService.java:0-30': 1.4206686751462156, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-chat/src/main/java/com/alibaba/cloud/ai/service/analytic/AnalyticSchemaService.java:0-30': 1.394769803506785, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-chat/src/main/java/com/alibaba/cloud/ai/service/analytic/AnalyticSchemaService.java:25-55': 1.3697983017004274, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-chat/src/main/java/com/alibaba/cloud/ai/service/analytic/AnalyticSchemaService.java:50-80': 2.244808586589493, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-chat/src/main/java/com/alibaba/cloud/ai/service/analytic/AnalyticNl2SqlService.java:0-30': 1.3301085874193976, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-chat/src/main/java/com/alibaba/cloud/ai/service/analytic/AnalyticVectorStoreService.java:0-30': 1.3301085874193976, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-chat/src/main/java/com/alibaba/cloud/ai/service/base/BaseVectorStoreService.java:0-30': 1.4995620278218964, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-chat/src/main/java/com/alibaba/cloud/ai/service/base/BaseVectorStoreService.java:50-80': 1.6624830670303476, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-chat/src/main/java/com/alibaba/cloud/ai/service/base/BaseVectorStoreService.java:75-105': 1.8279854392172998, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-chat/src/main/java/com/alibaba/cloud/ai/service/base/BaseNl2SqlService.java:0-30': 1.3339737577322137, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-chat/src/main/java/com/alibaba/cloud/ai/service/base/BaseNl2SqlService.java:75-105': 1.4802207498974258, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-chat/src/main/java/com/alibaba/cloud/ai/service/base/BaseSchemaService.java:0-30': 1.3576448869712883, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-chat/src/main/java/com/alibaba/cloud/ai/tool/PythonReplTool.java:0-30': 1.2999753052429273, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-chat/src/main/java/com/alibaba/cloud/ai/tool/PythonReplTool.java:50-80': 6.341131695001768, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-chat/src/main/java/com/alibaba/cloud/ai/tool/PythonReplTool.java:75-105': 3.5600086716775525, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-chat/src/main/java/com/alibaba/cloud/ai/tool/PythonReplTool.java:100-130': 3.3112514832500364, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-chat/src/main/java/com/alibaba/cloud/ai/tool/PythonReplTool.java:150-180': 5.219788486212143, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-chat/src/main/java/com/alibaba/cloud/ai/tool/PythonReplTool.java:175-205': 6.7831850033645456, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-chat/src/main/java/com/alibaba/cloud/ai/tool/PythonReplTool.java:250-280': 1.869906890424954, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-chat/src/main/java/com/alibaba/cloud/ai/tool/PythonReplTool.java:275-305': 7.411903496265312, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-chat/src/main/java/com/alibaba/cloud/ai/tool/PythonReplTool.java:300-330': 7.481615193586846, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-chat/src/main/java/com/alibaba/cloud/ai/tool/PythonReplTool.java:375-405': 1.4384756257963185, 'spring-ai-alibaba-nl2sql/spring-ai-alibaba-nl2sql-chat/src/main/java/com/alibaba/cloud/ai/tool/PythonReplTool.java:400-430': 2.115497405994602, 'spring-ai-alibaba-prompt/spring-ai-alibaba-prompt-nacos/src/test/java/com/alibaba/cloud/ai/prompt/ConfigurablePromptTemplateTests.java:0-30': 1.4250789533148378, 'spring-ai-alibaba-prompt/spring-ai-alibaba-prompt-nacos/src/test/java/com/alibaba/cloud/ai/prompt/ConfigurablePromptTemplateTests.java:25-55': 5.047888769023494, 'spring-ai-alibaba-prompt/spring-ai-alibaba-prompt-nacos/src/test/java/com/alibaba/cloud/ai/prompt/ConfigurablePromptTemplateTests.java:50-80': 2.6468975387865377, 'spring-ai-alibaba-prompt/spring-ai-alibaba-prompt-nacos/src/test/java/com/alibaba/cloud/ai/prompt/ConfigurablePromptTemplateTests.java:75-105': 2.832337624904243, 'spring-ai-alibaba-prompt/spring-ai-alibaba-prompt-nacos/src/test/java/com/alibaba/cloud/ai/prompt/ConfigurablePromptTemplateTests.java:100-130': 1.4384756257963185, 'spring-ai-alibaba-prompt/spring-ai-alibaba-prompt-nacos/src/test/java/com/alibaba/cloud/ai/prompt/ConfigurablePromptTemplateFactoryTests.java:0-30': 1.4660390745170708, 'spring-ai-alibaba-prompt/spring-ai-alibaba-prompt-nacos/src/test/java/com/alibaba/cloud/ai/prompt/ConfigurablePromptTemplateFactoryTests.java:25-55': 2.191232374304636, 'spring-ai-alibaba-prompt/spring-ai-alibaba-prompt-nacos/src/test/java/com/alibaba/cloud/ai/prompt/ConfigurablePromptTemplateFactoryTests.java:50-80': 2.537178094724767, 'spring-ai-alibaba-prompt/spring-ai-alibaba-prompt-nacos/src/test/java/com/alibaba/cloud/ai/prompt/ConfigurablePromptTemplateFactoryTests.java:75-105': 2.547739006255517, 'spring-ai-alibaba-prompt/spring-ai-alibaba-prompt-nacos/src/test/java/com/alibaba/cloud/ai/prompt/ConfigurablePromptTemplateFactoryTests.java:100-130': 2.013432484642509, 'spring-ai-alibaba-prompt/spring-ai-alibaba-prompt-nacos/src/main/java/com/alibaba/cloud/ai/prompt/ConfigurablePromptTemplateFactory.java:0-30': 1.4898286183391802, 'spring-ai-alibaba-prompt/spring-ai-alibaba-prompt-nacos/src/main/java/com/alibaba/cloud/ai/prompt/ConfigurablePromptTemplate.java:0-30': 1.4032971997212973, 'spring-ai-alibaba-prompt/spring-ai-alibaba-prompt-nacos/src/main/java/com/alibaba/cloud/ai/prompt/PromptTemplateBuilderConfigure.java:0-30': 1.6271269812152453, 'spring-ai-alibaba-prompt/spring-ai-alibaba-prompt-nacos/src/main/java/com/alibaba/cloud/ai/prompt/PromptTemplateCustomizer.java:0-30': 1.6156734531778587, 'community/README.md:0-30': 8.02086214480814, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-toutiaonews/src/test/java/com/alibaba/cloud/ai/toolcalling/toutiaonews/ToutiaoNewsSearchHotEventsTest.java:0-30': 1.4032971997212973, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-toutiaonews/src/main/java/com/alibaba/cloud/ai/toolcalling/toutiaonews/ToutiaoNewsConstants.java:0-30': 1.555453881109404, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-toutiaonews/src/main/java/com/alibaba/cloud/ai/toolcalling/toutiaonews/ToutiaoNewsProperties.java:0-30': 1.4946794771655387, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-toutiaonews/src/main/java/com/alibaba/cloud/ai/toolcalling/toutiaonews/ToutiaoNewsSearchHotEventsService.java:0-30': 1.4995620278218964, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-toutiaonews/src/main/java/com/alibaba/cloud/ai/toolcalling/toutiaonews/ToutiaoNewsAutoConfiguration.java:0-30': 1.3036670873065823, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-firecrawl/src/test/java/com/alibaba/cloud/ai/toolcalling/firecrawl/FireCrawlTest.java:0-30': 1.390544851406706, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-firecrawl/src/main/java/com/alibaba/cloud/ai/toolcalling/firecrawl/FireCrawlProperties.java:0-30': 1.4162856102690216, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-firecrawl/src/main/java/com/alibaba/cloud/ai/toolcalling/firecrawl/FireCrawlConstants.java:0-30': 1.461372044515705, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-firecrawl/src/main/java/com/alibaba/cloud/ai/toolcalling/firecrawl/FireCrawlService.java:0-30': 1.3863454179444579, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-firecrawl/src/main/java/com/alibaba/cloud/ai/toolcalling/firecrawl/FireCrawlFormatsEnum.java:0-30': 1.5987923371942092, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-firecrawl/src/main/java/com/alibaba/cloud/ai/toolcalling/firecrawl/FireCrawlAutoConfiguration.java:0-30': 1.4032971997212973, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-firecrawl/src/main/java/com/alibaba/cloud/ai/toolcalling/firecrawl/FireCrawlModeEnum.java:0-30': 2.720836156126353, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-firecrawl/src/main/java/com/alibaba/cloud/ai/toolcalling/firecrawl/FireCrawlModeEnum.java:25-55': 2.929863056080895, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-common/src/main/java/com/alibaba/cloud/ai/toolcalling/common/CommonToolCallProperties.java:0-30': 1.6156734531778587, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-common/src/main/java/com/alibaba/cloud/ai/toolcalling/common/JsonParseTool.java:0-30': 1.3738979381611398, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-common/src/main/java/com/alibaba/cloud/ai/toolcalling/common/JsonParseTool.java:200-230': 1.2013188213189965, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-common/src/main/java/com/alibaba/cloud/ai/toolcalling/common/JsonParseTool.java:225-255': 1.2172460758505306, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-common/src/main/java/com/alibaba/cloud/ai/toolcalling/common/CommonToolCallAutoConfiguration.java:0-30': 1.5607422602136187, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-common/src/main/java/com/alibaba/cloud/ai/toolcalling/common/CommonToolCallUtils.java:0-30': 1.5502012189992864, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-common/src/main/java/com/alibaba/cloud/ai/toolcalling/common/CommonToolCallUtils.java:75-105': 1.5714276365890276, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-common/src/main/java/com/alibaba/cloud/ai/toolcalling/common/RestClientTool.java:0-30': 1.2999753052429273, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-common/src/main/java/com/alibaba/cloud/ai/toolcalling/common/RestClientTool.java:50-80': 1.2336013365262284, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-common/src/main/java/com/alibaba/cloud/ai/toolcalling/common/CommonToolCallConstants.java:0-30': 1.5244612091889669, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-common/src/main/java/com/alibaba/cloud/ai/toolcalling/common/CommonToolCallConstants.java:50-80': 4.06032460869417, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-common/src/main/java/com/alibaba/cloud/ai/toolcalling/common/CommonToolCallConstants.java:75-105': 5.304366722927759, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-common/src/main/java/com/alibaba/cloud/ai/toolcalling/common/WebClientTool.java:0-30': 3.701548949793365, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-common/src/main/java/com/alibaba/cloud/ai/toolcalling/common/WebClientTool.java:50-80': 2.605025170944165, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-common/src/main/java/com/alibaba/cloud/ai/toolcalling/common/WebClientTool.java:75-105': 4.661966677646936, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-common/src/main/java/com/alibaba/cloud/ai/toolcalling/common/interfaces/SearchService.java:0-30': 1.4429973314407667, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-tencentmap/src/test/java/com/alibaba/cloud/ai/toolcalling/tencentmap/TencentMapWeatherServiceTest.java:0-30': 1.3616719918917644, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-tencentmap/src/main/java/com/alibaba/cloud/ai/toolcalling/tencentmap/TencentMapAutoConfiguration.java:0-30': 1.4429973314407667, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-tencentmap/src/main/java/com/alibaba/cloud/ai/toolcalling/tencentmap/TencentMapProperties.java:0-30': 1.4660390745170708, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-tencentmap/src/main/java/com/alibaba/cloud/ai/toolcalling/tencentmap/TencentMapConstants.java:0-30': 1.4429973314407667, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-tencentmap/src/main/java/com/alibaba/cloud/ai/toolcalling/tencentmap/TencentMapWeatherService.java:0-30': 1.461372044515705, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-serpapi/src/test/java/com/alibaba/cloud/ai/toolcalling/serpapi/SerpApiServiceTest.java:0-30': 1.307379897570568, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-serpapi/src/main/java/com/alibaba/cloud/ai/toolcalling/serpapi/SerpApiConstants.java:0-30': 1.4995620278218964, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-serpapi/src/main/java/com/alibaba/cloud/ai/toolcalling/serpapi/SerpApiService.java:0-30': 1.2999753052429273, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-serpapi/src/main/java/com/alibaba/cloud/ai/toolcalling/serpapi/SerpApiProperties.java:0-30': 1.4475475537922684, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-serpapi/src/main/java/com/alibaba/cloud/ai/toolcalling/serpapi/SerpApiAutoConfiguration.java:0-30': 1.4076001190620582, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-bravesearch/src/test/java/com/alibaba/cloud/ai/toolcalling/bravesearch/BraveSearchTest.java:0-30': 1.31111391621134, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-bravesearch/src/main/java/com/alibaba/cloud/ai/toolcalling/bravesearch/BraveSearchProperties.java:0-30': 1.4250789533148378, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-bravesearch/src/main/java/com/alibaba/cloud/ai/toolcalling/bravesearch/BraveSearchService.java:0-30': 1.3378614571544742, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-bravesearch/src/main/java/com/alibaba/cloud/ai/toolcalling/bravesearch/BraveSearchService.java:25-55': 3.9121453947683715, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-bravesearch/src/main/java/com/alibaba/cloud/ai/toolcalling/bravesearch/BraveSearchConstants.java:0-30': 1.4339821696206667, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-bravesearch/src/main/java/com/alibaba/cloud/ai/toolcalling/bravesearch/BraveSearchAutoConfiguration.java:0-30': 1.3301085874193976, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-yuque/src/test/java/com/alibaba/cloud/ai/toolcalling/yuque/YuqueTest.java:0-30': 1.4206686751462156, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-yuque/src/main/java/com/alibaba/cloud/ai/toolcalling/yuque/YuqueUpdateDocService.java:0-30': 1.4429973314407667, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-yuque/src/main/java/com/alibaba/cloud/ai/toolcalling/yuque/YuqueDeleteDocService.java:0-30': 1.4429973314407667, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-yuque/src/main/java/com/alibaba/cloud/ai/toolcalling/yuque/YuqueQueryDocService.java:0-30': 1.4429973314407667, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-yuque/src/main/java/com/alibaba/cloud/ai/toolcalling/yuque/YuqueQueryBookService.java:0-30': 1.5194154424953983, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-yuque/src/main/java/com/alibaba/cloud/ai/toolcalling/yuque/YuqueAutoConfiguration.java:0-30': 1.4162856102690216, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-yuque/src/main/java/com/alibaba/cloud/ai/toolcalling/yuque/YuqueUpdateBookService.java:0-30': 1.4384756257963185, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-yuque/src/main/java/com/alibaba/cloud/ai/toolcalling/yuque/YuqueProperties.java:0-30': 1.461372044515705, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-yuque/src/main/java/com/alibaba/cloud/ai/toolcalling/yuque/YuqueConstants.java:0-30': 1.461372044515705, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-yuque/src/main/java/com/alibaba/cloud/ai/toolcalling/yuque/YuqueCreateDocService.java:0-30': 1.4384756257963185, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-yuque/src/main/java/com/alibaba/cloud/ai/toolcalling/yuque/YuqueDeleteBookService.java:0-30': 1.4429973314407667, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-yuque/src/main/java/com/alibaba/cloud/ai/toolcalling/yuque/YuqueCreateBookService.java:0-30': 1.4384756257963185, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-weather/src/test/java/com/alibaba/cloud/ai/toolcalling/weather/WeatherServiceTest.java:0-30': 1.3417718832367183, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-weather/src/main/java/com/alibaba/cloud/ai/toolcalling/weather/WeatherConstants.java:0-30': 1.5044765819016563, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-weather/src/main/java/com/alibaba/cloud/ai/toolcalling/weather/WeatherAutoConfiguration.java:0-30': 1.4162856102690216, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-weather/src/main/java/com/alibaba/cloud/ai/toolcalling/weather/WeatherService.java:0-30': 1.2999753052429273, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-weather/src/main/java/com/alibaba/cloud/ai/toolcalling/weather/WeatherProperties.java:0-30': 1.461372044515705, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-microsofttranslate/src/main/java/com/alibaba/cloud/ai/toolcalling/microsofttranslate/MicroSoftTranslateAutoConfiguration.java:0-30': 1.3863454179444579, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-microsofttranslate/src/main/java/com/alibaba/cloud/ai/toolcalling/microsofttranslate/MicroSoftTranslateProperties.java:0-30': 1.4898286183391802, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-microsofttranslate/src/main/java/com/alibaba/cloud/ai/toolcalling/microsofttranslate/MicroSoftTranslateService.java:0-30': 1.3378614571544742, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-microsofttranslate/src/main/java/com/alibaba/cloud/ai/toolcalling/microsofttranslate/MicroSoftTranslateConstants.java:0-30': 1.4850091437813338, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-baidumap/src/test/java/com/alibaba/cloud/ai/toolcalling/baidumap/BaiduMapTest.java:0-30': 1.3576448869712883, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-baidumap/src/main/java/com/alibaba/cloud/ai/toolcalling/baidumap/BaiduMapConstants.java:0-30': 1.4567346345301488, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-baidumap/src/main/java/com/alibaba/cloud/ai/toolcalling/baidumap/BaiduMapSearchInfoService.java:0-30': 1.4429973314407667, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-baidumap/src/main/java/com/alibaba/cloud/ai/toolcalling/baidumap/BaiDuMapAutoConfiguration.java:0-30': 1.452126563470489, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-baidumap/src/main/java/com/alibaba/cloud/ai/toolcalling/baidumap/BaiDuMapAutoConfiguration.java:25-55': 1.2237358609737488, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-baidumap/src/main/java/com/alibaba/cloud/ai/toolcalling/baidumap/BaiDuMapWeatherService.java:0-30': 1.3697983017004274, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-baidumap/src/main/java/com/alibaba/cloud/ai/toolcalling/baidumap/BaiDuMapWeatherService.java:25-55': 2.549955096050456, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-baidumap/src/main/java/com/alibaba/cloud/ai/toolcalling/baidumap/BaiDuMapWeatherService.java:50-80': 5.535896052652842, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-baidumap/src/main/java/com/alibaba/cloud/ai/toolcalling/baidumap/BaiDuMapWeatherService.java:75-105': 8.456161224415098, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-baidumap/src/main/java/com/alibaba/cloud/ai/toolcalling/baidumap/BaiDuMapWeatherService.java:100-130': 8.77930895981726, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-baidumap/src/main/java/com/alibaba/cloud/ai/toolcalling/baidumap/BaiDuMapProperties.java:0-30': 1.4850091437813338, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-baidumap/src/main/java/com/alibaba/cloud/ai/toolcalling/baidumap/BaiDuMapTools.java:0-30': 1.4295166990043104, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-baidumap/src/main/java/com/alibaba/cloud/ai/toolcalling/baidumap/BaiDuMapTools.java:25-55': 2.597012834261719, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-baidumap/src/main/java/com/alibaba/cloud/ai/toolcalling/baidumap/BaiDuMapTools.java:50-80': 2.4324208558455553, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-baidumap/src/main/java/com/alibaba/cloud/ai/toolcalling/baidumap/BaiDuMapTools.java:75-105': 4.940115612745559, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-baidumap/src/main/java/com/alibaba/cloud/ai/toolcalling/baidumap/BaiDuMapTools.java:100-130': 2.439448399059616, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-duckduckgo/src/test/java/com/alibaba/cloud/ai/toolcalling/duckduckgo/DuckDuckGoTest.java:0-30': 1.4206686751462156, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-duckduckgo/src/main/java/com/alibaba/cloud/ai/toolcalling/duckduckgo/DuckDuckGoProperties.java:0-30': 1.452126563470489, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-duckduckgo/src/main/java/com/alibaba/cloud/ai/toolcalling/duckduckgo/DuckDuckGoAutoConfiguration.java:0-30': 1.3738979381611398, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-duckduckgo/src/main/java/com/alibaba/cloud/ai/toolcalling/duckduckgo/DuckDuckGoConstants.java:0-30': 1.5044765819016563, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-duckduckgo/src/main/java/com/alibaba/cloud/ai/toolcalling/duckduckgo/DuckDuckGoQueryNewsService.java:0-30': 1.3657230586340428, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-searches/src/main/java/com/alibaba/cloud/ai/toolcalling/searches/SearchUtil.java:0-30': 1.6100069440831988, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-searches/src/main/java/com/alibaba/cloud/ai/toolcalling/searches/SearchEnum.java:0-30': 1.4660390745170708, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-aliyunaisearch/src/test/java/com/alibaba/cloud/ai/toolcalling/aliyunaisearch/AliyunAiSearchTest.java:0-30': 1.31111391621134, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-aliyunaisearch/src/main/java/com/alibaba/cloud/ai/toolcalling/aliyunaisearch/AliyunAiSearchProperties.java:0-30': 2.0178576739880874, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-aliyunaisearch/src/main/java/com/alibaba/cloud/ai/toolcalling/aliyunaisearch/AliyunAiSearchService.java:0-30': 1.2854149260948562, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-aliyunaisearch/src/main/java/com/alibaba/cloud/ai/toolcalling/aliyunaisearch/AliyunAiSearchService.java:75-105': 2.215716752354985, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-aliyunaisearch/src/main/java/com/alibaba/cloud/ai/toolcalling/aliyunaisearch/AliyunAiSearchAutoConfiguration.java:0-30': 1.3301085874193976, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-aliyunaisearch/src/main/java/com/alibaba/cloud/ai/toolcalling/aliyunaisearch/AliyunAiSearchConstants.java:0-30': 1.4429973314407667, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-jsonprocessor/src/test/java/com/alibaba/cloud/ai/toolcalling/jsonprocessor/JsonProcessorReplaceServiceTest.java:0-30': 1.3536415319003627, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-jsonprocessor/src/test/java/com/alibaba/cloud/ai/toolcalling/jsonprocessor/JsonProcessorReplaceServiceTest.java:75-105': 2.2568777837603804, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-jsonprocessor/src/test/java/com/alibaba/cloud/ai/toolcalling/jsonprocessor/JsonProcessorRemoveServiceTest.java:0-30': 1.390544851406706, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-jsonprocessor/src/test/java/com/alibaba/cloud/ai/toolcalling/jsonprocessor/JsonProcessorRemoveServiceTest.java:50-80': 1.2237358609737488, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-jsonprocessor/src/test/java/com/alibaba/cloud/ai/toolcalling/jsonprocessor/JsonProcessorRemoveServiceTest.java:75-105': 1.869906890424954, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-jsonprocessor/src/test/java/com/alibaba/cloud/ai/toolcalling/jsonprocessor/JsonProcessorRemoveServiceTest.java:100-130': 1.9008788485373251, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-jsonprocessor/src/test/java/com/alibaba/cloud/ai/toolcalling/jsonprocessor/JsonProcessorRemoveServiceTest.java:125-155': 1.869906890424954, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-jsonprocessor/src/test/java/com/alibaba/cloud/ai/toolcalling/jsonprocessor/JsonProcessorInsertServiceTest.java:0-30': 1.3536415319003627, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-jsonprocessor/src/test/java/com/alibaba/cloud/ai/toolcalling/jsonprocessor/JsonProcessorInsertServiceTest.java:75-105': 3.411593750509827, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-jsonprocessor/src/test/java/com/alibaba/cloud/ai/toolcalling/jsonprocessor/JsonProcessorParseServiceTest.java:0-30': 1.4429973314407667, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-jsonprocessor/src/main/java/com/alibaba/cloud/ai/toolcalling/jsonprocessor/JsonProcessorAutoConfiguration.java:0-30': 1.4567346345301488, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-jsonprocessor/src/main/java/com/alibaba/cloud/ai/toolcalling/jsonprocessor/JsonProcessorConstants.java:0-30': 1.3697983017004274, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-jsonprocessor/src/main/java/com/alibaba/cloud/ai/toolcalling/jsonprocessor/JsonProcessorInsertService.java:0-30': 1.4707360092264137, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-jsonprocessor/src/main/java/com/alibaba/cloud/ai/toolcalling/jsonprocessor/JsonProcessorRemoveService.java:0-30': 1.4429973314407667, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-jsonprocessor/src/main/java/com/alibaba/cloud/ai/toolcalling/jsonprocessor/JsonProcessorProperties.java:0-30': 1.5714276365890276, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-jsonprocessor/src/main/java/com/alibaba/cloud/ai/toolcalling/jsonprocessor/JsonProcessorParseService.java:0-30': 1.4429973314407667, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-jsonprocessor/src/main/java/com/alibaba/cloud/ai/toolcalling/jsonprocessor/JsonProcessorReplaceService.java:0-30': 1.4707360092264137, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-sensitivefilter/src/test/java/com/alibaba/cloud/ai/toolcalling/sensitivefilter/SensitiveFilterAutoConfigurationTest.java:0-30': 1.4850091437813338, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-sensitivefilter/src/test/java/com/alibaba/cloud/ai/toolcalling/sensitivefilter/SensitiveFilterAutoConfigurationTest.java:125-155': 1.7622333697887298, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-sensitivefilter/src/test/java/com/alibaba/cloud/ai/toolcalling/sensitivefilter/SensitiveFilterAutoConfigurationTest.java:150-180': 2.013432484642509, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-sensitivefilter/src/test/java/com/alibaba/cloud/ai/toolcalling/sensitivefilter/TestApplication.java:0-30': 1.582260333065363, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-sensitivefilter/src/test/java/com/alibaba/cloud/ai/toolcalling/sensitivefilter/SensitiveFilterIntegrationTest.java:0-30': 1.3821712726179565, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-sensitivefilter/src/test/java/com/alibaba/cloud/ai/toolcalling/sensitivefilter/SensitiveFilterIntegrationTest.java:125-155': 2.0090266618268062, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-sensitivefilter/src/test/java/com/alibaba/cloud/ai/toolcalling/sensitivefilter/SensitiveFilterIntegrationTest.java:150-180': 2.791772705324631, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-sensitivefilter/src/test/java/com/alibaba/cloud/ai/toolcalling/sensitivefilter/SensitiveFilterIntegrationTest.java:175-205': 1.9117238727444157, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-sensitivefilter/src/test/java/com/alibaba/cloud/ai/toolcalling/sensitivefilter/SensitiveFilterServiceTest.java:0-30': 1.555453881109404, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-sensitivefilter/src/test/java/com/alibaba/cloud/ai/toolcalling/sensitivefilter/SensitiveFilterServiceTest.java:150-180': 2.0961775092284123, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-sensitivefilter/src/test/java/com/alibaba/cloud/ai/toolcalling/sensitivefilter/SensitiveFilterPropertiesTest.java:0-30': 1.555453881109404, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-sensitivefilter/src/main/java/com/alibaba/cloud/ai/toolcalling/sensitivefilter/SensitiveFilterProperties.java:0-30': 1.5502012189992864, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-sensitivefilter/src/main/java/com/alibaba/cloud/ai/toolcalling/sensitivefilter/SensitiveFilterService.java:0-30': 1.6100069440831988, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-sensitivefilter/src/main/java/com/alibaba/cloud/ai/toolcalling/sensitivefilter/SensitiveFilterService.java:75-105': 1.5607422602136187, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-sensitivefilter/src/main/java/com/alibaba/cloud/ai/toolcalling/sensitivefilter/SensitiveFilterAutoConfiguration.java:0-30': 1.4995620278218964, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-sensitivefilter/src/main/java/com/alibaba/cloud/ai/toolcalling/sensitivefilter/SensitiveFilterAutoConfiguration.java:25-55': 1.712085963376952, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-sensitivefilter/src/main/java/com/alibaba/cloud/ai/toolcalling/sensitivefilter/SensitiveFilterConstants.java:0-30': 1.5607422602136187, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-kuaidi100/src/test/java/com/alibaba/cloud/ai/toolcalling/kuaidi100/Kuaidi100Test.java:0-30': 1.3339737577322137, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-kuaidi100/src/main/java/com/alibaba/cloud/ai/toolcalling/kuaidi100/Kuaidi100Constants.java:0-30': 1.4660390745170708, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-kuaidi100/src/main/java/com/alibaba/cloud/ai/toolcalling/kuaidi100/Kuaidi100Service.java:0-30': 1.307379897570568, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-kuaidi100/src/main/java/com/alibaba/cloud/ai/toolcalling/kuaidi100/Kuaidi100Service.java:75-105': 2.1208279864009265, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-kuaidi100/src/main/java/com/alibaba/cloud/ai/toolcalling/kuaidi100/Kuaidi100Service.java:100-130': 4.433915991976864, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-kuaidi100/src/main/java/com/alibaba/cloud/ai/toolcalling/kuaidi100/Kuaidi100Service.java:125-155': 4.601489414098681, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-kuaidi100/src/main/java/com/alibaba/cloud/ai/toolcalling/kuaidi100/Kuaidi100Exception.java:0-30': 1.6994097969724125, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-kuaidi100/src/main/java/com/alibaba/cloud/ai/toolcalling/kuaidi100/Kuaidi100Properties.java:0-30': 2.375000193454486, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-kuaidi100/src/main/java/com/alibaba/cloud/ai/toolcalling/kuaidi100/Kuaidi100AutoConfiguration.java:0-30': 1.2782563680468169, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-jinacrawler/src/test/java/com/alibaba/cloud/ai/toolcalling/jinacrawler/JinaCrawlerTest.java:0-30': 1.390544851406706, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-jinacrawler/src/main/java/com/alibaba/cloud/ai/toolcalling/jinacrawler/JinaCrawlerConstants.java:0-30': 1.461372044515705, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-jinacrawler/src/main/java/com/alibaba/cloud/ai/toolcalling/jinacrawler/JinaCrawlerService.java:0-30': 1.3496617171995993, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-jinacrawler/src/main/java/com/alibaba/cloud/ai/toolcalling/jinacrawler/JinaCrawlerProperties.java:0-30': 1.3339737577322137, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-jinacrawler/src/main/java/com/alibaba/cloud/ai/toolcalling/jinacrawler/JinaCrawlerAutoConfiguration.java:0-30': 1.3616719918917644, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-dingtalk/src/test/java/com/alibaba/cloud/ai/toolcalling/dingtalk/DingTalkRobotTest.java:0-30': 1.3301085874193976, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-dingtalk/src/main/java/com/alibaba/cloud/ai/toolcalling/dingtalk/SignTools.java:0-30': 1.555453881109404, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-dingtalk/src/main/java/com/alibaba/cloud/ai/toolcalling/dingtalk/DingTalkAutoConfiguration.java:0-30': 1.4119295075815892, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-dingtalk/src/main/java/com/alibaba/cloud/ai/toolcalling/dingtalk/DingTalkRobotService.java:0-30': 1.4076001190620582, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-dingtalk/src/main/java/com/alibaba/cloud/ai/toolcalling/dingtalk/DingTalkProperties.java:0-30': 1.461372044515705, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-dingtalk/src/main/java/com/alibaba/cloud/ai/toolcalling/dingtalk/DingTalkConstants.java:0-30': 1.4206686751462156, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-regex/src/test/java/com/alibaba/cloud/ai/toolcalling/regex/RegexServiceTest.java:0-30': 1.582260333065363, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-regex/src/main/java/com/alibaba/cloud/ai/toolcalling/regex/RegexService.java:0-30': 1.5714276365890276, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-regex/src/main/java/com/alibaba/cloud/ai/toolcalling/regex/RegexAutoConfiguration.java:0-30': 1.4429973314407667, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-regex/src/main/java/com/alibaba/cloud/ai/toolcalling/regex/RegexUtils.java:0-30': 1.6043800433693445, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-regex/src/main/java/com/alibaba/cloud/ai/toolcalling/regex/RegexUtils.java:25-55': 2.3010703669032884, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-regex/src/main/java/com/alibaba/cloud/ai/toolcalling/regex/RegexUtils.java:50-80': 1.813542198650695, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-regex/src/main/java/com/alibaba/cloud/ai/toolcalling/regex/RegexConstants.java:0-30': 1.5660667218567792, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-baidusearch/src/test/java/com/alibaba/cloud/ai/toolcalling/baidusearch/BaiduSearchTest.java:0-30': 1.2747069058480733, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-baidusearch/src/main/java/com/alibaba/cloud/ai/toolcalling/baidusearch/BaiduSearchConstants.java:0-30': 1.544983913259478, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-baidusearch/src/main/java/com/alibaba/cloud/ai/toolcalling/baidusearch/BaiduSearchService.java:0-30': 1.3339737577322137, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-baidusearch/src/main/java/com/alibaba/cloud/ai/toolcalling/baidusearch/BaiduSearchProperties.java:0-30': 1.4995620278218964, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-baidusearch/src/main/java/com/alibaba/cloud/ai/toolcalling/baidusearch/BaiduSearchAutoConfiguration.java:0-30': 1.3738979381611398, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-tavilysearch/src/test/java/com/alibaba/cloud/ai/toolcalling/tavily/TavilySearchServiceTest.java:0-30': 1.307379897570568, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-tavilysearch/src/main/java/com/alibaba/cloud/ai/toolcalling/tavily/TavilySearchProperties.java:0-30': 1.4032971997212973, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-tavilysearch/src/main/java/com/alibaba/cloud/ai/toolcalling/tavily/TavilySearchAutoConfiguration.java:0-30': 1.3576448869712883, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-tavilysearch/src/main/java/com/alibaba/cloud/ai/toolcalling/tavily/TavilySearchService.java:0-30': 1.2676667917202797, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-tavilysearch/src/main/java/com/alibaba/cloud/ai/toolcalling/tavily/TavilySearchService.java:75-105': 1.7524339964579523, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-tavilysearch/src/main/java/com/alibaba/cloud/ai/toolcalling/tavily/TavilySearchService.java:100-130': 1.564092507749316, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-tavilysearch/src/main/java/com/alibaba/cloud/ai/toolcalling/tavily/TavilySearchConstants.java:0-30': 1.4995620278218964, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-tushare/src/test/java/com/alibaba/cloud/ai/toolcalling/tushare/TushareStockQuotesServiceTest.java:0-30': 1.4339821696206667, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-tushare/src/test/java/com/alibaba/cloud/ai/toolcalling/tushare/TushareStockQuotesServiceTest.java:25-55': 3.3374277194355466, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-tushare/src/main/java/com/alibaba/cloud/ai/toolcalling/tushare/TushareStockQuotesService.java:0-30': 1.4850091437813338, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-tushare/src/main/java/com/alibaba/cloud/ai/toolcalling/tushare/TushareStockQuotesService.java:25-55': 4.609402306053985, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-tushare/src/main/java/com/alibaba/cloud/ai/toolcalling/tushare/TushareStockQuotesService.java:50-80': 5.0038004796961895, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-tushare/src/main/java/com/alibaba/cloud/ai/toolcalling/tushare/TushareAutoConfiguration.java:0-30': 1.4429973314407667, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-tushare/src/main/java/com/alibaba/cloud/ai/toolcalling/tushare/TushareConstants.java:0-30': 1.4754631369960434, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-tushare/src/main/java/com/alibaba/cloud/ai/toolcalling/tushare/TushareProperties.java:0-30': 1.4995620278218964, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-time/src/test/java/com/alibaba/cloud/ai/toolcalling/time/TimeServiceTest.java:0-30': 1.4898286183391802, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-time/src/test/java/com/alibaba/cloud/ai/toolcalling/time/GetTimeByZoneIdServiceTest.java:0-30': 1.4567346345301488, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-time/src/test/java/com/alibaba/cloud/ai/toolcalling/time/TimeUtilsTest.java:0-30': 1.5660667218567792, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-time/src/main/java/com/alibaba/cloud/ai/toolcalling/time/TimeProperties.java:0-30': 1.582260333065363, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-time/src/main/java/com/alibaba/cloud/ai/toolcalling/time/GetTimeByZoneIdService.java:0-30': 1.4850091437813338, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-time/src/main/java/com/alibaba/cloud/ai/toolcalling/time/GetTimeByZoneIdService.java:25-55': 1.5144029672636237, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-time/src/main/java/com/alibaba/cloud/ai/toolcalling/time/TimeConstants.java:0-30': 1.5607422602136187, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-time/src/main/java/com/alibaba/cloud/ai/toolcalling/time/TimeUtils.java:0-30': 1.576825380051758, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-time/src/main/java/com/alibaba/cloud/ai/toolcalling/time/TimeService.java:0-30': 1.539801608104721, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-time/src/main/java/com/alibaba/cloud/ai/toolcalling/time/TimeAutoConfiguration.java:0-30': 1.461372044515705, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-baidutranslate/src/test/java/com/alibaba/cloud/ai/toolcalling/baidutranslate/BaiduTranslateTest.java:0-30': 1.4032971997212973, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-baidutranslate/src/main/java/com/alibaba/cloud/ai/toolcalling/baidutranslate/BaiduTranslateService.java:0-30': 1.4032971997212973, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-baidutranslate/src/main/java/com/alibaba/cloud/ai/toolcalling/baidutranslate/BaiduTranslateService.java:50-80': 1.2818256526239191, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-baidutranslate/src/main/java/com/alibaba/cloud/ai/toolcalling/baidutranslate/BaiduTranslateService.java:100-130': 6.862036298665765, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-baidutranslate/src/main/java/com/alibaba/cloud/ai/toolcalling/baidutranslate/BaiduTranslateConstants.java:0-30': 1.4384756257963185, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-baidutranslate/src/main/java/com/alibaba/cloud/ai/toolcalling/baidutranslate/BaiduTranslateAutoConfiguration.java:0-30': 1.452126563470489, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-baidutranslate/src/main/java/com/alibaba/cloud/ai/toolcalling/baidutranslate/BaiduTranslateProperties.java:0-30': 1.461372044515705, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-sinanews/src/test/java/com/alibaba/cloud/ai/toolcalling/sinanews/SinaNewsTest.java:0-30': 1.4162856102690216, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-sinanews/src/main/java/com/alibaba/cloud/ai/toolcalling/sinanews/SinaNewsAutoConfiguration.java:0-30': 1.3036670873065823, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-sinanews/src/main/java/com/alibaba/cloud/ai/toolcalling/sinanews/SinaNewsConstants.java:0-30': 1.555453881109404, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-sinanews/src/main/java/com/alibaba/cloud/ai/toolcalling/sinanews/SinaNewsProperties.java:0-30': 1.5044765819016563, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-sinanews/src/main/java/com/alibaba/cloud/ai/toolcalling/sinanews/SinaNewsService.java:0-30': 1.4995620278218964, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-amap/src/test/java/com/alibaba/cloud/ai/toolcalling/amp/WeatherSearchServiceTest.java:0-30': 1.3576448869712883, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-amap/src/main/java/com/alibaba/cloud/ai/toolcalling/amp/WeatherSearchService.java:0-30': 1.461372044515705, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-amap/src/main/java/com/alibaba/cloud/ai/toolcalling/amp/WeatherSearchService.java:25-55': 2.5654503720600963, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-amap/src/main/java/com/alibaba/cloud/ai/toolcalling/amp/WeatherSearchService.java:50-80': 5.182696187757637, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-amap/src/main/java/com/alibaba/cloud/ai/toolcalling/amp/WeatherSearchService.java:75-105': 2.794715588524507, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-amap/src/main/java/com/alibaba/cloud/ai/toolcalling/amp/AmapProperties.java:0-30': 1.4475475537922684, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-amap/src/main/java/com/alibaba/cloud/ai/toolcalling/amp/AmapAutoConfiguration.java:0-30': 1.4384756257963185, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-amap/src/main/java/com/alibaba/cloud/ai/toolcalling/amp/AmapConstants.java:0-30': 1.4802207498974258, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-youdaotranslate/src/test/java/com/alibaba/cloud/ai/toolcalling/youdaotranslate/YoudaotranslateTest.java:0-30': 1.4032971997212973, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-youdaotranslate/src/main/java/com/alibaba/cloud/ai/toolcalling/youdaotranslate/YoudaoTranslateAutoConfiguration.java:0-30': 1.4119295075815892, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-youdaotranslate/src/main/java/com/alibaba/cloud/ai/toolcalling/youdaotranslate/AuthTools.java:0-30': 1.4707360092264137, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-youdaotranslate/src/main/java/com/alibaba/cloud/ai/toolcalling/youdaotranslate/YoudaoTranslateProperties.java:0-30': 1.452126563470489, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-youdaotranslate/src/main/java/com/alibaba/cloud/ai/toolcalling/youdaotranslate/YoudaoTranslateConstants.java:0-30': 1.4567346345301488, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-youdaotranslate/src/main/java/com/alibaba/cloud/ai/toolcalling/youdaotranslate/YoudaoTranslateService.java:0-30': 1.3301085874193976, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-youdaotranslate/src/main/java/com/alibaba/cloud/ai/toolcalling/youdaotranslate/YoudaoTranslateService.java:100-130': 3.3095640736112206, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-larksuite/src/main/java/com/alibaba/cloud/ai/toolcalling/larksuite/LarkSuiteChatService.java:0-30': 1.4032971997212973, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-larksuite/src/main/java/com/alibaba/cloud/ai/toolcalling/larksuite/LarkSuiteChatService.java:50-80': 3.752758037019071, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-larksuite/src/main/java/com/alibaba/cloud/ai/toolcalling/larksuite/LarkSuiteAutoConfiguration.java:0-30': 1.461372044515705, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-larksuite/src/main/java/com/alibaba/cloud/ai/toolcalling/larksuite/LarkSuiteAutoConfiguration.java:25-55': 1.3657230586340428, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-larksuite/src/main/java/com/alibaba/cloud/ai/toolcalling/larksuite/LarkSuiteProperties.java:0-30': 1.6624830670303476, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-larksuite/src/main/java/com/alibaba/cloud/ai/toolcalling/larksuite/LarkSuiteCreateSheetService.java:0-30': 1.2108247615986183, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-larksuite/src/main/java/com/alibaba/cloud/ai/toolcalling/larksuite/LarkSuiteCreateDocService.java:0-30': 1.4119295075815892, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-larksuite/src/main/java/com/alibaba/cloud/ai/toolcalling/larksuite/LarkSuiteCreateDocService.java:50-80': 3.555241762302633, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-larksuite/src/main/java/com/alibaba/cloud/ai/toolcalling/larksuite/LarkSuiteGetDocContentService.java:0-30': 1.394769803506785, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-larksuite/src/main/java/com/alibaba/cloud/ai/toolcalling/larksuite/LarkSuiteGetDocContentService.java:50-80': 3.6632115760345845, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-larksuite/src/main/java/com/alibaba/cloud/ai/toolcalling/larksuite/param/resp/ValuesAppendRespBodyUpdates.java:0-30': 1.6505282088323439, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-larksuite/src/main/java/com/alibaba/cloud/ai/toolcalling/larksuite/param/resp/ValuesAppendRespBody.java:0-30': 1.6100069440831988, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-larksuite/src/main/java/com/alibaba/cloud/ai/toolcalling/larksuite/param/resp/ValuesAppendResp.java:0-30': 1.6624830670303476, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-larksuite/src/main/java/com/alibaba/cloud/ai/toolcalling/larksuite/param/req/ValuesAppendReq.java:0-30': 1.5714276365890276, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-larksuite/src/main/java/com/alibaba/cloud/ai/toolcalling/larksuite/param/req/ValueRange.java:0-30': 1.638744057033641, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-larksuite/src/main/java/com/alibaba/cloud/ai/toolcalling/larksuite/param/req/ValuesAppendReqBody.java:0-30': 1.5714276365890276, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-alitranslate/src/test/java/com/alibaba/cloud/ai/toolcalling/alitranslate/AliTranslateTest.java:0-30': 1.3990205075566946, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-alitranslate/src/main/java/com/alibaba/cloud/ai/toolcalling/alitranslate/AliTranslateAutoConfiguration.java:0-30': 1.461372044515705, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-alitranslate/src/main/java/com/alibaba/cloud/ai/toolcalling/alitranslate/AliTranslateProperties.java:0-30': 1.4802207498974258, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-alitranslate/src/main/java/com/alibaba/cloud/ai/toolcalling/alitranslate/AliTranslateService.java:0-30': 1.271177101356609, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-alitranslate/src/main/java/com/alibaba/cloud/ai/toolcalling/alitranslate/AliTranslateService.java:75-105': 5.1954521216457525, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-alitranslate/src/main/java/com/alibaba/cloud/ai/toolcalling/alitranslate/AliTranslateService.java:100-130': 5.16711314015796, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-alitranslate/src/main/java/com/alibaba/cloud/ai/toolcalling/alitranslate/AliTranslateService.java:125-155': 5.0713202963005415, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-alitranslate/src/main/java/com/alibaba/cloud/ai/toolcalling/alitranslate/AliTranslateConstants.java:0-30': 1.4754631369960434, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-alitranslate/src/main/java/com/alibaba/cloud/ai/toolcalling/alitranslate/AliTranslateConstants.java:25-55': 5.747608834688467, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-githubtoolkit/src/main/java/com/alibaba/cloud/ai/toolcalling/githubtoolkit/SearchRepositoryService.java:0-30': 1.3262657509484723, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-githubtoolkit/src/main/java/com/alibaba/cloud/ai/toolcalling/githubtoolkit/GetIssueService.java:0-30': 1.3576448869712883, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-githubtoolkit/src/main/java/com/alibaba/cloud/ai/toolcalling/githubtoolkit/GithubToolKitAutoConfiguration.java:0-30': 1.3821712726179565, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-githubtoolkit/src/main/java/com/alibaba/cloud/ai/toolcalling/githubtoolkit/Response.java:0-30': 1.70572432962589, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-githubtoolkit/src/main/java/com/alibaba/cloud/ai/toolcalling/githubtoolkit/CreatePullRequestService.java:0-30': 1.4295166990043104, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-githubtoolkit/src/main/java/com/alibaba/cloud/ai/toolcalling/githubtoolkit/CreatePullRequestService.java:100-130': 1.473710620433528, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-githubtoolkit/src/main/java/com/alibaba/cloud/ai/toolcalling/githubtoolkit/CreatePullRequestService.java:125-155': 2.306852044848528, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-githubtoolkit/src/main/java/com/alibaba/cloud/ai/toolcalling/githubtoolkit/GithubToolKitProperties.java:0-30': 1.4946794771655387, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-githubtoolkit/src/main/java/com/alibaba/cloud/ai/toolcalling/githubtoolkit/GithubToolKitConstants.java:0-30': 1.3863454179444579, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-googletranslate/src/main/java/com/alibaba/cloud/ai/toolcalling/googletranslate/GoogleTranslateService.java:0-30': 1.3378614571544742, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-googletranslate/src/main/java/com/alibaba/cloud/ai/toolcalling/googletranslate/GoogleTranslateService.java:75-105': 4.6796552189150455, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-googletranslate/src/main/java/com/alibaba/cloud/ai/toolcalling/googletranslate/GoogleTranslateService.java:100-130': 5.142827253088269, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-googletranslate/src/main/java/com/alibaba/cloud/ai/toolcalling/googletranslate/GoogleTranslateProperties.java:0-30': 1.4295166990043104, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-googletranslate/src/main/java/com/alibaba/cloud/ai/toolcalling/googletranslate/GoogleTranslateConstants.java:0-30': 1.4995620278218964, 'community/tool-calls/spring-ai-alibaba-starter-tool-calling-googletranslate/src/main/java/com/alibaba/cloud/ai/toolcalling/googletranslate/GoogleTranslateAutoConfiguration.java:0-30': 1.4475475537922684, 'community/document-parsers/spring-ai-alibaba-starter-document-parser-bibtex/src/test/java/com/alibaba/cloud/ai/parser/bibtex/BibtexDocumentParserTest.java:0-30': 1.3863454179444579, 'community/document-parsers/spring-ai-alibaba-starter-document-parser-bibtex/src/test/java/com/alibaba/cloud/ai/parser/bibtex/BibtexDocumentParserTest.java:25-55': 2.1009743252108466, 'community/document-parsers/spring-ai-alibaba-starter-document-parser-bibtex/src/test/java/com/alibaba/cloud/ai/parser/bibtex/BibtexDocumentParserTest.java:50-80': 1.2890243568454003, 'community/document-parsers/spring-ai-alibaba-starter-document-parser-bibtex/src/main/java/com/alibaba/cloud/ai/parser/bibtex/BibtexDocumentParser.java:0-30': 1.3697983017004274, 'community/document-parsers/spring-ai-alibaba-starter-document-parser-bibtex/src/main/java/com/alibaba/cloud/ai/parser/bibtex/BibtexDocumentParser.java:125-155': 1.4898286183391802, 'community/document-parsers/spring-ai-alibaba-starter-document-parser-bshtml/src/test/java/com/alibaba/cloud/ai/parser/bshtml/BsHtmlDocumentParserTest.java:0-30': 1.4754631369960434, 'community/document-parsers/spring-ai-alibaba-starter-document-parser-bshtml/src/test/java/com/alibaba/cloud/ai/parser/bshtml/BsHtmlDocumentParserTest.java:25-55': 2.3229221506818423, 'community/document-parsers/spring-ai-alibaba-starter-document-parser-bshtml/src/test/java/com/alibaba/cloud/ai/parser/bshtml/BsHtmlDocumentParserTest.java:50-80': 1.2641758158808274, 'community/document-parsers/spring-ai-alibaba-starter-document-parser-bshtml/src/test/java/com/alibaba/cloud/ai/parser/bshtml/BsHtmlDocumentParserTest.java:100-130': 1.2676667917202797, 'community/document-parsers/spring-ai-alibaba-starter-document-parser-bshtml/src/main/java/com/alibaba/cloud/ai/parser/bshtml/BsHtmlDocumentParser.java:0-30': 1.6100069440831988, 'community/document-parsers/spring-ai-alibaba-starter-document-parser-yaml/src/test/java/com/alibaba/cloud/ai/parser/yaml/YamlDocumentParserTest.java:0-30': 1.5094234550964287, 'community/document-parsers/spring-ai-alibaba-starter-document-parser-yaml/src/test/java/com/alibaba/cloud/ai/parser/yaml/YamlDocumentParserTest.java:25-55': 1.2854149260948562, 'community/document-parsers/spring-ai-alibaba-starter-document-parser-yaml/src/main/java/com/alibaba/cloud/ai/parser/yaml/YamlDocumentParser.java:0-30': 1.4898286183391802, 'community/document-parsers/spring-ai-alibaba-starter-document-parser-yaml/src/main/java/com/alibaba/cloud/ai/parser/yaml/YamlDocumentParser.java:50-80': 1.5714276365890276, 'community/document-parsers/spring-ai-alibaba-starter-document-parser-yaml/src/main/java/com/alibaba/cloud/ai/parser/yaml/YamlDocumentParser.java:75-105': 1.6329148574925245, 'community/document-parsers/spring-ai-alibaba-starter-document-parser-directory/src/test/resources/4.py:0-30': 1.9037955790598078, 'community/document-parsers/spring-ai-alibaba-starter-document-parser-directory/src/test/java/com/alibaba/cloud/ai/parser/directory/DocumentDirectoryParserTest.java:0-30': 1.6746123679952611, 'community/document-parsers/spring-ai-alibaba-starter-document-parser-directory/src/main/java/com/alibaba/cloud/ai/parser/directory/DocumentDirectoryParser.java:0-30': 1.587732881718793, 'community/document-parsers/spring-ai-alibaba-starter-document-parser-tika/src/test/java/com/alibaba/cloud/ai/parser/tika/ApacheTikaDocumentParserTest.java:0-30': 1.4429973314407667, 'community/document-parsers/spring-ai-alibaba-starter-document-parser-tika/src/test/java/com/alibaba/cloud/ai/parser/tika/ApacheTikaDocumentParserTest.java:25-55': 1.2890243568454003, 'community/document-parsers/spring-ai-alibaba-starter-document-parser-tika/src/test/java/com/alibaba/cloud/ai/parser/tika/ApacheTikaDocumentParserTest.java:100-130': 1.2747069058480733, 'community/document-parsers/spring-ai-alibaba-starter-document-parser-tika/src/test/java/com/alibaba/cloud/ai/parser/tika/ApacheTikaDocumentParserTest.java:125-155': 2.0759859974294437, 'community/document-parsers/spring-ai-alibaba-starter-document-parser-tika/src/main/java/com/alibaba/cloud/ai/parser/tika/TikaDocumentParser.java:0-30': 1.3821712726179565, 'community/document-parsers/spring-ai-alibaba-starter-document-parser-tika/src/main/java/com/alibaba/cloud/ai/parser/tika/TikaDocumentParser.java:25-55': 3.8286867440139845, 'community/document-parsers/spring-ai-alibaba-starter-document-parser-tika/src/main/java/com/alibaba/cloud/ai/parser/tika/TikaDocumentParser.java:75-105': 2.952335544232879, 'community/document-parsers/spring-ai-alibaba-starter-document-parser-multi-modality/src/test/java/com/alibaba/cloud/ai/parser/multi/ImageDashScopeParserTest.java:0-30': 1.576825380051758, 'community/document-parsers/spring-ai-alibaba-starter-document-parser-multi-modality/src/test/java/com/alibaba/cloud/ai/parser/multi/ImageDashScopeParserTest.java:25-55': 5.654123546036562, 'community/document-parsers/spring-ai-alibaba-starter-document-parser-multi-modality/src/test/java/com/alibaba/cloud/ai/parser/multi/SttDashScopeParserTest.java:0-30': 6.133808732206095, 'community/document-parsers/spring-ai-alibaba-starter-document-parser-multi-modality/src/test/java/com/alibaba/cloud/ai/parser/multi/SttDashScopeParserTest.java:25-55': 6.958205004731934, 'community/document-parsers/spring-ai-alibaba-starter-document-parser-multi-modality/src/main/java/com/alibaba/cloud/ai/parser/multi/SttDashScopeParser.java:0-30': 1.3576448869712883, 'community/document-parsers/spring-ai-alibaba-starter-document-parser-multi-modality/src/main/java/com/alibaba/cloud/ai/parser/multi/ImageDashScopeParser.java:0-30': 1.4206686751462156, 'community/document-parsers/spring-ai-alibaba-starter-document-parser-pdf-tables/src/test/java/com/alibaba/cloud/ai/parser/pdf/tables/PdfTablesParserTests.java:0-30': 1.4898286183391802, 'community/document-parsers/spring-ai-alibaba-starter-document-parser-pdf-tables/src/test/java/com/alibaba/cloud/ai/parser/pdf/tables/PdfTablesParserTests.java:100-130': 1.4567346345301488, 'community/document-parsers/spring-ai-alibaba-starter-document-parser-pdf-tables/src/main/java/com/alibaba/cloud/ai/parser/pdf/tables/PdfTablesParser.java:0-30': 1.5094234550964287, 'community/document-parsers/spring-ai-alibaba-starter-document-parser-markdown/src/test/resources/code.md:0-30': 4.5033226107269995, 'community/document-parsers/spring-ai-alibaba-starter-document-parser-markdown/src/test/java/com/alibaba/cloud/ai/parser/markdown/MarkdownDocumentParserTest.java:0-30': 1.4946794771655387, 'community/document-parsers/spring-ai-alibaba-starter-document-parser-markdown/src/test/java/com/alibaba/cloud/ai/parser/markdown/MarkdownDocumentParserTest.java:100-130': 1.0823558279648557, 'community/document-parsers/spring-ai-alibaba-starter-document-parser-markdown/src/test/java/com/alibaba/cloud/ai/parser/markdown/MarkdownDocumentParserTest.java:125-155': 4.404151327174372, 'community/document-parsers/spring-ai-alibaba-starter-document-parser-markdown/src/test/java/com/alibaba/cloud/ai/parser/markdown/MarkdownDocumentParserTest.java:150-180': 5.191283661477575, 'community/document-parsers/spring-ai-alibaba-starter-document-parser-markdown/src/test/java/com/alibaba/cloud/ai/parser/markdown/MarkdownDocumentParserTest.java:175-205': 4.404151327174372, 'community/document-parsers/spring-ai-alibaba-starter-document-parser-markdown/src/main/java/com/alibaba/cloud/ai/parser/markdown/MarkdownDocumentParser.java:0-30': 4.94670553640388, 'community/document-parsers/spring-ai-alibaba-starter-document-parser-markdown/src/main/java/com/alibaba/cloud/ai/parser/markdown/MarkdownDocumentParser.java:75-105': 1.731458800865721, 'community/document-parsers/spring-ai-alibaba-starter-document-parser-markdown/src/main/java/com/alibaba/cloud/ai/parser/markdown/MarkdownDocumentParser.java:100-130': 2.6339126002990785, 'community/document-parsers/spring-ai-alibaba-starter-document-parser-markdown/src/main/java/com/alibaba/cloud/ai/parser/markdown/MarkdownDocumentParser.java:125-155': 5.779485553040935, 'community/document-parsers/spring-ai-alibaba-starter-document-parser-markdown/src/main/java/com/alibaba/cloud/ai/parser/markdown/MarkdownDocumentParser.java:150-180': 7.929766955860558, 'community/document-parsers/spring-ai-alibaba-starter-document-parser-markdown/src/main/java/com/alibaba/cloud/ai/parser/markdown/MarkdownDocumentParser.java:175-205': 2.2725913165823757, 'community/document-parsers/spring-ai-alibaba-starter-document-parser-markdown/src/main/java/com/alibaba/cloud/ai/parser/markdown/config/MarkdownDocumentParserConfig.java:0-30': 1.6505282088323439, 'community/document-parsers/spring-ai-alibaba-starter-document-parser-markdown/src/main/java/com/alibaba/cloud/ai/parser/markdown/config/MarkdownDocumentParserConfig.java:25-55': 4.700801576028519, 'community/document-parsers/spring-ai-alibaba-starter-document-parser-markdown/src/main/java/com/alibaba/cloud/ai/parser/markdown/config/MarkdownDocumentParserConfig.java:50-80': 4.0497435026672175, 'community/document-parsers/spring-ai-alibaba-starter-document-parser-markdown/src/main/java/com/alibaba/cloud/ai/parser/markdown/config/MarkdownDocumentParserConfig.java:75-105': 5.723425738006437, 'community/document-parsers/spring-ai-alibaba-starter-document-parser-apache-pdfbox/src/test/java/com/alibaba/cloud/ai/parser/apache/pdfbox/ParagraphPdfDocumentParserTests.java:0-30': 1.544983913259478, 'community/document-parsers/spring-ai-alibaba-starter-document-parser-apache-pdfbox/src/test/java/com/alibaba/cloud/ai/parser/apache/pdfbox/ParagraphPdfDocumentParserTests.java:25-55': 2.0539719812595294, 'community/document-parsers/spring-ai-alibaba-starter-document-parser-apache-pdfbox/src/test/java/com/alibaba/cloud/ai/parser/apache/pdfbox/ParagraphPdfDocumentParserTests.java:50-80': 1.3821712726179565, 'community/document-parsers/spring-ai-alibaba-starter-document-parser-apache-pdfbox/src/test/java/com/alibaba/cloud/ai/parser/apache/pdfbox/PagePdfDocumentParserTests.java:0-30': 1.4898286183391802, 'community/document-parsers/spring-ai-alibaba-starter-document-parser-apache-pdfbox/src/main/java/com/alibaba/cloud/ai/parser/apache/pdfbox/ParagraphPdfDocumentParser.java:0-30': 1.4995620278218964, 'community/document-parsers/spring-ai-alibaba-starter-document-parser-apache-pdfbox/src/main/java/com/alibaba/cloud/ai/parser/apache/pdfbox/ParagraphPdfDocumentParser.java:25-55': 2.265600042357499, 'community/document-parsers/spring-ai-alibaba-starter-document-parser-apache-pdfbox/src/main/java/com/alibaba/cloud/ai/parser/apache/pdfbox/ParagraphPdfDocumentParser.java:50-80': 2.4642443332029242, 'community/document-parsers/spring-ai-alibaba-starter-document-parser-apache-pdfbox/src/main/java/com/alibaba/cloud/ai/parser/apache/pdfbox/ParagraphPdfDocumentParser.java:75-105': 1.587732881718793, 'community/document-parsers/spring-ai-alibaba-starter-document-parser-apache-pdfbox/src/main/java/com/alibaba/cloud/ai/parser/apache/pdfbox/PagePdfDocumentParser.java:0-30': 1.4754631369960434, 'community/vector-stores/spring-ai-alibaba-starter-opensearch-store/src/main/java/com/alibaba/cloud/ai/vectorstore/opensearch/OpenSearchApi.java:0-30': 1.4429973314407667, 'community/vector-stores/spring-ai-alibaba-starter-opensearch-store/src/main/java/com/alibaba/cloud/ai/vectorstore/opensearch/OpenSearchApi.java:25-55': 1.5244612091889669, 'community/vector-stores/spring-ai-alibaba-starter-opensearch-store/src/main/java/com/alibaba/cloud/ai/vectorstore/opensearch/OpenSearchApi.java:50-80': 1.3148693254696644, 'community/vector-stores/spring-ai-alibaba-starter-opensearch-store/src/main/java/com/alibaba/cloud/ai/vectorstore/opensearch/OpenSearchApi.java:75-105': 3.8999459522971787, 'community/vector-stores/spring-ai-alibaba-starter-opensearch-store/src/main/java/com/alibaba/cloud/ai/vectorstore/opensearch/OpenSearchApi.java:100-130': 5.947560015756316, 'community/vector-stores/spring-ai-alibaba-starter-opensearch-store/src/main/java/com/alibaba/cloud/ai/vectorstore/opensearch/OpenSearchApi.java:125-155': 5.587286292561109, 'community/vector-stores/spring-ai-alibaba-starter-opensearch-store/src/main/java/com/alibaba/cloud/ai/vectorstore/opensearch/OpenSearchApi.java:150-180': 1.307379897570568, 'community/vector-stores/spring-ai-alibaba-starter-opensearch-store/src/main/java/com/alibaba/cloud/ai/vectorstore/opensearch/OpenSearchApi.java:175-205': 4.900603832016889, 'community/vector-stores/spring-ai-alibaba-starter-opensearch-store/src/main/java/com/alibaba/cloud/ai/vectorstore/opensearch/OpenSearchApi.java:200-230': 5.767761017332387, 'community/vector-stores/spring-ai-alibaba-starter-opensearch-store/src/main/java/com/alibaba/cloud/ai/vectorstore/opensearch/OpenSearchApi.java:225-255': 4.88863186866311, 'community/vector-stores/spring-ai-alibaba-starter-opensearch-store/src/main/java/com/alibaba/cloud/ai/vectorstore/opensearch/OpenSearchApi.java:250-280': 6.8800248699330036, 'community/vector-stores/spring-ai-alibaba-starter-opensearch-store/src/main/java/com/alibaba/cloud/ai/vectorstore/opensearch/OpenSearchApi.java:275-305': 3.444578459477754, 'community/vector-stores/spring-ai-alibaba-starter-opensearch-store/src/main/java/com/alibaba/cloud/ai/vectorstore/opensearch/OpenSearchApi.java:300-330': 4.2870968572645, 'community/vector-stores/spring-ai-alibaba-starter-opensearch-store/src/main/java/com/alibaba/cloud/ai/vectorstore/opensearch/OpenSearchApi.java:325-355': 5.212193121237784, 'community/vector-stores/spring-ai-alibaba-starter-opensearch-store/src/main/java/com/alibaba/cloud/ai/vectorstore/opensearch/OpenSearchVectorStoreAutoConfiguration.java:0-30': 1.2999753052429273, 'community/vector-stores/spring-ai-alibaba-starter-opensearch-store/src/main/java/com/alibaba/cloud/ai/vectorstore/opensearch/OpenSearchVectorStoreProperties.java:0-30': 1.4995620278218964, 'community/vector-stores/spring-ai-alibaba-starter-opensearch-store/src/main/java/com/alibaba/cloud/ai/vectorstore/opensearch/OpenSearchVectorStoreFilterExpressionConverter.java:0-30': 1.461372044515705, 'community/vector-stores/spring-ai-alibaba-starter-opensearch-store/src/main/java/com/alibaba/cloud/ai/vectorstore/opensearch/OpenSearchVectorStoreFilterExpressionConverter.java:50-80': 2.354172872838646, 'community/vector-stores/spring-ai-alibaba-starter-opensearch-store/src/main/java/com/alibaba/cloud/ai/vectorstore/opensearch/OpenSearchVectorStore.java:0-30': 1.3339737577322137, 'community/vector-stores/spring-ai-alibaba-starter-opensearch-store/src/main/java/com/alibaba/cloud/ai/vectorstore/opensearch/OpenSearchVectorStore.java:25-55': 1.31111391621134, 'community/vector-stores/spring-ai-alibaba-starter-opensearch-store/src/main/java/com/alibaba/cloud/ai/vectorstore/opensearch/OpenSearchVectorStore.java:200-230': 1.3536415319003627, 'community/vector-stores/spring-ai-alibaba-starter-opensearch-store/src/main/java/com/alibaba/cloud/ai/vectorstore/opensearch/OpenSearchVectorStore.java:250-280': 1.5144029672636237, 'community/vector-stores/spring-ai-alibaba-starter-opensearch-store/src/main/java/com/alibaba/cloud/ai/vectorstore/opensearch/OpenSearchVectorStore.java:275-305': 1.9618052960295647, 'community/vector-stores/spring-ai-alibaba-starter-opensearch-store/src/main/java/com/alibaba/cloud/ai/vectorstore/opensearch/OpenSearchVectorStore.java:325-355': 2.000272609463373, 'community/vector-stores/spring-ai-alibaba-starter-opensearch-store/src/main/java/com/alibaba/cloud/ai/vectorstore/opensearch/OpenSearchVectorStoreOptions.java:0-30': 2.1009743252108466, 'community/vector-stores/spring-ai-alibaba-starter-analyticdb-store/src/test/java/com/alibaba/cloud/ai/vectorstore/analyticdb/AnalyticDbVectorTest.java:0-30': 1.3224450553019844, 'community/vector-stores/spring-ai-alibaba-starter-analyticdb-store/src/test/java/com/alibaba/cloud/ai/vectorstore/analyticdb/AnalyticDbVectorTest.java:75-105': 1.3339737577322137, 'community/vector-stores/spring-ai-alibaba-starter-analyticdb-store/src/test/java/com/alibaba/cloud/ai/vectorstore/analyticdb/AnalyticDbVectorTest.java:100-130': 4.78796610892037, 'community/vector-stores/spring-ai-alibaba-starter-analyticdb-store/src/main/java/com/alibaba/cloud/ai/vectorstore/analyticdb/AnalyticDbConfig.java:0-30': 1.6746123679952611, 'community/vector-stores/spring-ai-alibaba-starter-analyticdb-store/src/main/java/com/alibaba/cloud/ai/vectorstore/analyticdb/AnalyticDbVectorStoreProperties.java:0-30': 1.4707360092264137, 'community/vector-stores/spring-ai-alibaba-starter-analyticdb-store/src/main/java/com/alibaba/cloud/ai/vectorstore/analyticdb/AnalyticDbVectorStore.java:0-30': 1.3863454179444579, 'community/vector-stores/spring-ai-alibaba-starter-analyticdb-store/src/main/java/com/alibaba/cloud/ai/vectorstore/analyticdb/AnalyticDbVectorStore.java:125-155': 2.0945232588603075, 'community/vector-stores/spring-ai-alibaba-starter-analyticdb-store/src/main/java/com/alibaba/cloud/ai/vectorstore/analyticdb/AnalyticDbVectorStore.java:150-180': 2.3511430106594946, 'community/vector-stores/spring-ai-alibaba-starter-analyticdb-store/src/main/java/com/alibaba/cloud/ai/vectorstore/analyticdb/AnalyticDbVectorStore.java:400-430': 2.155225660967689, 'community/vector-stores/spring-ai-alibaba-starter-analyticdb-store/src/main/java/com/alibaba/cloud/ai/vectorstore/analyticdb/AnalyticDbVectorStoreAutoConfiguration.java:0-30': 1.3036670873065823, 'community/vector-stores/spring-ai-alibaba-starter-analyticdb-store/src/main/java/com/alibaba/cloud/ai/vectorstore/analyticdb/AdVectorFilterExpressionConverter.java:0-30': 1.5044765819016563, 'community/vector-stores/spring-ai-alibaba-starter-analyticdb-store/src/main/java/com/alibaba/cloud/ai/vectorstore/analyticdb/AdVectorFilterExpressionConverter.java:50-80': 1.6446150239729562, 'community/vector-stores/spring-ai-alibaba-starter-analyticdb-store/src/main/java/com/alibaba/cloud/ai/vectorstore/analyticdb/AdVectorFilterExpressionConverter.java:75-105': 2.066640046160255, 'community/vector-stores/spring-ai-alibaba-starter-oceanbase-store/src/test/java/com/alibaba/cloud/ai/vectorstore/oceanbase/OceanBaseVectorTest.java:0-30': 1.3262657509484723, 'community/vector-stores/spring-ai-alibaba-starter-oceanbase-store/src/test/java/com/alibaba/cloud/ai/vectorstore/oceanbase/OceanBaseVectorTest.java:50-80': 1.4660390745170708, 'community/vector-stores/spring-ai-alibaba-starter-oceanbase-store/src/test/java/com/alibaba/cloud/ai/vectorstore/oceanbase/OceanBaseVectorTest.java:125-155': 1.2336013365262284, 'community/vector-stores/spring-ai-alibaba-starter-oceanbase-store/src/test/java/com/alibaba/cloud/ai/vectorstore/oceanbase/OceanBaseVectorTest.java:150-180': 4.800937170510075, 'community/vector-stores/spring-ai-alibaba-starter-oceanbase-store/src/main/java/com/alibaba/cloud/ai/vectorstore/oceanbase/OceanBaseVectorStoreProperties.java:0-30': 1.4660390745170708, 'community/vector-stores/spring-ai-alibaba-starter-oceanbase-store/src/main/java/com/alibaba/cloud/ai/vectorstore/oceanbase/OceanBaseVectorStore.java:0-30': 1.3697983017004274, 'community/vector-stores/spring-ai-alibaba-starter-oceanbase-store/src/main/java/com/alibaba/cloud/ai/vectorstore/oceanbase/OceanBaseVectorFilterExpressionConverter.java:0-30': 1.4946794771655387, 'community/vector-stores/spring-ai-alibaba-starter-oceanbase-store/src/main/java/com/alibaba/cloud/ai/vectorstore/oceanbase/OceanBaseVectorFilterExpressionConverter.java:50-80': 2.2393333571830687, 'community/vector-stores/spring-ai-alibaba-starter-oceanbase-store/src/main/java/com/alibaba/cloud/ai/vectorstore/oceanbase/OceanBaseVectorFilterExpressionConverter.java:75-105': 2.515420452091244, 'community/vector-stores/spring-ai-alibaba-starter-oceanbase-store/src/main/java/com/alibaba/cloud/ai/vectorstore/oceanbase/OceanBaseVectorStoreAutoConfiguration.java:0-30': 1.3262657509484723, 'community/vector-stores/spring-ai-alibaba-starter-tair-store/src/main/java/com/alibaba/cloud/ai/vectorstore/tair/TairVectorStore.java:0-30': 1.3148693254696644, 'community/vector-stores/spring-ai-alibaba-starter-tair-store/src/main/java/com/alibaba/cloud/ai/vectorstore/tair/TairVectorStore.java:25-55': 1.394769803506785, 'community/vector-stores/spring-ai-alibaba-starter-tair-store/src/main/java/com/alibaba/cloud/ai/vectorstore/tair/TairVectorApi.java:0-30': 8.768786020625729, 'community/vector-stores/spring-ai-alibaba-starter-tair-store/src/main/java/com/alibaba/cloud/ai/vectorstore/tair/TairVectorApi.java:25-55': 8.288859047029291, 'community/vector-stores/spring-ai-alibaba-starter-tair-store/src/main/java/com/alibaba/cloud/ai/vectorstore/tair/TairVectorStoreOptions.java:0-30': 1.576825380051758, 'community/vector-stores/spring-ai-alibaba-starter-tair-store/src/main/java/com/alibaba/cloud/ai/vectorstore/tair/TairVectorStoreOptions.java:25-55': 1.6156734531778587, 'community/vector-stores/spring-ai-alibaba-starter-tair-store/src/main/java/com/alibaba/cloud/ai/vectorstore/tair/TairVectorStoreOptions.java:50-80': 7.9599837350687945, 'community/memories/spring-ai-alibaba-starter-memory-redis/src/test/java/com/alibaba/cloud/ai/memory/redis/RedisChatMemoryRepositoryIT.java:0-30': 1.3262657509484723, 'community/memories/spring-ai-alibaba-starter-memory-redis/src/test/java/com/alibaba/cloud/ai/memory/redis/RedisChatMemoryRepositoryIT.java:25-55': 1.4707360092264137, 'community/memories/spring-ai-alibaba-starter-memory-redis/src/main/java/com/alibaba/cloud/ai/memory/redis/RedisChatMemoryRepository.java:0-30': 1.3780221876928578, 'community/memories/spring-ai-alibaba-starter-memory-redis/src/main/java/com/alibaba/cloud/ai/memory/redis/serializer/MessageDeserializer.java:0-30': 1.4384756257963185, 'community/memories/spring-ai-alibaba-starter-memory-elasticsearch/src/test/java/com/alibaba/cloud/ai/memory/elasticsearch/ElasticsearchChatMemoryRepositoryIT.java:0-30': 1.31111391621134, 'community/memories/spring-ai-alibaba-starter-memory-elasticsearch/src/main/java/com/alibaba/cloud/ai/memory/elasticsearch/ElasticsearchChatMemoryRepository.java:0-30': 1.3378614571544742, 'community/memories/spring-ai-alibaba-starter-memory-elasticsearch/src/main/java/com/alibaba/cloud/ai/memory/elasticsearch/ElasticsearchChatMemoryRepository.java:125-155': 1.555453881109404, 'community/memories/spring-ai-alibaba-starter-memory-elasticsearch/src/main/java/com/alibaba/cloud/ai/memory/elasticsearch/ElasticsearchChatMemoryRepository.java:275-305': 1.555453881109404, 'community/memories/spring-ai-alibaba-starter-memory-jdbc/src/test/java/com/alibaba/cloud/ai/memory/jdbc/SQLiteChatMemoryRepositorySQLiteIT.java:0-30': 1.2963043732383113, 'community/memories/spring-ai-alibaba-starter-memory-jdbc/src/test/java/com/alibaba/cloud/ai/memory/jdbc/OracleChatMemoryRepositoryIT.java:0-30': 1.2963043732383113, 'community/memories/spring-ai-alibaba-starter-memory-jdbc/src/test/java/com/alibaba/cloud/ai/memory/jdbc/SqlServerChatMemoryRepositoryIT.java:0-30': 1.2963043732383113, 'community/memories/spring-ai-alibaba-starter-memory-jdbc/src/test/java/com/alibaba/cloud/ai/memory/jdbc/MysqlChatMemoryRepositoryIT.java:0-30': 1.2963043732383113, 'community/memories/spring-ai-alibaba-starter-memory-jdbc/src/test/java/com/alibaba/cloud/ai/memory/jdbc/MysqlChatMemoryRepositoryIT.java:25-55': 1.4162856102690216, 'community/memories/spring-ai-alibaba-starter-memory-jdbc/src/test/java/com/alibaba/cloud/ai/memory/jdbc/PostgresChatMemoryRepositoryIT.java:0-30': 1.2963043732383113, 'community/memories/spring-ai-alibaba-starter-memory-jdbc/src/main/java/com/alibaba/cloud/ai/memory/jdbc/SqlServerChatMemoryRepository.java:0-30': 1.4850091437813338, 'community/memories/spring-ai-alibaba-starter-memory-jdbc/src/main/java/com/alibaba/cloud/ai/memory/jdbc/JdbcChatMemoryRepository.java:0-30': 1.4250789533148378, 'community/memories/spring-ai-alibaba-starter-memory-jdbc/src/main/java/com/alibaba/cloud/ai/memory/jdbc/JdbcChatMemoryRepository.java:25-55': 1.390544851406706, 'community/memories/spring-ai-alibaba-starter-memory-jdbc/src/main/java/com/alibaba/cloud/ai/memory/jdbc/JdbcChatMemoryRepository.java:50-80': 2.000272609463373, 'community/memories/spring-ai-alibaba-starter-memory-jdbc/src/main/java/com/alibaba/cloud/ai/memory/jdbc/JdbcChatMemoryRepository.java:100-130': 1.5295406001183258, 'community/memories/spring-ai-alibaba-starter-memory-jdbc/src/main/java/com/alibaba/cloud/ai/memory/jdbc/JdbcChatMemoryRepository.java:125-155': 1.5094234550964287, 'community/memories/spring-ai-alibaba-starter-memory-jdbc/src/main/java/com/alibaba/cloud/ai/memory/jdbc/MysqlChatMemoryRepository.java:0-30': 1.390544851406706, 'community/memories/spring-ai-alibaba-starter-memory-jdbc/src/main/java/com/alibaba/cloud/ai/memory/jdbc/MysqlChatMemoryRepository.java:50-80': 1.6329148574925245, 'community/memories/spring-ai-alibaba-starter-memory-jdbc/src/main/java/com/alibaba/cloud/ai/memory/jdbc/PostgresChatMemoryRepository.java:0-30': 1.3863454179444579, 'community/memories/spring-ai-alibaba-starter-memory-jdbc/src/main/java/com/alibaba/cloud/ai/memory/jdbc/OracleChatMemoryRepository.java:0-30': 1.4119295075815892, 'community/memories/spring-ai-alibaba-starter-memory-jdbc/src/main/java/com/alibaba/cloud/ai/memory/jdbc/SQLiteChatMemoryRepository.java:0-30': 1.5144029672636237, 'community/memories/spring-ai-alibaba-starter-memory-jdbc/src/main/java/com/alibaba/cloud/ai/memory/jdbc/SQLiteChatMemoryRepository.java:25-55': 1.4850091437813338, 'community/document-readers/spring-ai-alibaba-starter-document-reader-sqlite/README.md:0-30': 2.5844828952318584, 'community/document-readers/spring-ai-alibaba-starter-document-reader-sqlite/README.md:125-155': 1.7784132932580297, 'community/document-readers/spring-ai-alibaba-starter-document-reader-sqlite/README.md:175-205': 2.1142306775732047, 'community/document-readers/spring-ai-alibaba-starter-document-reader-sqlite/src/test/java/com/alibaba/cloud/ai/reader/sqlite/SQLiteDocumentReaderTest.java:0-30': 1.4567346345301488, 'community/document-readers/spring-ai-alibaba-starter-document-reader-sqlite/src/test/java/com/alibaba/cloud/ai/reader/sqlite/SQLiteDocumentReaderTest.java:50-80': 1.9410671124612497, 'community/document-readers/spring-ai-alibaba-starter-document-reader-sqlite/src/test/java/com/alibaba/cloud/ai/reader/sqlite/SQLiteDocumentReaderTest.java:75-105': 1.4206686751462156, 'community/document-readers/spring-ai-alibaba-starter-document-reader-sqlite/src/main/java/com/alibaba/cloud/ai/reader/sqlite/SQLiteResource.java:0-30': 2.223066758027963, 'community/document-readers/spring-ai-alibaba-starter-document-reader-sqlite/src/main/java/com/alibaba/cloud/ai/reader/sqlite/SQLiteResource.java:50-80': 1.394769803506785, 'community/document-readers/spring-ai-alibaba-starter-document-reader-sqlite/src/main/java/com/alibaba/cloud/ai/reader/sqlite/SQLiteDocumentReader.java:0-30': 1.4754631369960434, 'community/document-readers/spring-ai-alibaba-starter-document-reader-sqlite/src/main/java/com/alibaba/cloud/ai/reader/sqlite/SQLiteDocumentReader.java:25-55': 2.255839762599752, 'community/document-readers/spring-ai-alibaba-starter-document-reader-sqlite/src/main/java/com/alibaba/cloud/ai/reader/sqlite/SQLiteDocumentReader.java:50-80': 2.3034951853349526, 'community/document-readers/spring-ai-alibaba-starter-document-reader-mbox/README.md:0-30': 2.1351767481159443, 'community/document-readers/spring-ai-alibaba-starter-document-reader-mbox/README.md:75-105': 1.7853297775722856, 'community/document-readers/spring-ai-alibaba-starter-document-reader-mbox/README.md:125-155': 1.9359099717752393, 'community/document-readers/spring-ai-alibaba-starter-document-reader-mbox/src/test/java/com/alibaba/cloud/ai/reader/mbox/MboxDocumentReaderTest.java:0-30': 1.4475475537922684, 'community/document-readers/spring-ai-alibaba-starter-document-reader-mbox/src/test/java/com/alibaba/cloud/ai/reader/mbox/MboxDocumentReaderTest.java:100-130': 1.452126563470489, 'community/document-readers/spring-ai-alibaba-starter-document-reader-mbox/src/test/java/com/alibaba/cloud/ai/reader/mbox/MboxDocumentReaderTest.java:125-155': 1.8737230773897195, 'community/document-readers/spring-ai-alibaba-starter-document-reader-mbox/src/test/java/com/alibaba/cloud/ai/reader/mbox/MboxDocumentReaderTest.java:150-180': 2.0539719812595294, 'community/document-readers/spring-ai-alibaba-starter-document-reader-mbox/src/main/java/com/alibaba/cloud/ai/reader/mbox/MboxDocumentReader.java:0-30': 1.452126563470489, 'community/document-readers/spring-ai-alibaba-starter-document-reader-mbox/src/main/java/com/alibaba/cloud/ai/reader/mbox/MboxDocumentReader.java:200-230': 1.65648406863643, 'community/document-readers/spring-ai-alibaba-starter-document-reader-mysql/README.md:0-30': 2.6339126002990785, 'community/document-readers/spring-ai-alibaba-starter-document-reader-mysql/README.md:125-155': 1.7512750765563054, 'community/document-readers/spring-ai-alibaba-starter-document-reader-mysql/README.md:175-205': 2.1240130411682925, 'community/document-readers/spring-ai-alibaba-starter-document-reader-mysql/src/test/java/com/alibaba/cloud/ai/reader/mysql/MySQLDocumentReaderTest.java:0-30': 1.4946794771655387, 'community/document-readers/spring-ai-alibaba-starter-document-reader-mysql/src/test/java/com/alibaba/cloud/ai/reader/mysql/MySQLDocumentReaderTest.java:50-80': 1.9618052960295647, 'community/document-readers/spring-ai-alibaba-starter-document-reader-mysql/src/main/java/com/alibaba/cloud/ai/reader/mysql/MySQLResource.java:0-30': 2.2176969475324566, 'community/document-readers/spring-ai-alibaba-starter-document-reader-mysql/src/main/java/com/alibaba/cloud/ai/reader/mysql/MySQLResource.java:50-80': 1.394769803506785, 'community/document-readers/spring-ai-alibaba-starter-document-reader-mysql/src/main/java/com/alibaba/cloud/ai/reader/mysql/MySQLDocumentReader.java:0-30': 2.1401539252526356, 'community/document-readers/spring-ai-alibaba-starter-document-reader-mysql/src/main/java/com/alibaba/cloud/ai/reader/mysql/MySQLDocumentReader.java:25-55': 2.233884771648712, 'community/document-readers/spring-ai-alibaba-starter-document-reader-mysql/src/main/java/com/alibaba/cloud/ai/reader/mysql/MySQLDocumentReader.java:50-80': 1.9787176467773766, 'community/document-readers/spring-ai-alibaba-starter-document-reader-youtube/src/test/java/com/alibaba/cloud/ai/reader/youtube/YoutubeDocumentReaderTest.java:0-30': 1.6100069440831988, 'community/document-readers/spring-ai-alibaba-starter-document-reader-youtube/src/main/java/com/alibaba/cloud/ai/reader/youtube/YoutubeDocumentReader.java:0-30': 1.4206686751462156, 'community/document-readers/spring-ai-alibaba-starter-document-reader-youtube/src/main/java/com/alibaba/cloud/ai/reader/youtube/YoutubeDocumentReader.java:25-55': 2.1501782175023574, 'community/document-readers/spring-ai-alibaba-starter-document-reader-youtube/src/main/java/com/alibaba/cloud/ai/reader/youtube/YoutubeDocumentReader.java:100-130': 5.580984884070567, 'community/document-readers/spring-ai-alibaba-starter-document-reader-mongodb/src/test/java/com/alibaba/cloud/ai/reader/mongodb/MongodbDocumentReaderIT.java:0-30': 1.4850091437813338, 'community/document-readers/spring-ai-alibaba-starter-document-reader-mongodb/src/main/java/com/alibaba/cloud/ai/reader/mongodb/MongodbResource.java:0-30': 2.1705111834422235, 'community/document-readers/spring-ai-alibaba-starter-document-reader-mongodb/src/main/java/com/alibaba/cloud/ai/reader/mongodb/MongodbResource.java:25-55': 1.7993254061418673, 'community/document-readers/spring-ai-alibaba-starter-document-reader-mongodb/src/main/java/com/alibaba/cloud/ai/reader/mongodb/MongodbResource.java:100-130': 1.9523769180600095, 'community/document-readers/spring-ai-alibaba-starter-document-reader-mongodb/src/main/java/com/alibaba/cloud/ai/reader/mongodb/MongodbDocumentReader.java:0-30': 1.3457052358459312, 'community/document-readers/spring-ai-alibaba-starter-document-reader-mongodb/src/main/java/com/alibaba/cloud/ai/reader/mongodb/MongodbDocumentReader.java:100-130': 1.2782563680468169, 'community/document-readers/spring-ai-alibaba-starter-document-reader-mongodb/src/main/java/com/alibaba/cloud/ai/reader/mongodb/MongodbDocumentReader.java:175-205': 1.7380142090139563, 'community/document-readers/spring-ai-alibaba-starter-document-reader-mongodb/src/main/java/com/alibaba/cloud/ai/reader/mongodb/MongodbDocumentReader.java:300-330': 2.4294668832765534, 'community/document-readers/spring-ai-alibaba-starter-document-reader-mongodb/src/main/java/com/alibaba/cloud/ai/reader/mongodb/MongodbDocumentReader.java:325-355': 2.342679585127059, 'community/document-readers/spring-ai-alibaba-starter-document-reader-mongodb/src/main/java/com/alibaba/cloud/ai/reader/mongodb/MongodbDocumentReader.java:350-380': 2.701621070561738, 'community/document-readers/spring-ai-alibaba-starter-document-reader-mongodb/src/main/java/com/alibaba/cloud/ai/reader/mongodb/MongodbDocumentReader.java:375-405': 2.2693333565209644, 'community/document-readers/spring-ai-alibaba-starter-document-reader-mongodb/src/main/java/com/alibaba/cloud/ai/reader/mongodb/converter/DocumentConverter.java:0-30': 1.5987923371942092, 'community/document-readers/spring-ai-alibaba-starter-document-reader-mongodb/src/main/java/com/alibaba/cloud/ai/reader/mongodb/converter/DefaultDocumentConverter.java:0-30': 1.5987923371942092, 'community/document-readers/spring-ai-alibaba-starter-document-reader-yuque/src/test/java/com/alibaba/cloud/ai/reader/yuque/YuQueDocumentLoaderIT.java:0-30': 2.0402786375861277, 'community/document-readers/spring-ai-alibaba-starter-document-reader-yuque/src/test/java/com/alibaba/cloud/ai/reader/yuque/YuQueDocumentLoaderIT.java:25-55': 1.4076001190620582, 'community/document-readers/spring-ai-alibaba-starter-document-reader-yuque/src/main/java/com/alibaba/cloud/ai/reader/yuque/YuQueResource.java:0-30': 1.4898286183391802, 'community/document-readers/spring-ai-alibaba-starter-document-reader-yuque/src/main/java/com/alibaba/cloud/ai/reader/yuque/YuQueResource.java:125-155': 4.2870968572645, 'community/document-readers/spring-ai-alibaba-starter-document-reader-yuque/src/main/java/com/alibaba/cloud/ai/reader/yuque/YuQueDocumentReader.java:0-30': 1.5607422602136187, 'community/document-readers/spring-ai-alibaba-starter-document-reader-gitlab/README.md:0-30': 2.8174746261393113, 'community/document-readers/spring-ai-alibaba-starter-document-reader-gitlab/src/test/java/com/alibaba/cloud/ai/reader/gitlab/GitLabRepositoryReaderTest.java:0-30': 1.4384756257963185, 'community/document-readers/spring-ai-alibaba-starter-document-reader-gitlab/src/test/java/com/alibaba/cloud/ai/reader/gitlab/GitLabRepositoryReaderTest.java:25-55': 1.4339821696206667, 'community/document-readers/spring-ai-alibaba-starter-document-reader-gitlab/src/test/java/com/alibaba/cloud/ai/reader/gitlab/GitLabRepositoryReaderTest.java:50-80': 1.9207627879856997, 'community/document-readers/spring-ai-alibaba-starter-document-reader-gitlab/src/test/java/com/alibaba/cloud/ai/reader/gitlab/GitLabRepositoryReaderTest.java:150-180': 1.3148693254696644, 'community/document-readers/spring-ai-alibaba-starter-document-reader-gitlab/src/test/java/com/alibaba/cloud/ai/reader/gitlab/GitLabRepositoryReaderTest.java:175-205': 1.3457052358459312, 'community/document-readers/spring-ai-alibaba-starter-document-reader-gitlab/src/test/java/com/alibaba/cloud/ai/reader/gitlab/GitLabIssueReaderTest.java:0-30': 1.452126563470489, 'community/document-readers/spring-ai-alibaba-starter-document-reader-gitlab/src/test/java/com/alibaba/cloud/ai/reader/gitlab/GitLabIssueReaderTest.java:25-55': 1.4032971997212973, 'community/document-readers/spring-ai-alibaba-starter-document-reader-gitlab/src/test/java/com/alibaba/cloud/ai/reader/gitlab/GitLabIssueReaderTest.java:50-80': 1.9288333297066318, 'community/document-readers/spring-ai-alibaba-starter-document-reader-gitlab/src/test/java/com/alibaba/cloud/ai/reader/gitlab/GitLabIssueReaderTest.java:75-105': 1.271177101356609, 'community/document-readers/spring-ai-alibaba-starter-document-reader-gitlab/src/test/java/com/alibaba/cloud/ai/reader/gitlab/GitLabIssueReaderTest.java:125-155': 1.3697983017004274, 'community/document-readers/spring-ai-alibaba-starter-document-reader-gitlab/src/main/java/com/alibaba/cloud/ai/reader/gitlab/GitLabScope.java:0-30': 1.6869199579366625, 'community/document-readers/spring-ai-alibaba-starter-document-reader-gitlab/src/main/java/com/alibaba/cloud/ai/reader/gitlab/AbstractGitLabReader.java:0-30': 1.4429973314407667, 'community/document-readers/spring-ai-alibaba-starter-document-reader-gitlab/src/main/java/com/alibaba/cloud/ai/reader/gitlab/GitLabIssueReader.java:0-30': 2.0448227535674666, 'community/document-readers/spring-ai-alibaba-starter-document-reader-gitlab/src/main/java/com/alibaba/cloud/ai/reader/gitlab/GitLabIssueReader.java:25-55': 1.4119295075815892, 'community/document-readers/spring-ai-alibaba-starter-document-reader-gitlab/src/main/java/com/alibaba/cloud/ai/reader/gitlab/GitLabIssueReader.java:125-155': 1.1444164674175241, 'community/document-readers/spring-ai-alibaba-starter-document-reader-gitlab/src/main/java/com/alibaba/cloud/ai/reader/gitlab/GitLabIssueType.java:0-30': 1.712085963376952, 'community/document-readers/spring-ai-alibaba-starter-document-reader-gitlab/src/main/java/com/alibaba/cloud/ai/reader/gitlab/GitLabIssueState.java:0-30': 1.6994097969724125, 'community/document-readers/spring-ai-alibaba-starter-document-reader-gitlab/src/main/java/com/alibaba/cloud/ai/reader/gitlab/GitLabIssueState.java:25-55': 2.1846625150837045, 'community/document-readers/spring-ai-alibaba-starter-document-reader-gitlab/src/main/java/com/alibaba/cloud/ai/reader/gitlab/GitLabIssueConfig.java:0-30': 2.180822559135374, 'community/document-readers/spring-ai-alibaba-starter-document-reader-gitlab/src/main/java/com/alibaba/cloud/ai/reader/gitlab/GitLabRepositoryReader.java:0-30': 2.0312507266116446, 'community/document-readers/spring-ai-alibaba-starter-document-reader-gitlab/src/main/java/com/alibaba/cloud/ai/reader/gitlab/GitLabRepositoryReader.java:25-55': 1.5607422602136187, 'community/document-readers/spring-ai-alibaba-starter-document-reader-gitlab/src/main/java/com/alibaba/cloud/ai/reader/gitlab/GitLabRepositoryReader.java:50-80': 1.4162856102690216, 'community/document-readers/spring-ai-alibaba-starter-document-reader-gitlab/src/main/java/com/alibaba/cloud/ai/reader/gitlab/GitLabRepositoryReader.java:75-105': 1.5660667218567792, 'community/document-readers/spring-ai-alibaba-starter-document-reader-arxiv/src/test/java/com/alibaba/cloud/ai/reader/arxiv/ArxivDocumentReaderTest.java:0-30': 1.6043800433693445, 'community/document-readers/spring-ai-alibaba-starter-document-reader-arxiv/src/test/java/com/alibaba/cloud/ai/reader/arxiv/ArxivDocumentReaderTest.java:25-55': 9.215914070198975, 'community/document-readers/spring-ai-alibaba-starter-document-reader-arxiv/src/test/java/com/alibaba/cloud/ai/reader/arxiv/ArxivClientTest.java:0-30': 1.5502012189992864, 'community/document-readers/spring-ai-alibaba-starter-document-reader-arxiv/src/test/java/com/alibaba/cloud/ai/reader/arxiv/ArxivClientTest.java:25-55': 9.250285550383424, 'community/document-readers/spring-ai-alibaba-starter-document-reader-arxiv/src/test/java/com/alibaba/cloud/ai/reader/arxiv/ArxivClientTest.java:50-80': 6.423406476331272, 'community/document-readers/spring-ai-alibaba-starter-document-reader-arxiv/src/test/java/com/alibaba/cloud/ai/reader/arxiv/ArxivClientTest.java:75-105': 7.378910022829526, 'community/document-readers/spring-ai-alibaba-starter-document-reader-arxiv/src/test/java/com/alibaba/cloud/ai/reader/arxiv/ArxivClientTest.java:100-130': 7.788946524496464, 'community/document-readers/spring-ai-alibaba-starter-document-reader-arxiv/src/test/java/com/alibaba/cloud/ai/reader/arxiv/ArxivClientTest.java:125-155': 1.9048226335943024, 'community/document-readers/spring-ai-alibaba-starter-document-reader-arxiv/src/test/java/com/alibaba/cloud/ai/reader/arxiv/ArxivClientTest.java:175-205': 6.984123425382336, 'community/document-readers/spring-ai-alibaba-starter-document-reader-arxiv/src/main/java/com/alibaba/cloud/ai/reader/arxiv/ArxivDocumentReader.java:0-30': 1.3821712726179565, 'community/document-readers/spring-ai-alibaba-starter-document-reader-arxiv/src/main/java/com/alibaba/cloud/ai/reader/arxiv/ArxivDocumentReader.java:25-55': 1.587732881718793, 'community/document-readers/spring-ai-alibaba-starter-document-reader-arxiv/src/main/java/com/alibaba/cloud/ai/reader/arxiv/ArxivDocumentReader.java:150-180': 1.4660390745170708, 'community/document-readers/spring-ai-alibaba-starter-document-reader-arxiv/src/main/java/com/alibaba/cloud/ai/reader/arxiv/ArxivResource.java:0-30': 2.191232374304636, 'community/document-readers/spring-ai-alibaba-starter-document-reader-arxiv/src/main/java/com/alibaba/cloud/ai/reader/arxiv/client/ArxivClient.java:0-30': 1.4567346345301488, 'community/document-readers/spring-ai-alibaba-starter-document-reader-arxiv/src/main/java/com/alibaba/cloud/ai/reader/arxiv/client/ArxivClient.java:75-105': 1.4429973314407667, 'community/document-readers/spring-ai-alibaba-starter-document-reader-arxiv/src/main/java/com/alibaba/cloud/ai/reader/arxiv/client/ArxivClient.java:100-130': 1.4429973314407667, 'community/document-readers/spring-ai-alibaba-starter-document-reader-arxiv/src/main/java/com/alibaba/cloud/ai/reader/arxiv/client/ArxivClient.java:125-155': 1.3417718832367183, 'community/document-readers/spring-ai-alibaba-starter-document-reader-arxiv/src/main/java/com/alibaba/cloud/ai/reader/arxiv/client/ArxivClient.java:150-180': 3.90009716203699, 'community/document-readers/spring-ai-alibaba-starter-document-reader-arxiv/src/main/java/com/alibaba/cloud/ai/reader/arxiv/client/ArxivClient.java:250-280': 1.555453881109404, 'community/document-readers/spring-ai-alibaba-starter-document-reader-arxiv/src/main/java/com/alibaba/cloud/ai/reader/arxiv/client/ArxivClient.java:275-305': 2.0914025469442454, 'community/document-readers/spring-ai-alibaba-starter-document-reader-arxiv/src/main/java/com/alibaba/cloud/ai/reader/arxiv/client/ArxivClient.java:375-405': 3.70337894027893, 'community/document-readers/spring-ai-alibaba-starter-document-reader-arxiv/src/main/java/com/alibaba/cloud/ai/reader/arxiv/client/ArxivSortCriterion.java:0-30': 1.587732881718793, 'community/document-readers/spring-ai-alibaba-starter-document-reader-arxiv/src/main/java/com/alibaba/cloud/ai/reader/arxiv/client/ArxivSortOrder.java:0-30': 1.555453881109404, 'community/document-readers/spring-ai-alibaba-starter-document-reader-arxiv/src/main/java/com/alibaba/cloud/ai/reader/arxiv/client/ArxivSearch.java:0-30': 1.5295406001183258, 'community/document-readers/spring-ai-alibaba-starter-document-reader-arxiv/src/main/java/com/alibaba/cloud/ai/reader/arxiv/client/ArxivResult.java:0-30': 1.555453881109404, 'community/document-readers/spring-ai-alibaba-starter-document-reader-arxiv/src/main/java/com/alibaba/cloud/ai/reader/arxiv/client/ArxivResult.java:50-80': 1.7249526580211665, 'community/document-readers/spring-ai-alibaba-starter-document-reader-arxiv/src/main/java/com/alibaba/cloud/ai/reader/arxiv/client/ArxivResult.java:175-205': 3.245949628363023, 'community/document-readers/spring-ai-alibaba-starter-document-reader-arxiv/src/main/java/com/alibaba/cloud/ai/reader/arxiv/client/ArxivResult.java:200-230': 3.444578459477754, 'community/document-readers/spring-ai-alibaba-starter-document-reader-arxiv/src/main/java/com/alibaba/cloud/ai/reader/arxiv/client/ArxivResult.java:225-255': 5.450725864695354, 'community/document-readers/spring-ai-alibaba-starter-document-reader-arxiv/src/main/java/com/alibaba/cloud/ai/reader/arxiv/client/ArxivResult.java:275-305': 3.322587353419976, 'community/document-readers/spring-ai-alibaba-starter-document-reader-arxiv/src/main/java/com/alibaba/cloud/ai/reader/arxiv/client/ArxivResult.java:300-330': 4.512226596021997, 'community/document-readers/spring-ai-alibaba-starter-document-reader-chatgpt-data/README.md:0-30': 2.4955017207351506, 'community/document-readers/spring-ai-alibaba-starter-document-reader-chatgpt-data/README.md:25-55': 3.2584761210481337, 'community/document-readers/spring-ai-alibaba-starter-document-reader-chatgpt-data/src/test/java/com/alibaba/cloud/ai/reader/chatgpt/data/ChatGptDataDocumentReaderTests.java:0-30': 1.5660667218567792, 'community/document-readers/spring-ai-alibaba-starter-document-reader-chatgpt-data/src/main/java/com/alibaba/cloud/ai/reader/chatgpt/data/ChatGptDataDocumentReader.java:0-30': 1.4475475537922684, 'community/document-readers/spring-ai-alibaba-starter-document-reader-chatgpt-data/src/main/java/com/alibaba/cloud/ai/reader/chatgpt/data/ChatGptDataDocumentReader.java:25-55': 1.4898286183391802, 'community/document-readers/spring-ai-alibaba-starter-document-reader-chatgpt-data/src/main/java/com/alibaba/cloud/ai/reader/chatgpt/data/ChatGptDataDocumentReader.java:50-80': 1.452126563470489, 'community/document-readers/spring-ai-alibaba-starter-document-reader-email/README.md:0-30': 2.594219856311161, 'community/document-readers/spring-ai-alibaba-starter-document-reader-email/README.md:25-55': 2.165391975076459, 'community/document-readers/spring-ai-alibaba-starter-document-reader-email/src/test/java/com/alibaba/cloud/ai/reader/email/msg/MsgEmailDocumentReaderTest.java:0-30': 1.5714276365890276, 'community/document-readers/spring-ai-alibaba-starter-document-reader-email/src/test/java/com/alibaba/cloud/ai/reader/email/eml/EmlEmailDocumentReaderTest.java:0-30': 1.5660667218567792, 'community/document-readers/spring-ai-alibaba-starter-document-reader-email/src/test/java/com/alibaba/cloud/ai/reader/email/eml/EmlEmailDocumentReaderTest.java:25-55': 2.8322125977197317, 'community/document-readers/spring-ai-alibaba-starter-document-reader-email/src/test/java/com/alibaba/cloud/ai/reader/email/eml/EmlEmailDocumentReaderTest.java:75-105': 2.6211990837517285, 'community/document-readers/spring-ai-alibaba-starter-document-reader-email/src/test/java/com/alibaba/cloud/ai/reader/email/eml/EmlEmailDocumentReaderTest.java:100-130': 2.527059997833506, 'community/document-readers/spring-ai-alibaba-starter-document-reader-email/src/main/java/com/alibaba/cloud/ai/reader/email/msg/MsgEmailDocumentReader.java:0-30': 1.4946794771655387, 'community/document-readers/spring-ai-alibaba-starter-document-reader-email/src/main/java/com/alibaba/cloud/ai/reader/email/msg/MsgEmailParser.java:0-30': 1.576825380051758, 'community/document-readers/spring-ai-alibaba-starter-document-reader-email/src/main/java/com/alibaba/cloud/ai/reader/email/msg/MsgEmailElement.java:0-30': 1.6329148574925245, 'community/document-readers/spring-ai-alibaba-starter-document-reader-email/src/main/java/com/alibaba/cloud/ai/reader/email/msg/MsgEmailElement.java:25-55': 1.9776093833837265, 'community/document-readers/spring-ai-alibaba-starter-document-reader-email/src/main/java/com/alibaba/cloud/ai/reader/email/msg/MsgEmailElement.java:50-80': 1.8804004116084223, 'community/document-readers/spring-ai-alibaba-starter-document-reader-email/src/main/java/com/alibaba/cloud/ai/reader/email/msg/MsgParser.java:0-30': 1.4660390745170708, 'community/document-readers/spring-ai-alibaba-starter-document-reader-email/src/main/java/com/alibaba/cloud/ai/reader/email/eml/EmailElement.java:0-30': 1.5244612091889669, 'community/document-readers/spring-ai-alibaba-starter-document-reader-email/src/main/java/com/alibaba/cloud/ai/reader/email/eml/EmailElement.java:75-105': 1.7784132932580297, 'community/document-readers/spring-ai-alibaba-starter-document-reader-email/src/main/java/com/alibaba/cloud/ai/reader/email/eml/EmlEmailDocumentReader.java:0-30': 1.4384756257963185, 'community/document-readers/spring-ai-alibaba-starter-document-reader-email/src/main/java/com/alibaba/cloud/ai/reader/email/eml/EmlEmailDocumentReader.java:25-55': 2.191232374304636, 'community/document-readers/spring-ai-alibaba-starter-document-reader-email/src/main/java/com/alibaba/cloud/ai/reader/email/eml/EmlEmailDocumentReader.java:75-105': 2.0448227535674666, 'community/document-readers/spring-ai-alibaba-starter-document-reader-email/src/main/java/com/alibaba/cloud/ai/reader/email/eml/EmailParser.java:0-30': 1.4567346345301488, 'community/document-readers/spring-ai-alibaba-starter-document-reader-email/src/main/java/com/alibaba/cloud/ai/reader/email/eml/EmailParser.java:25-55': 1.5607422602136187, 'community/document-readers/spring-ai-alibaba-starter-document-reader-email/src/main/java/com/alibaba/cloud/ai/reader/email/eml/EmailParser.java:75-105': 1.5502012189992864, 'community/document-readers/spring-ai-alibaba-starter-document-reader-email/src/main/java/com/alibaba/cloud/ai/reader/email/eml/EmailParser.java:175-205': 2.067850372817686, 'community/document-readers/spring-ai-alibaba-starter-document-reader-email/src/main/java/com/alibaba/cloud/ai/reader/email/eml/EmailParser.java:200-230': 1.5194154424953983, 'community/document-readers/spring-ai-alibaba-starter-document-reader-email/src/main/java/com/alibaba/cloud/ai/reader/email/eml/EmailParser.java:275-305': 1.5660667218567792, 'community/document-readers/spring-ai-alibaba-starter-document-reader-larksuite/src/test/java/com/alibaba/cloud/ai/reader/feishu/FeiShuDocumentReaderTest.java:0-30': 2.035754673184634, 'community/document-readers/spring-ai-alibaba-starter-document-reader-larksuite/src/test/java/com/alibaba/cloud/ai/reader/feishu/FeiShuDocumentReaderTest.java:25-55': 1.8891448720732436, 'community/document-readers/spring-ai-alibaba-starter-document-reader-larksuite/src/test/java/com/alibaba/cloud/ai/reader/feishu/FeiShuDocumentReaderTest.java:75-105': 1.3262657509484723, 'community/document-readers/spring-ai-alibaba-starter-document-reader-larksuite/src/main/java/com/alibaba/cloud/ai/reader/feishu/FeiShuDocumentReader.java:0-30': 1.3780221876928578, 'community/document-readers/spring-ai-alibaba-starter-document-reader-larksuite/src/main/java/com/alibaba/cloud/ai/reader/feishu/FeiShuDocumentReader.java:75-105': 3.496354980556851, 'community/document-readers/spring-ai-alibaba-starter-document-reader-larksuite/src/main/java/com/alibaba/cloud/ai/reader/feishu/FeiShuDocumentReader.java:100-130': 3.285482664435823, 'community/document-readers/spring-ai-alibaba-starter-document-reader-larksuite/src/main/java/com/alibaba/cloud/ai/reader/feishu/FeiShuDocumentReader.java:125-155': 3.555241762302633, 'community/document-readers/spring-ai-alibaba-starter-document-reader-larksuite/src/main/java/com/alibaba/cloud/ai/reader/feishu/FeiShuResource.java:0-30': 1.5346539525074083, 'community/document-readers/spring-ai-alibaba-starter-document-reader-bilibili/src/test/java/com/alibaba/cloud/ai/reader/bilibili/BilibiliDocumentReaderTest.java:0-30': 1.6100069440831988, 'community/document-readers/spring-ai-alibaba-starter-document-reader-bilibili/src/main/java/com/alibaba/cloud/ai/reader/bilibili/BilibiliDocumentReader.java:0-30': 1.4206686751462156, 'community/document-readers/spring-ai-alibaba-starter-document-reader-bilibili/src/main/java/com/alibaba/cloud/ai/reader/bilibili/BilibiliDocumentReader.java:25-55': 2.013432484642509, 'community/document-readers/spring-ai-alibaba-starter-document-reader-bilibili/src/main/java/com/alibaba/cloud/ai/reader/bilibili/BilibiliDocumentReader.java:50-80': 1.2076394433123816, 'community/document-readers/spring-ai-alibaba-starter-document-reader-bilibili/src/main/java/com/alibaba/cloud/ai/reader/bilibili/BilibiliDocumentReader.java:75-105': 1.4339821696206667, 'community/document-readers/spring-ai-alibaba-starter-document-reader-bilibili/src/main/java/com/alibaba/cloud/ai/reader/bilibili/BilibiliDocumentReader.java:100-130': 1.3576448869712883, 'community/document-readers/spring-ai-alibaba-starter-document-reader-huggingface-fs/src/test/java/com/alibaba/cloud/ai/reader/huggingface/fs/HuggingFaceFSDocumentReaderTests.java:0-30': 1.4429973314407667, 'community/document-readers/spring-ai-alibaba-starter-document-reader-huggingface-fs/src/main/java/com/alibaba/cloud/ai/reader/huggingface/fs/HuggingFaceFSDocumentReader.java:0-30': 1.4802207498974258, 'community/document-readers/spring-ai-alibaba-starter-document-reader-huggingface-fs/src/main/java/com/alibaba/cloud/ai/reader/huggingface/fs/HuggingFaceFSDocumentReader.java:50-80': 1.587732881718793, 'community/document-readers/spring-ai-alibaba-starter-document-reader-huggingface-fs/src/main/java/com/alibaba/cloud/ai/reader/huggingface/fs/HuggingFaceFSDocumentReader.java:75-105': 1.4946794771655387, 'community/document-readers/spring-ai-alibaba-starter-document-reader-elasticsearch/README.md:0-30': 2.6339126002990785, 'community/document-readers/spring-ai-alibaba-starter-document-reader-elasticsearch/README.md:75-105': 2.5183183510177276, 'community/document-readers/spring-ai-alibaba-starter-document-reader-elasticsearch/README.md:100-130': 8.813399588734555, 'community/document-readers/spring-ai-alibaba-starter-document-reader-elasticsearch/src/test/java/com/alibaba/cloud/ai/document/reader/es/ElasticsearchDocumentReaderTest.java:0-30': 1.2782563680468169, 'community/document-readers/spring-ai-alibaba-starter-document-reader-elasticsearch/src/test/java/com/alibaba/cloud/ai/document/reader/es/ElasticsearchDocumentReaderTest.java:125-155': 1.3536415319003627, 'community/document-readers/spring-ai-alibaba-starter-document-reader-elasticsearch/src/test/java/com/alibaba/cloud/ai/document/reader/es/ElasticsearchDocumentReaderTest.java:150-180': 1.3496617171995993, 'community/document-readers/spring-ai-alibaba-starter-document-reader-elasticsearch/src/test/java/com/alibaba/cloud/ai/document/reader/es/ElasticsearchDocumentReaderTest.java:175-205': 1.4206686751462156, 'community/document-readers/spring-ai-alibaba-starter-document-reader-elasticsearch/src/test/java/com/alibaba/cloud/ai/document/reader/es/ElasticsearchDocumentReaderTest.java:275-305': 12.436388516111922, 'community/document-readers/spring-ai-alibaba-starter-document-reader-elasticsearch/src/main/java/com/alibaba/cloud/ai/document/reader/es/ElasticsearchDocumentReader.java:0-30': 1.271177101356609, 'community/document-readers/spring-ai-alibaba-starter-document-reader-elasticsearch/src/main/java/com/alibaba/cloud/ai/document/reader/es/ElasticsearchDocumentReader.java:25-55': 1.4032971997212973, 'community/document-readers/spring-ai-alibaba-starter-document-reader-elasticsearch/src/main/java/com/alibaba/cloud/ai/document/reader/es/ElasticsearchDocumentReader.java:175-205': 2.021144748148701, 'community/document-readers/spring-ai-alibaba-starter-document-reader-elasticsearch/src/main/java/com/alibaba/cloud/ai/document/reader/es/ElasticsearchConfig.java:0-30': 2.2393333571830687, 'community/document-readers/spring-ai-alibaba-starter-document-reader-elasticsearch/src/main/java/com/alibaba/cloud/ai/document/reader/es/ElasticsearchConfig.java:50-80': 2.021144748148701, 'community/document-readers/spring-ai-alibaba-starter-document-reader-tencent-cos/src/test/java/com/alibaba/cloud/ai/reader/tencent/cos/TencentCosDocumentLoaderIT.java:0-30': 1.390544851406706, 'community/document-readers/spring-ai-alibaba-starter-document-reader-tencent-cos/src/main/java/com/alibaba/cloud/ai/reader/tencent/cos/TencentCosDocumentReader.java:0-30': 1.5295406001183258, 'community/document-readers/spring-ai-alibaba-starter-document-reader-tencent-cos/src/main/java/com/alibaba/cloud/ai/reader/tencent/cos/TencentCosResource.java:0-30': 1.4032971997212973, 'community/document-readers/spring-ai-alibaba-starter-document-reader-tencent-cos/src/main/java/com/alibaba/cloud/ai/reader/tencent/cos/TencentCosResource.java:200-230': 1.9576222845269413, 'community/document-readers/spring-ai-alibaba-starter-document-reader-tencent-cos/src/main/java/com/alibaba/cloud/ai/reader/tencent/cos/TencentCosResource.java:225-255': 1.2572512301815688, 'community/document-readers/spring-ai-alibaba-starter-document-reader-tencent-cos/src/main/java/com/alibaba/cloud/ai/reader/tencent/cos/TencentCosResource.java:250-280': 1.9359099717752393, 'community/document-readers/spring-ai-alibaba-starter-document-reader-tencent-cos/src/main/java/com/alibaba/cloud/ai/reader/tencent/cos/TencentCredentials.java:0-30': 1.5244612091889669, 'community/document-readers/spring-ai-alibaba-starter-document-reader-obsidian/src/test/java/com/alibaba/cloud/ai/reader/obsidian/ObsidianDocumentReaderIT.java:0-30': 1.4946794771655387, 'community/document-readers/spring-ai-alibaba-starter-document-reader-obsidian/src/test/java/com/alibaba/cloud/ai/reader/obsidian/ObsidianDocumentReaderIT.java:50-80': 1.452126563470489, 'community/document-readers/spring-ai-alibaba-starter-document-reader-obsidian/src/main/java/com/alibaba/cloud/ai/reader/obsidian/ObsidianResource.java:0-30': 1.5194154424953983, 'community/document-readers/spring-ai-alibaba-starter-document-reader-obsidian/src/main/java/com/alibaba/cloud/ai/reader/obsidian/ObsidianResource.java:50-80': 1.182747793947089, 'community/document-readers/spring-ai-alibaba-starter-document-reader-obsidian/src/main/java/com/alibaba/cloud/ai/reader/obsidian/ObsidianResource.java:75-105': 1.2641758158808274, 'community/document-readers/spring-ai-alibaba-starter-document-reader-obsidian/src/main/java/com/alibaba/cloud/ai/reader/obsidian/ObsidianDocumentReader.java:0-30': 2.1009743252108466, 'community/document-readers/spring-ai-alibaba-starter-document-reader-obsidian/src/main/java/com/alibaba/cloud/ai/reader/obsidian/ObsidianDocumentReader.java:25-55': 1.6043800433693445, 'community/document-readers/spring-ai-alibaba-starter-document-reader-onenote/src/test/java/com/alibaba/cloud/api/reader/onenote/OneNoteDocumentReaderTest.java:0-30': 1.5660667218567792, 'community/document-readers/spring-ai-alibaba-starter-document-reader-onenote/src/main/java/com/alibaba/cloud/api/reader/onenote/OneNoteResource.java:0-30': 1.5987923371942092, 'community/document-readers/spring-ai-alibaba-starter-document-reader-onenote/src/main/java/com/alibaba/cloud/api/reader/onenote/OneNoteDocumentReader.java:0-30': 1.4429973314407667, 'community/document-readers/spring-ai-alibaba-starter-document-reader-onenote/src/main/java/com/alibaba/cloud/api/reader/onenote/OneNoteDocumentReader.java:75-105': 3.569758733665551, 'community/document-readers/spring-ai-alibaba-starter-document-reader-onenote/src/main/java/com/alibaba/cloud/api/reader/onenote/OneNoteDocumentReader.java:100-130': 1.9048226335943024, 'community/document-readers/spring-ai-alibaba-starter-document-reader-onenote/src/main/java/com/alibaba/cloud/api/reader/onenote/OneNoteDocumentReader.java:125-155': 3.332460411364238, 'community/document-readers/spring-ai-alibaba-starter-document-reader-onenote/src/main/java/com/alibaba/cloud/api/reader/onenote/OneNoteDocumentReader.java:150-180': 1.9576222845269413, 'community/document-readers/spring-ai-alibaba-starter-document-reader-onenote/src/main/java/com/alibaba/cloud/api/reader/onenote/OneNoteDocumentReader.java:175-205': 1.9288333297066318, 'community/document-readers/spring-ai-alibaba-starter-document-reader-onenote/src/main/java/com/alibaba/cloud/api/reader/onenote/OneNoteDocumentReader.java:200-230': 4.094303612787326, 'community/document-readers/spring-ai-alibaba-starter-document-reader-onenote/src/main/java/com/alibaba/cloud/api/reader/onenote/OneNoteDocumentReader.java:225-255': 1.5660667218567792, 'community/document-readers/spring-ai-alibaba-starter-document-reader-gpt-repo/src/test/java/com/alibaba/cloud/ai/reader/gptrepo/GptRepoDocumentReaderTest.java:0-30': 1.4946794771655387, 'community/document-readers/spring-ai-alibaba-starter-document-reader-gpt-repo/src/main/java/com/alibaba/cloud/ai/reader/gptrepo/GptRepoDocumentReader.java:0-30': 2.0961775092284123, 'community/document-readers/spring-ai-alibaba-starter-document-reader-gpt-repo/src/main/java/com/alibaba/cloud/ai/reader/gptrepo/GptRepoDocumentReader.java:25-55': 4.610445318904228, 'community/document-readers/spring-ai-alibaba-starter-document-reader-gpt-repo/src/main/java/com/alibaba/cloud/ai/reader/gptrepo/GptRepoDocumentReader.java:50-80': 1.4850091437813338, 'community/document-readers/spring-ai-alibaba-starter-document-reader-gitbook/README.md:0-30': 2.2284626359745925, 'community/document-readers/spring-ai-alibaba-starter-document-reader-gitbook/README.md:50-80': 2.930510043901812, 'community/document-readers/spring-ai-alibaba-starter-document-reader-gitbook/README.md:75-105': 4.401542195876399, 'community/document-readers/spring-ai-alibaba-starter-document-reader-gitbook/src/test/java/com/alibaba/cloud/ai/reader/gitbook/GitbookDocumentReaderTest.java:0-30': 1.4162856102690216, 'community/document-readers/spring-ai-alibaba-starter-document-reader-gitbook/src/test/java/com/alibaba/cloud/ai/reader/gitbook/GitbookDocumentReaderTest.java:50-80': 2.1451543605784527, 'community/document-readers/spring-ai-alibaba-starter-document-reader-gitbook/src/main/java/com/alibaba/cloud/ai/reader/gitbook/GitbookClient.java:0-30': 1.4206686751462156, 'community/document-readers/spring-ai-alibaba-starter-document-reader-gitbook/src/main/java/com/alibaba/cloud/ai/reader/gitbook/GitbookClient.java:25-55': 2.2725913165823757, 'community/document-readers/spring-ai-alibaba-starter-document-reader-gitbook/src/main/java/com/alibaba/cloud/ai/reader/gitbook/GitbookClient.java:50-80': 1.2782563680468169, 'community/document-readers/spring-ai-alibaba-starter-document-reader-gitbook/src/main/java/com/alibaba/cloud/ai/reader/gitbook/GitbookClient.java:100-130': 1.4384756257963185, 'community/document-readers/spring-ai-alibaba-starter-document-reader-gitbook/src/main/java/com/alibaba/cloud/ai/reader/gitbook/GitbookClient.java:125-155': 4.429535219428092, 'community/document-readers/spring-ai-alibaba-starter-document-reader-gitbook/src/main/java/com/alibaba/cloud/ai/reader/gitbook/GitbookClient.java:150-180': 6.561711130318347, 'community/document-readers/spring-ai-alibaba-starter-document-reader-gitbook/src/main/java/com/alibaba/cloud/ai/reader/gitbook/GitbookClient.java:175-205': 2.0819175888035204, 'community/document-readers/spring-ai-alibaba-starter-document-reader-gitbook/src/main/java/com/alibaba/cloud/ai/reader/gitbook/GitbookDocumentReader.java:0-30': 2.0493871560742165, 'community/document-readers/spring-ai-alibaba-starter-document-reader-gitbook/src/main/java/com/alibaba/cloud/ai/reader/gitbook/GitbookDocumentReader.java:25-55': 1.638744057033641, 'community/document-readers/spring-ai-alibaba-starter-document-reader-gitbook/src/main/java/com/alibaba/cloud/ai/reader/gitbook/GitbookDocumentReader.java:75-105': 1.4660390745170708, 'community/document-readers/spring-ai-alibaba-starter-document-reader-gitbook/src/main/java/com/alibaba/cloud/ai/reader/gitbook/GitbookDocumentReader.java:100-130': 1.6271269812152453, 'community/document-readers/spring-ai-alibaba-starter-document-reader-gitbook/src/main/java/com/alibaba/cloud/ai/reader/gitbook/model/GitbookPage.java:0-30': 2.1451543605784527, 'community/document-readers/spring-ai-alibaba-starter-document-reader-gitbook/src/main/java/com/alibaba/cloud/ai/reader/gitbook/model/GitbookPage.java:50-80': 2.021144748148701, 'community/document-readers/spring-ai-alibaba-starter-document-reader-gitbook/src/main/java/com/alibaba/cloud/ai/reader/gitbook/model/GitbookSpace.java:0-30': 2.1964746382642875, 'community/document-readers/spring-ai-alibaba-starter-document-reader-github/src/test/java/com/alibaba/cloud/ai/reader/github/GitHubDocumentLoaderIT.java:0-30': 1.4295166990043104, 'community/document-readers/spring-ai-alibaba-starter-document-reader-github/src/test/java/com/alibaba/cloud/ai/reader/github/GitHubDocumentLoaderIT.java:25-55': 1.4567346345301488, 'community/document-readers/spring-ai-alibaba-starter-document-reader-github/src/test/java/com/alibaba/cloud/ai/reader/github/GitHubDocumentLoaderIT.java:50-80': 1.5044765819016563, 'community/document-readers/spring-ai-alibaba-starter-document-reader-github/src/main/java/com/alibaba/cloud/ai/reader/github/GitHubResource.java:0-30': 1.4995620278218964, 'community/document-readers/spring-ai-alibaba-starter-document-reader-github/src/main/java/com/alibaba/cloud/ai/reader/github/GitHubDocumentReader.java:0-30': 1.5660667218567792, 'community/document-readers/spring-ai-alibaba-starter-document-reader-notion/src/test/java/com/alibaba/cloud/ai/reader/notion/NotionDocumentReaderIT.java:0-30': 1.5194154424953983, 'community/document-readers/spring-ai-alibaba-starter-document-reader-notion/src/main/java/com/alibaba/cloud/ai/reader/notion/NotionDocumentReader.java:0-30': 1.5144029672636237, 'community/document-readers/spring-ai-alibaba-starter-document-reader-notion/src/main/java/com/alibaba/cloud/ai/reader/notion/NotionDocumentReader.java:50-80': 1.576825380051758, 'community/document-readers/spring-ai-alibaba-starter-document-reader-notion/src/main/java/com/alibaba/cloud/ai/reader/notion/NotionDocumentReader.java:75-105': 1.8325829169733974, 'community/document-readers/spring-ai-alibaba-starter-document-reader-notion/src/main/java/com/alibaba/cloud/ai/reader/notion/NotionResource.java:0-30': 1.461372044515705, 'community/document-readers/spring-ai-alibaba-starter-document-reader-notion/src/main/java/com/alibaba/cloud/ai/reader/notion/NotionResource.java:25-55': 1.7715501919595864, 'community/document-readers/spring-ai-alibaba-starter-document-reader-notion/src/main/java/com/alibaba/cloud/ai/reader/notion/NotionResource.java:100-130': 2.670948728003196, 'community/document-readers/spring-ai-alibaba-starter-document-reader-notion/src/main/java/com/alibaba/cloud/ai/reader/notion/NotionResource.java:125-155': 2.357708124431884, 'community/document-readers/spring-ai-alibaba-starter-document-reader-notion/src/main/java/com/alibaba/cloud/ai/reader/notion/NotionResource.java:150-180': 2.3843393571225806, 'community/document-readers/spring-ai-alibaba-starter-document-reader-notion/src/main/java/com/alibaba/cloud/ai/reader/notion/NotionResource.java:200-230': 2.4254336861032075, 'community/document-readers/spring-ai-alibaba-starter-document-reader-notion/src/main/java/com/alibaba/cloud/ai/reader/notion/NotionResource.java:250-280': 2.7854959621632487, 'community/document-readers/spring-ai-alibaba-starter-document-reader-poi/src/test/java/com/alibaba/cloud/ai/reader/poi/ApachePoiDocumentParserTest.java:0-30': 1.9787176467773766, 'community/document-readers/spring-ai-alibaba-starter-document-reader-poi/src/test/java/com/alibaba/cloud/ai/reader/poi/ApachePoiDocumentParserTest.java:25-55': 1.2890243568454003, 'community/document-readers/spring-ai-alibaba-starter-document-reader-poi/src/main/java/com/alibaba/cloud/ai/reader/poi/PoiDocumentReader.java:0-30': 1.461372044515705, 'community/document-readers/spring-ai-alibaba-starter-document-reader-poi/src/main/java/com/alibaba/cloud/ai/reader/poi/PoiDocumentReader.java:25-55': 1.539801608104721, 'community/document-readers/spring-ai-alibaba-starter-document-reader-poi/src/main/java/com/alibaba/cloud/ai/reader/poi/PoiDocumentReader.java:50-80': 1.4802207498974258, 'spring-ai-alibaba-graph/README.md:0-30': 6.818478564921302, 'spring-ai-alibaba-graph/README.md:25-55': 2.8749687364454237, 'spring-ai-alibaba-graph/README.md:50-80': 1.7897149389729103, 'spring-ai-alibaba-graph/README.md:75-105': 4.68433634498754, 'spring-ai-alibaba-graph/README.md:100-130': 2.427343011948134, 'spring-ai-alibaba-graph/README.md:125-155': 2.2336457229180158, 'spring-ai-alibaba-graph/README.md:150-180': 2.520444315043768, 'spring-ai-alibaba-graph/README.md:175-205': 1.214026927770339, 'spring-ai-alibaba-graph/README.md:225-255': 2.1830004502966376, 'spring-ai-alibaba-graph/README.md:250-280': 1.9288333297066318, 'spring-ai-alibaba-graph/README.md:275-305': 12.877031117715237, 'spring-ai-alibaba-graph/README-zh.md:75-105': 2.3387012566452765, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/README.md:0-30': 3.5607889881747634, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/README.md:25-55': 3.3622795054565056, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/README.md:250-280': 3.122647793816758, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/README.md:325-355': 3.1365803006295225, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/graph-ui/.stylelintrc.js:0-30': 1.806405830545199, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/graph-ui/typings.d.ts:0-30': 2.0725182748781217, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/graph-ui/typings.d.ts:25-55': 2.233884771648712, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/graph-ui/README.md:100-130': 1.9691264049738522, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/graph-ui/.umirc.ts:0-30': 2.0725182748781217, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/graph-ui/.umirc.ts:25-55': 5.938194633133432, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/graph-ui/.umirc.ts:50-80': 4.652195436533873, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/graph-ui/.eslintrc.js:0-30': 1.8426605816446973, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/graph-ui/src/app.ts:0-30': 2.0725182748781217, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/graph-ui/src/app.ts:25-55': 2.125291522339446, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/graph-ui/src/locales/zh-CN.ts:0-30': 2.0725182748781217, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/graph-ui/src/locales/zh-CN.ts:25-55': 2.180822559135374, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/graph-ui/src/locales/zh-CN.ts:50-80': 3.9067737436138046, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/graph-ui/src/locales/zh-CN.ts:75-105': 4.730385013992759, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/graph-ui/src/locales/zh-CN.ts:100-130': 6.003011367235349, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/graph-ui/src/locales/zh-CN.ts:125-155': 4.177370416971692, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/graph-ui/src/locales/en-US.ts:0-30': 2.0725182748781217, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/graph-ui/src/locales/en-US.ts:25-55': 2.180822559135374, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/graph-ui/src/locales/en-US.ts:50-80': 4.420799592711476, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/graph-ui/src/locales/en-US.ts:75-105': 5.103968123066489, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/graph-ui/src/locales/en-US.ts:100-130': 4.981560699648053, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/graph-ui/src/constants/index.ts:0-30': 2.0725182748781217, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/graph-ui/src/constants/index.ts:25-55': 2.2176969475324566, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/graph-ui/src/utils/NodeUtil.ts:0-30': 2.0725182748781217, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/graph-ui/src/utils/NodeUtil.ts:25-55': 2.0866492893536632, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/graph-ui/src/utils/GraphUtil.ts:0-30': 2.0725182748781217, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/graph-ui/src/utils/GraphUtil.ts:25-55': 2.1602968574702204, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/graph-ui/src/utils/format.ts:0-30': 2.0725182748781217, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/graph-ui/src/utils/format.ts:25-55': 2.2123530160457276, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/graph-ui/src/utils/DSLUtil.ts:0-30': 2.0725182748781217, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/graph-ui/src/utils/DSLUtil.ts:25-55': 2.1351767481159443, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/graph-ui/src/models/DSLModel.ts:0-30': 2.0725182748781217, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/graph-ui/src/models/DSLModel.ts:25-55': 2.155225660967689, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/graph-ui/src/models/global.ts:0-30': 2.0725182748781217, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/graph-ui/src/models/global.ts:25-55': 2.1057931452632648, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/graph-ui/src/components/Guide/index.ts:0-30': 2.0725182748781217, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/graph-ui/src/components/Guide/index.ts:25-55': 2.2176969475324566, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/graph-ui/src/pages/Graph/Design/types.ts:0-30': 2.0725182748781217, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/graph-ui/src/pages/Graph/Design/types.ts:25-55': 2.1705111834422235, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/graph-ui/src/pages/Graph/Design/types/index.ts:0-30': 2.0725182748781217, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/graph-ui/src/pages/Graph/Design/types/index.ts:25-55': 5.331009223785793, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/graph-ui/src/pages/Graph/Design/types/index.ts:50-80': 3.9091253001129207, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/graph-ui/src/store/GraphState.ts:0-30': 2.0725182748781217, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/graph-ui/src/store/GraphState.ts:25-55': 2.155225660967689, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/test/java/com/alibaba/cloud/ai/app/AppDelegateTest.java:0-30': 1.31111391621134, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/test/java/com/alibaba/cloud/ai/saver/AppMemorySaverTest.java:0-30': 1.806405830545199, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/test/java/com/alibaba/cloud/ai/dsl/nodes/LLMNodeDataConverterTest.java:0-30': 1.70572432962589, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/test/java/com/alibaba/cloud/ai/dsl/nodes/VariableAggregatorNodeDataConverterTest.java:0-30': 1.693141844245956, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/test/java/com/alibaba/cloud/ai/dsl/nodes/RetrieverNodeDataConverterTest.java:0-30': 1.6994097969724125, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/test/java/com/alibaba/cloud/ai/dsl/nodes/AnswerNodeDataConverterTest.java:0-30': 1.3339737577322137, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/test/java/com/alibaba/cloud/ai/dsl/nodes/CodeNodeDataConverterTest.java:0-30': 4.8251954893239315, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/test/java/com/alibaba/cloud/ai/dsl/nodes/BranchNodeDataConverterTest.java:0-30': 1.6994097969724125, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/test/java/com/alibaba/cloud/ai/dsl/nodes/EndNodeDataConverterTest.java:0-30': 1.3339737577322137, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/test/java/com/alibaba/cloud/ai/dsl/nodes/StartNodeDataConverterTest.java:0-30': 1.390544851406706, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/test/java/com/alibaba/cloud/ai/dsl/adapters/DifyDSLAdapterTest.java:0-30': 1.7184952272002534, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/test/java/com/alibaba/cloud/ai/dsl/adapters/CustomDSLAdapterTest.java:0-30': 1.4206686751462156, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/SpringAIAlibabaGraphStudio.java:0-30': 5.5031623312434, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/SpringAIAlibabaGraphStudio.java:25-55': 4.4626737159866785, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/config/GraphInitializrConfiguration.java:0-30': 5.029196523569732, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/config/GraphInitializrConfiguration.java:25-55': 5.971357890320185, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/config/GraphProjectGenerationConfiguration.java:0-30': 4.311702348833113, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/config/GraphProjectGenerationConfiguration.java:25-55': 5.305456939799011, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/config/SwaggerConfiguration.java:0-30': 1.5244612091889669, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/config/AppSaverConfiguration.java:0-30': 1.4162856102690216, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/utils/StringTemplateUtil.java:0-30': 1.539801608104721, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/param/DSLParam.java:0-30': 1.6329148574925245, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/param/ProjectGenerateParam.java:0-30': 1.6505282088323439, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/param/CodeGenerateParam.java:0-30': 6.284711559857364, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/param/CodeGenerateParam.java:25-55': 4.853933805746784, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/param/CreateAppParam.java:0-30': 1.6043800433693445, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/controller/GeneratorController.java:0-30': 7.151802086728353, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/controller/GeneratorController.java:25-55': 5.649565601924164, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/controller/AppController.java:0-30': 1.4429973314407667, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/controller/DSLController.java:0-30': 1.4339821696206667, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/controller/RunnerController.java:0-30': 1.4567346345301488, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/saver/AppMemorySaver.java:0-30': 1.576825380051758, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/saver/AppSaver.java:0-30': 1.6624830670303476, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/common/R.java:0-30': 4.879385439276703, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/common/R.java:25-55': 5.7214865305344125, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/common/R.java:50-80': 5.708831420241527, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/common/R.java:75-105': 5.5935028396439685, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/common/ReturnCode.java:0-30': 6.957486389902649, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/common/ReturnCode.java:25-55': 5.94995342756166, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/model/VariableType.java:0-30': 1.638744057033641, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/model/AppModeEnum.java:0-30': 1.6746123679952611, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/model/VariableSelector.java:0-30': 1.693141844245956, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/model/VariableSelector.java:25-55': 1.9037955790598078, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/model/Variable.java:0-30': 1.6446150239729562, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/model/Variable.java:25-55': 1.8279854392172998, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/model/RunEvent.java:0-30': 1.539801608104721, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/model/App.java:0-30': 1.5987923371942092, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/model/AppMetadata.java:0-30': 1.5932434174609875, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/model/chatbot/ChatBot.java:0-30': 1.8207351761199357, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/model/workflow/Case.java:0-30': 1.65648406863643, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/model/workflow/Node.java:0-30': 1.65648406863643, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/model/workflow/EdgeType.java:0-30': 1.7184952272002534, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/model/workflow/Graph.java:0-30': 1.6685256744000492, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/model/workflow/NodeData.java:0-30': 1.5607422602136187, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/model/workflow/NodeType.java:0-30': 1.6869199579366625, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/model/workflow/NodeType.java:25-55': 5.06756659489052, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/model/workflow/Edge.java:0-30': 1.6869199579366625, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/model/workflow/Workflow.java:0-30': 1.638744057033641, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/model/workflow/nodedata/LLMNodeData.java:0-30': 1.4707360092264137, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/model/workflow/nodedata/LLMNodeData.java:225-255': 3.779590667292789, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/model/workflow/nodedata/LLMNodeData.java:250-280': 4.881657388458004, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/model/workflow/nodedata/LLMNodeData.java:275-305': 2.8796170630316986, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/model/workflow/nodedata/MCPNodeData.java:0-30': 1.4898286183391802, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/model/workflow/nodedata/VariableAggregatorNodeData.java:0-30': 1.5094234550964287, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/model/workflow/nodedata/EndNodeData.java:0-30': 1.4898286183391802, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/model/workflow/nodedata/ToolNodeData.java:0-30': 1.5144029672636237, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/model/workflow/nodedata/HttpNodeData.java:0-30': 1.3738979381611398, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/model/workflow/nodedata/KnowledgeRetrievalNodeData.java:0-30': 1.4384756257963185, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/model/workflow/nodedata/CodeNodeData.java:0-30': 6.064279129126407, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/model/workflow/nodedata/CodeNodeData.java:25-55': 6.121344768453906, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/model/workflow/nodedata/CodeNodeData.java:50-80': 5.767078113745032, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/model/workflow/nodedata/ParameterParsingNodeData.java:0-30': 2.0866492893536632, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/model/workflow/nodedata/ParameterParsingNodeData.java:25-55': 1.5194154424953983, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/model/workflow/nodedata/ListOperatorNodeData.java:0-30': 1.4384756257963185, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/model/workflow/nodedata/RetrieverNodeData.java:0-30': 1.4206686751462156, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/model/workflow/nodedata/QuestionClassifierNodeData.java:0-30': 1.544983913259478, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/model/workflow/nodedata/QuestionClassifierNodeData.java:150-180': 3.8206738977755452, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/model/workflow/nodedata/QuestionClassifierNodeData.java:175-205': 3.779590667292789, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/model/workflow/nodedata/QuestionClassifierNodeData.java:200-230': 4.8892020996999, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/model/workflow/nodedata/QuestionClassifierNodeData.java:225-255': 2.8796170630316986, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/model/workflow/nodedata/QuestionClassifierNodeData.java:250-280': 2.5367149247779235, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/model/workflow/nodedata/HumanNodeData.java:0-30': 1.5502012189992864, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/model/workflow/nodedata/DocumentExtractorNodeData.java:0-30': 1.544983913259478, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/model/workflow/nodedata/AnswerNodeData.java:0-30': 1.452126563470489, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/model/workflow/nodedata/StartNodeData.java:0-30': 1.5144029672636237, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/model/workflow/nodedata/BranchNodeData.java:0-30': 1.5244612091889669, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/format/ApplicationYamlContributor.java:0-30': 1.5502012189992864, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/format/EclipseJdtFormatProjectContributor.java:0-30': 5.102875270634525, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/format/EclipseJdtFormatProjectContributor.java:25-55': 5.028634209654279, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/format/EclipseJdtFormatProjectContributor.java:50-80': 4.986797889914808, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/api/DSLAPI.java:0-30': 1.3697983017004274, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/api/AppAPI.java:0-30': 1.3496617171995993, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/api/RunnerAPI.java:0-30': 1.3417718832367183, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/app/AppDelegateImpl.java:0-30': 1.4995620278218964, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/app/AppDelegate.java:0-30': 1.587732881718793, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/runner/Runner.java:0-30': 1.5607422602136187, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/runner/RunnableModel.java:0-30': 1.813542198650695, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/generator/GraphProjectReqToDescConverter.java:0-30': 1.3457052358459312, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/generator/GraphProjectReqToDescConverter.java:50-80': 1.2538173069550065, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/generator/GraphProjectReqToDescConverter.java:175-205': 1.3821712726179565, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/generator/GraphProjectRequest.java:0-30': 1.5987923371942092, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/generator/GraphProjectContributor.java:0-30': 1.4995620278218964, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/generator/GraphProjectDescription.java:0-30': 1.587732881718793, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/generator/ProjectGenerator.java:0-30': 4.792193106215798, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/generator/CodeGenerator.java:0-30': 9.481364866970079, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/generator/CodeGenerator.java:25-55': 5.300870077135432, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/generator/GraphAppPropertiesCustomizer.java:0-30': 1.5194154424953983, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/generator/GraphAppPropertiesCustomizer.java:25-55': 2.3466714716180768, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/generator/GraphAppPropertiesCustomizer.java:50-80': 1.8804004116084223, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/generator/workflow/NodeSection.java:0-30': 1.6213799903421466, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/generator/workflow/WorkflowProjectGenerator.java:0-30': 3.569758733665551, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/generator/workflow/WorkflowProjectGenerator.java:25-55': 2.262926338691766, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/generator/workflow/WorkflowProjectGenerator.java:50-80': 3.6872067510901667, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/generator/workflow/WorkflowProjectGenerator.java:75-105': 3.555241762302633, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/generator/workflow/WorkflowProjectGenerator.java:100-130': 4.924603927592207, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/generator/workflow/WorkflowProjectGenerator.java:125-155': 1.3224450553019844, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/generator/workflow/WorkflowProjectGenerator.java:200-230': 1.3990205075566946, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/generator/workflow/WorkflowProjectGenerator.java:300-330': 2.8477828903546665, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/generator/workflow/WorkflowProjectGenerator.java:325-355': 1.3536415319003627, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/generator/workflow/WorkflowProjectGenerator.java:350-380': 3.984432318996469, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/generator/workflow/sections/VariableAggregatorNodeSection.java:0-30': 1.4206686751462156, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/generator/workflow/sections/VariableAggregatorNodeSection.java:25-55': 1.9008788485373251, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/generator/workflow/sections/VariableAggregatorNodeSection.java:50-80': 1.2890243568454003, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/generator/workflow/sections/HumanNodeSection.java:0-30': 1.4754631369960434, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/generator/workflow/sections/HumanNodeSection.java:25-55': 2.6625263904582317, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/generator/workflow/sections/HumanNodeSection.java:50-80': 3.695275151572466, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/generator/workflow/sections/ParameterParsingNodeSection.java:0-30': 1.4475475537922684, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/generator/workflow/sections/BranchNodeSection.java:0-30': 1.4295166990043104, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/generator/workflow/sections/QuestionClassifierNodeSection.java:0-30': 1.452126563470489, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/generator/workflow/sections/KnowledgeRetrievalNodeSection.java:0-30': 1.4206686751462156, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/generator/workflow/sections/AnswerNodeSection.java:0-30': 1.4295166990043104, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/generator/workflow/sections/LLMNodeSection.java:0-30': 1.4475475537922684, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/generator/workflow/sections/EndNodeSection.java:0-30': 1.4802207498974258, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/generator/workflow/sections/MCPNodeSection.java:0-30': 1.4660390745170708, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/generator/workflow/sections/CodeNodeSection.java:0-30': 5.716575118500072, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/generator/workflow/sections/CodeNodeSection.java:25-55': 5.899117617672262, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/generator/workflow/sections/ListOperatorNodeSection.java:0-30': 1.4660390745170708, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/generator/workflow/sections/ToolNodeSection.java:0-30': 1.4707360092264137, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/generator/workflow/sections/HttpNodeSection.java:0-30': 1.4250789533148378, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/generator/workflow/sections/DocumentExtractorNodeSection.java:0-30': 1.4660390745170708, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/generator/workflow/sections/StartNodeSection.java:0-30': 1.4802207498974258, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/dsl/AbstractNodeDataConverter.java:0-30': 2.0046400786841847, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/dsl/AbstractNodeDataConverter.java:25-55': 1.3821712726179565, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/dsl/DSLAdapter.java:0-30': 2.186015073891746, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/dsl/DSLDialectType.java:0-30': 1.6746123679952611, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/dsl/NodeDataConverter.java:0-30': 2.0772072989769175, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/dsl/NodeDataConverter.java:25-55': 1.4707360092264137, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/dsl/NodeDataConverter.java:50-80': 2.3073545395254653, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/dsl/AbstractDSLAdapter.java:0-30': 2.0866492893536632, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/dsl/AbstractDSLAdapter.java:25-55': 1.3339737577322137, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/dsl/Serializer.java:0-30': 1.6746123679952611, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/dsl/nodes/ToolNodeDataConverter.java:0-30': 1.4250789533148378, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/dsl/nodes/ToolNodeDataConverter.java:25-55': 1.4295166990043104, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/dsl/nodes/ParameterParsingNodeDataConverter.java:0-30': 1.4076001190620582, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/dsl/nodes/ParameterParsingNodeDataConverter.java:25-55': 1.4076001190620582, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/dsl/nodes/LLMNodeDataConverter.java:0-30': 1.394769803506785, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/dsl/nodes/LLMNodeDataConverter.java:25-55': 1.4946794771655387, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/dsl/nodes/LLMNodeDataConverter.java:50-80': 2.091447863306145, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/dsl/nodes/LLMNodeDataConverter.java:75-105': 3.77305884851681, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/dsl/nodes/LLMNodeDataConverter.java:175-205': 3.9564648751537903, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/dsl/nodes/RetrieverNodeDataConverter.java:0-30': 1.4162856102690216, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/dsl/nodes/AnswerNodeDataConverter.java:0-30': 1.3697983017004274, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/dsl/nodes/EndNodeDataConverter.java:0-30': 1.3738979381611398, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/dsl/nodes/VariableAggregatorNodeDataConverter.java:0-30': 1.3496617171995993, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/dsl/nodes/DocumentExtractorNodeDataConverter.java:0-30': 1.3780221876928578, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/dsl/nodes/HumanNodeDataConverter.java:0-30': 1.4250789533148378, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/dsl/nodes/StartNodeDataConverter.java:0-30': 1.3262657509484723, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/dsl/nodes/CodeNodeDataConverter.java:0-30': 3.8893176904262488, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/dsl/nodes/CodeNodeDataConverter.java:25-55': 5.499837280222778, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/dsl/nodes/CodeNodeDataConverter.java:50-80': 5.394339295110455, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/dsl/nodes/CodeNodeDataConverter.java:75-105': 5.37404461617809, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/dsl/nodes/CodeNodeDataConverter.java:100-130': 5.035481672074022, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/dsl/nodes/ListOperatorNodeDataConverter.java:0-30': 1.4076001190620582, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/dsl/nodes/ListOperatorNodeDataConverter.java:25-55': 1.4076001190620582, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/dsl/nodes/HttpNodeDataConverter.java:0-30': 1.2470054290998271, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/dsl/nodes/HttpNodeDataConverter.java:25-55': 1.3576448869712883, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/dsl/nodes/MCPNodeDataConverter.java:0-30': 1.4295166990043104, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/dsl/nodes/MCPNodeDataConverter.java:25-55': 1.4802207498974258, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/dsl/nodes/QuestionClassifyNodeDataConverter.java:0-30': 1.2676667917202797, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/dsl/nodes/QuestionClassifyNodeDataConverter.java:75-105': 3.196093111568914, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/dsl/nodes/QuestionClassifyNodeDataConverter.java:125-155': 2.8404073615045973, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/dsl/nodes/QuestionClassifyNodeDataConverter.java:150-180': 2.9662499138976615, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/dsl/nodes/BranchNodeDataConverter.java:0-30': 1.3576448869712883, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/dsl/nodes/KnowledgeRetrievalNodeDataConverter.java:0-30': 1.3148693254696644, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/dsl/serialize/YamlSerializer.java:0-30': 1.5660667218567792, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/dsl/serialize/JsonSerializer.java:0-30': 1.4567346345301488, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/dsl/adapters/DifyDSLAdapter.java:0-30': 3.6702794210250245, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/dsl/adapters/DifyDSLAdapter.java:25-55': 3.639533777055803, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/dsl/adapters/DifyDSLAdapter.java:50-80': 3.4988076847842673, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/dsl/adapters/DifyDSLAdapter.java:125-155': 4.276710217313111, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/dsl/adapters/DifyDSLAdapter.java:150-180': 3.3910417135755178, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/dsl/adapters/DifyDSLAdapter.java:200-230': 1.6908344808807734, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/dsl/adapters/DifyDSLAdapter.java:225-255': 1.8891448720732436, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/dsl/adapters/CustomDSLAdapter.java:0-30': 1.3148693254696644, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/service/dsl/adapters/CustomDSLAdapter.java:25-55': 1.3990205075566946, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/exception/RestExceptionHandler.java:0-30': 3.9966497529732257, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/exception/RestExceptionHandler.java:25-55': 5.275698454498672, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/exception/RestExceptionHandler.java:50-80': 5.643421133163019, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/exception/NotImplementedException.java:0-30': 6.979953128884415, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/exception/NotImplementedException.java:25-55': 5.999722714140041, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/exception/NotImplementedException.java:50-80': 5.681320755896346, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/exception/SerializationException.java:0-30': 6.645974325034188, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/exception/SerializationException.java:25-55': 5.908767141146877, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/exception/NotFoundException.java:0-30': 6.979953128884415, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-studio/src/main/java/com/alibaba/cloud/ai/exception/NotFoundException.java:25-55': 6.0095770227223815, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/org/springframework/ai/tool/function/FunctionToolCallback.java:0-30': 1.5044765819016563, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/GraphApplication.java:0-30': 1.4339821696206667, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/config/AiConfig.java:0-30': 2.0725182748781217, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/config/AiConfig.java:25-55': 1.813542198650695, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/stream/LLmSearchStreamController.java:0-30': 1.2302952180021771, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/stream/LLmSearchStreamController.java:25-55': 1.88405569391203, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/stream/LLmSearchStreamController.java:50-80': 4.266139827322856, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/stream/LLmSearchStreamController.java:75-105': 2.071364940362281, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/stream/LLmSearchStreamController.java:150-180': 2.5185388473191925, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/stream/node/ResultNode.java:0-30': 1.4898286183391802, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/stream/node/LLmNode.java:0-30': 1.3738979381611398, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/stream/node/LLmNode.java:25-55': 2.278230591642821, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/stream/node/BaiduSearchNode.java:0-30': 1.3780221876928578, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/stream/node/TavilySearchNode.java:0-30': 1.4339821696206667, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/openmanus/OpenmanusController.java:0-30': 1.2854149260948562, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/openmanus/OpenmanusController.java:75-105': 1.6877262922473488, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/openmanus/SupervisorAgent.java:0-30': 1.452126563470489, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/openmanus/SupervisorAgent.java:50-80': 2.9407163719699514, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/openmanus/SupervisorAgent.java:75-105': 3.591077236003719, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/openmanus/OpenManusPrompt.java:0-30': 1.5044765819016563, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/openmanus/OpenManusPrompt.java:25-55': 7.227827507693886, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/openmanus/OpenManusPrompt.java:50-80': 4.724920709262726, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/openmanus/OpenManusPrompt.java:75-105': 5.121007851485121, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/openmanus/OpenmanusAutoConfiguration.java:0-30': 1.394769803506785, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/openmanus/human/OpenmanusHumanController.java:0-30': 1.2854149260948562, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/openmanus/human/OpenmanusHumanController.java:50-80': 1.4339821696206667, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/openmanus/human/OpenmanusHumanController.java:75-105': 1.709726714166389, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/openmanus/human/OpenmanusHumanController.java:100-130': 1.133117125648299, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/openmanus/human/OpenmanusHumanController.java:125-155': 1.8661062166195947, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/openmanus/human/OpenmanusHumanController.java:150-180': 1.6746123679952611, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/openmanus/tool/BrowserUseTool.java:0-30': 1.3697983017004274, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/openmanus/tool/BrowserUseTool.java:75-105': 3.458688253397736, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/openmanus/tool/BrowserUseTool.java:125-155': 3.7986382565893297, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/openmanus/tool/BrowserUseTool.java:225-255': 1.2538173069550065, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/openmanus/tool/BrowserUseTool.java:275-305': 2.534645878649833, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/openmanus/tool/BrowserUseTool.java:300-330': 1.9660062221768586, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/openmanus/tool/BrowserUseTool.java:325-355': 2.1756546538314656, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/openmanus/tool/Bash.java:0-30': 1.4162856102690216, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/openmanus/tool/Bash.java:25-55': 3.0578650269982477, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/openmanus/tool/Bash.java:50-80': 3.366893167169371, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/openmanus/tool/Builder.java:0-30': 1.3457052358459312, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/openmanus/tool/GoogleSearch.java:0-30': 1.3821712726179565, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/openmanus/tool/GoogleSearch.java:50-80': 1.452126563470489, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/openmanus/tool/FileSaver.java:0-30': 1.4850091437813338, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/openmanus/tool/FileSaver.java:25-55': 1.6156734531778587, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/openmanus/tool/FileSaver.java:50-80': 4.5015222785588165, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/openmanus/tool/PythonExecute.java:0-30': 5.065944995120781, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/openmanus/tool/PythonExecute.java:25-55': 4.8198992182898674, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/openmanus/tool/PythonExecute.java:50-80': 4.353408089848669, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/openmanus/tool/PythonExecute.java:75-105': 5.625577496694452, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/openmanus/tool/DocLoaderTool.java:0-30': 1.4032971997212973, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/openmanus/tool/DocLoaderTool.java:50-80': 1.4032971997212973, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/openmanus/tool/PlanningTool.java:0-30': 1.3738979381611398, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/openmanus/tool/PlanningTool.java:50-80': 2.1351767481159443, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/openmanus/tool/PlanningTool.java:75-105': 2.058577366497676, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/openmanus/tool/PlanningTool.java:400-430': 1.4384756257963185, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/openmanus/tool/PlanningTool.java:425-455': 1.7647398580192688, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/openmanus/tool/Plan.java:0-30': 1.587732881718793, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/openmanus/tool/Summary.java:0-30': 1.3780221876928578, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/openmanus/tool/support/CodeExecutionResult.java:0-30': 4.754787255441469, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/openmanus/tool/support/PlanToolExecuteResult.java:0-30': 1.5932434174609875, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/openmanus/tool/support/ExecuteCommandResult.java:0-30': 4.6696147153478496, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/openmanus/tool/support/ExecuteCommandResult.java:25-55': 5.901436724445677, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/openmanus/tool/support/IpUtils.java:0-30': 1.6271269812152453, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/openmanus/tool/support/ToolExecuteResult.java:0-30': 1.587732881718793, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/openmanus/tool/support/CodeUtils.java:0-30': 1.4898286183391802, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/openmanus/tool/support/CodeUtils.java:25-55': 5.46289984779003, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/openmanus/tool/support/CodeUtils.java:50-80': 5.744217974658001, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/openmanus/tool/support/CodeUtils.java:75-105': 5.381373119727721, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/openmanus/tool/support/CodeUtils.java:100-130': 5.126061148089828, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/openmanus/tool/support/CodeUtils.java:125-155': 5.080933247193559, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/openmanus/tool/support/CodeUtils.java:150-180': 5.579033192375003, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/openmanus/tool/support/CodeUtils.java:175-205': 5.398736846467, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/openmanus/tool/support/llmbash/BashProcess.java:0-30': 1.4660390745170708, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/openmanus/tool/support/llmbash/BashProcess.java:50-80': 4.614062012084614, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/openmanus/tool/support/serpapi/SerpApiService.java:0-30': 1.4339821696206667, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/openmanus/tool/support/serpapi/SerpApiProperties.java:0-30': 1.4850091437813338, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/mcp/OpenMeteoService.java:0-30': 1.4475475537922684, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/mcp/OpenMeteoService.java:50-80': 4.120193104219458, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/mcp/OpenMeteoService.java:75-105': 5.050541574036815, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/mcp/OpenMeteoService.java:125-155': 3.510893034907441, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/mcp/OpenMeteoService.java:150-180': 4.215747542470884, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/mcp/OpenMeteoService.java:175-205': 2.861002387620897, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/mcp/McpAutoConfiguration.java:0-30': 1.3616719918917644, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/mcp/McpController.java:0-30': 1.4162856102690216, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/workflow/WorkflowAutoconfiguration.java:0-30': 1.2538173069550065, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/workflow/RecordingNode.java:0-30': 1.4754631369960434, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/workflow/CustomerServiceController.java:0-30': 1.3616719918917644, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/bigtool/constants/Constant.java:0-30': 1.544983913259478, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/bigtool/utils/MethodUtils.java:0-30': 1.4802207498974258, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/bigtool/utils/MethodUtils.java:50-80': 1.6213799903421466, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/bigtool/utils/MethodUtils.java:125-155': 7.112976932567495, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/bigtool/utils/MethodUtils.java:150-180': 6.034161091202517, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/bigtool/utils/MethodUtils.java:175-205': 4.117287426948538, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/bigtool/agent/Tool.java:0-30': 1.6807436320551328, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/bigtool/agent/ToolAgent.java:0-30': 1.4295166990043104, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/bigtool/agent/ToolAgent.java:50-80': 1.3821712726179565, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/bigtool/agent/CalculateAgent.java:0-30': 1.4567346345301488, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/bigtool/controller/BigToolController.java:0-30': 1.2818256526239191, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/bigtool/service/VectorStoreService.java:0-30': 1.5346539525074083, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/react/ReactController.java:0-30': 1.4250789533148378, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/react/ReactAutoconfiguration.java:0-30': 1.2676667917202797, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/react/ReactAutoconfiguration.java:50-80': 1.544983913259478, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/react/tool/weather/function/WeatherUtils.java:0-30': 1.5660667218567792, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/react/tool/weather/function/WeatherAutoConfiguration.java:0-30': 1.4119295075815892, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/react/tool/weather/function/WeatherService.java:0-30': 1.3990205075566946, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/react/tool/weather/function/WeatherProperties.java:0-30': 1.539801608104721, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/reflection/ReflectionController.java:0-30': 1.3457052358459312, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/reflection/RelectionAutoconfiguration.java:0-30': 1.214026927770339, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/reflection/RelectionAutoconfiguration.java:100-130': 8.098563027984762, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/reflection/RelectionAutoconfiguration.java:125-155': 12.545815206096242, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-example/src/main/java/com/alibaba/cloud/ai/example/graph/reflection/RelectionAutoconfiguration.java:200-230': 1.5346539525074083, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/test/java/com/alibaba/cloud/ai/graph/StateGraphTest.java:0-30': 1.2963043732383113, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/test/java/com/alibaba/cloud/ai/graph/StateGraphTest.java:25-55': 1.8542389282175655, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/test/java/com/alibaba/cloud/ai/graph/StateGraphTest.java:50-80': 1.3378614571544742, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/test/java/com/alibaba/cloud/ai/graph/StateGraphTest.java:225-255': 1.8814023722736681, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/test/java/com/alibaba/cloud/ai/graph/StateGraphTest.java:275-305': 1.2782563680468169, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/test/java/com/alibaba/cloud/ai/graph/StateGraphTest.java:675-705': 2.152295302681449, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/test/java/com/alibaba/cloud/ai/graph/StateGraphTest.java:725-755': 1.9964162770576024, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/test/java/com/alibaba/cloud/ai/graph/StateGraphTest.java:750-780': 2.0790770720562803, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/test/java/com/alibaba/cloud/ai/graph/OverAllStateBuilderTest.java:0-30': 1.5044765819016563, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/test/java/com/alibaba/cloud/ai/graph/OverAllStateBuilderTest.java:25-55': 1.4475475537922684, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/test/java/com/alibaba/cloud/ai/graph/AsyncTest.java:0-30': 1.3378614571544742, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/test/java/com/alibaba/cloud/ai/graph/SerializeTest.java:0-30': 1.3496617171995993, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/test/java/com/alibaba/cloud/ai/graph/SerializeTest.java:25-55': 1.2013188213189965, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/test/java/com/alibaba/cloud/ai/graph/SerializeTest.java:75-105': 1.4076001190620582, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/test/java/com/alibaba/cloud/ai/graph/SerializeTest.java:100-130': 2.2398068645209266, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/test/java/com/alibaba/cloud/ai/graph/SerializeTest.java:175-205': 1.5094234550964287, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/test/java/com/alibaba/cloud/ai/graph/StateGraphRepresentationTest.java:0-30': 1.3657230586340428, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/test/java/com/alibaba/cloud/ai/graph/StateGraphRepresentationTest.java:25-55': 1.2999753052429273, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/test/java/com/alibaba/cloud/ai/graph/StateGraphRepresentationTest.java:50-80': 1.3616719918917644, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/test/java/com/alibaba/cloud/ai/graph/StateGraphRepresentationTest.java:75-105': 1.4850091437813338, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/test/java/com/alibaba/cloud/ai/graph/StateGraphRepresentationTest.java:175-205': 1.3262657509484723, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/test/java/com/alibaba/cloud/ai/graph/StateGraphRepresentationTest.java:275-305': 1.4567346345301488, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/test/java/com/alibaba/cloud/ai/graph/StateGraphRepresentationTest.java:350-380': 1.638744057033641, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/test/java/com/alibaba/cloud/ai/graph/StateGraphMemorySaverTest.java:0-30': 1.2926541151579503, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/test/java/com/alibaba/cloud/ai/graph/StateGraphMemorySaverTest.java:250-280': 1.3262657509484723, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/test/java/com/alibaba/cloud/ai/graph/StateGraphMemorySaverTest.java:325-355': 1.2890243568454003, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/test/java/com/alibaba/cloud/ai/graph/SubGraphTest.java:0-30': 1.3697983017004274, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/test/java/com/alibaba/cloud/ai/graph/SubGraphTest.java:50-80': 1.394769803506785, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/test/java/com/alibaba/cloud/ai/graph/SubGraphTest.java:125-155': 1.3863454179444579, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/test/java/com/alibaba/cloud/ai/graph/SubGraphTest.java:175-205': 1.3821712726179565, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/test/java/com/alibaba/cloud/ai/graph/SubGraphTest.java:275-305': 1.3148693254696644, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/test/java/com/alibaba/cloud/ai/graph/SubGraphTest.java:425-455': 1.0623180817251892, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/test/java/com/alibaba/cloud/ai/graph/GenericType.java:0-30': 1.8207351761199357, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/test/java/com/alibaba/cloud/ai/graph/StateGraphStreamTest.java:0-30': 1.2336013365262284, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/test/java/com/alibaba/cloud/ai/graph/StateGraphStreamTest.java:50-80': 2.0539719812595294, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/test/java/com/alibaba/cloud/ai/graph/StateGraphStreamTest.java:75-105': 1.5346539525074083, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/test/java/com/alibaba/cloud/ai/graph/StateGraphStreamTest.java:100-130': 1.8891448720732436, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/test/java/com/alibaba/cloud/ai/graph/StateGraphStreamTest.java:125-155': 1.4076001190620582, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/test/java/com/alibaba/cloud/ai/graph/StateGraphStreamTest.java:150-180': 1.1858029889787507, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/test/java/com/alibaba/cloud/ai/graph/StateGraphStreamTest.java:300-330': 1.0697446848750967, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/test/java/com/alibaba/cloud/ai/graph/StateGraphStreamTest.java:350-380': 1.394769803506785, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/test/java/com/alibaba/cloud/ai/graph/StateGraphStreamTest.java:375-405': 1.2302952180021771, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/test/java/com/alibaba/cloud/ai/graph/StateGraphStreamTest.java:400-430': 1.461372044515705, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/test/java/com/alibaba/cloud/ai/graph/serializer/SerializerMapper.java:0-30': 1.539801608104721, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/test/java/com/alibaba/cloud/ai/graph/serializer/SerializerMapper.java:75-105': 3.0914523424241187, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/test/java/com/alibaba/cloud/ai/graph/serializer/AgentState.java:0-30': 1.5044765819016563, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/test/java/com/alibaba/cloud/ai/graph/serializer/AgentState.java:75-105': 1.2963043732383113, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/test/java/com/alibaba/cloud/ai/graph/serializer/AgentState.java:150-180': 5.029558819139601, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/test/java/com/alibaba/cloud/ai/graph/serializer/AgentState.java:200-230': 1.813542198650695, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/test/java/com/alibaba/cloud/ai/graph/serializer/ObjectStreamStateSerializer.java:0-30': 1.4567346345301488, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/test/java/com/alibaba/cloud/ai/graph/serializer/ObjectOutputWithMapper.java:0-30': 1.4946794771655387, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/test/java/com/alibaba/cloud/ai/graph/serializer/AppendableValue.java:0-30': 2.2123530160457276, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/test/java/com/alibaba/cloud/ai/graph/serializer/ObjectInputWithMapper.java:0-30': 1.5194154424953983, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/test/java/com/alibaba/cloud/ai/graph/serializer/AppendableValueRW.java:0-30': 1.5660667218567792, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/test/java/com/alibaba/cloud/ai/graph/serializer/AppendableValueRW.java:25-55': 2.278230591642821, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/test/java/com/alibaba/cloud/ai/graph/checkpoint/RedisSaverTest.java:0-30': 1.390544851406706, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/test/java/com/alibaba/cloud/ai/graph/checkpoint/RedisSaverTest.java:50-80': 1.587732881718793, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/test/java/com/alibaba/cloud/ai/graph/checkpoint/RedisSaverTest.java:75-105': 1.6043800433693445, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/test/java/com/alibaba/cloud/ai/graph/checkpoint/RedisSaverTest.java:175-205': 1.5044765819016563, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/test/java/com/alibaba/cloud/ai/graph/checkpoint/CheckpointTest.java:0-30': 1.4206686751462156, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/test/java/com/alibaba/cloud/ai/graph/checkpoint/CheckpointTest.java:25-55': 3.9363005139949134, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/test/java/com/alibaba/cloud/ai/graph/checkpoint/CheckpointTest.java:50-80': 1.7380142090139563, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/test/java/com/alibaba/cloud/ai/graph/plain_text/JacksonSerializerTest.java:0-30': 1.390544851406706, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/test/java/com/alibaba/cloud/ai/graph/stream/LLmNodeAction.java:0-30': 4.041550771316788, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/test/java/com/alibaba/cloud/ai/graph/stream/LLmNodeAction.java:25-55': 2.5389384309958003, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/test/java/com/alibaba/cloud/ai/graph/stream/LLmNodeAction.java:50-80': 4.966923819275253, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/test/java/com/alibaba/cloud/ai/graph/agent/ReactAgentTest.java:0-30': 1.307379897570568, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/test/java/com/alibaba/cloud/ai/graph/agent/ReactAgentTest.java:25-55': 3.9346448401315177, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/test/java/com/alibaba/cloud/ai/graph/agent/ReactAgentTest.java:75-105': 5.525062512790688, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/test/java/com/alibaba/cloud/ai/graph/agent/ReactAgentTest.java:100-130': 6.766036678440742, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/test/java/com/alibaba/cloud/ai/graph/agent/ReactAgentTest.java:125-155': 3.6413356822111913, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/test/java/com/alibaba/cloud/ai/graph/agent/ReactAgentTest.java:150-180': 3.664300225040908, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/test/java/com/alibaba/cloud/ai/graph/agent/ReactAgentTest.java:175-205': 3.2538801164913282, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/test/java/com/alibaba/cloud/ai/graph/agent/ReactAgentTest.java:200-230': 3.552285540648945, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/test/java/com/alibaba/cloud/ai/graph/agent/ReactAgentTest.java:225-255': 1.844508628176459, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/test/java/com/alibaba/cloud/ai/graph/agent/ReactAgentTest.java:250-280': 3.618657188938845, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/test/java/com/alibaba/cloud/ai/graph/node/CodeActionTest.java:0-30': 6.398993585406071, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/test/java/com/alibaba/cloud/ai/graph/node/CodeActionTest.java:25-55': 5.005629708057762, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/test/java/com/alibaba/cloud/ai/graph/node/CodeActionTest.java:50-80': 5.566950451266351, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/test/java/com/alibaba/cloud/ai/graph/node/CodeActionTest.java:75-105': 3.1142568209824133, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/test/java/com/alibaba/cloud/ai/graph/node/CodeActionTest.java:100-130': 5.865820063663004, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/test/java/com/alibaba/cloud/ai/graph/node/CodeActionTest.java:125-155': 5.414170359583678, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/test/java/com/alibaba/cloud/ai/graph/node/KnowledgeRetrievalNodeTest.java:0-30': 1.2890243568454003, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/test/java/com/alibaba/cloud/ai/graph/node/ListOperatorNodeTest.java:0-30': 1.3262657509484723, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/test/java/com/alibaba/cloud/ai/graph/node/ListOperatorNodeTest.java:100-130': 1.858551031644274, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/test/java/com/alibaba/cloud/ai/graph/node/VariableAggregatorNodeTest.java:0-30': 1.4707360092264137, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/test/java/com/alibaba/cloud/ai/graph/node/HttpNodeTest.java:0-30': 1.31111391621134, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/test/java/com/alibaba/cloud/ai/graph/node/HttpNodeTest.java:175-205': 2.370948960573387, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/test/java/com/alibaba/cloud/ai/graph/node/HttpNodeTest.java:200-230': 2.115514316336476, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/test/java/com/alibaba/cloud/ai/graph/node/code/DockerCodeExecutorTest.java:0-30': 6.934110040732387, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/test/java/com/alibaba/cloud/ai/graph/node/code/DockerCodeExecutorTest.java:25-55': 6.012374987199652, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/test/java/com/alibaba/cloud/ai/graph/node/code/DockerCodeExecutorTest.java:50-80': 5.875074630989534, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/CompiledGraph.java:0-30': 1.2572512301815688, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/CompiledGraph.java:25-55': 1.9359399347124375, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/CompiledGraph.java:50-80': 2.1424390785181644, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/CompiledGraph.java:100-130': 1.4206686751462156, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/CompiledGraph.java:125-155': 1.3301085874193976, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/CompiledGraph.java:325-355': 1.3148693254696644, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/CompiledGraph.java:425-455': 1.390544851406706, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/CompiledGraph.java:450-480': 1.3148693254696644, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/CompiledGraph.java:475-505': 1.869906890424954, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/CompiledGraph.java:500-530': 2.4607767994348553, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/CompiledGraph.java:525-555': 3.58543531357939, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/CompiledGraph.java:550-580': 4.001760023132778, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/CompiledGraph.java:750-780': 1.4898286183391802, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/CompiledGraph.java:775-805': 1.2890243568454003, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/CompiledGraph.java:825-855': 2.116636412166311, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/CompiledGraph.java:875-905': 2.091447863306145, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/CompiledGraph.java:1000-1030': 2.885752173114609, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/CompiledGraph.java:1025-1055': 2.5025987753123475, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/CompiledGraph.java:1075-1105': 1.31111391621134, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/CompiledGraph.java:1100-1130': 1.9451795992287988, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/CompiledGraph.java:1125-1155': 1.4707360092264137, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/DiagramGenerator.java:0-30': 4.503256472529186, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/DiagramGenerator.java:25-55': 3.2202428047508467, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/DiagramGenerator.java:50-80': 5.613798671523004, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/DiagramGenerator.java:75-105': 3.882165413782098, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/DiagramGenerator.java:225-255': 1.4384756257963185, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/RunnableConfig.java:0-30': 2.115497405994602, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/RunnableConfig.java:25-55': 6.077557335459806, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/RunnableConfig.java:50-80': 5.6047965283286985, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/RunnableConfig.java:75-105': 2.6965385110276108, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/RunnableConfig.java:100-130': 5.142827253088269, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/RunnableConfig.java:125-155': 6.064279129126407, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/RunnableConfig.java:150-180': 3.9456597344133417, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/RunnableConfig.java:175-205': 1.6446150239729562, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/RunnableConfig.java:200-230': 6.672600374579224, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/RunnableConfig.java:225-255': 3.2710996706369686, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/StateGraph.java:0-30': 1.2641758158808274, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/StateGraph.java:25-55': 1.2302952180021771, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/StateGraph.java:50-80': 1.7853297775722856, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/StateGraph.java:125-155': 1.9829913947974505, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/StateGraph.java:150-180': 1.916752790562206, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/StateGraph.java:175-205': 1.3301085874193976, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/StateGraph.java:200-230': 1.3148693254696644, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/StateGraph.java:275-305': 1.3417718832367183, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/StateGraph.java:300-330': 1.9576222845269413, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/StateGraph.java:350-380': 1.452126563470489, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/StateGraph.java:375-405': 1.5144029672636237, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/StateGraph.java:400-430': 1.3863454179444579, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/StateGraph.java:425-455': 1.4119295075815892, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/StateGraph.java:500-530': 2.534645878649833, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/StateGraph.java:525-555': 3.964184240224791, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/OverAllStateBuilder.java:0-30': 2.0448227535674666, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/OverAllStateBuilder.java:25-55': 5.079451526843398, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/OverAllStateBuilder.java:100-130': 2.1964746382642875, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/OverAllStateBuilder.java:125-155': 2.276837018004281, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/OverAllStateBuilder.java:150-180': 2.508547596964633, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/HasMetadata.java:0-30': 1.5660667218567792, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/SubGraphNode.java:0-30': 1.6329148574925245, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/SubGraphNode.java:25-55': 6.172326594781259, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/OverAllStateFactory.java:0-30': 4.136159153724666, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/OverAllStateFactory.java:25-55': 3.734004562449868, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/OverAllState.java:0-30': 1.5044765819016563, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/OverAllState.java:25-55': 1.3697983017004274, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/OverAllState.java:50-80': 2.730546564155095, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/OverAllState.java:150-180': 2.1602968574702204, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/OverAllState.java:225-255': 2.324372727477271, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/OverAllState.java:275-305': 2.6092849778390717, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/OverAllState.java:300-330': 1.4754631369960434, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/OverAllState.java:325-355': 1.3990205075566946, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/OverAllState.java:400-430': 1.2108247615986183, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/NodeOutput.java:0-30': 1.6329148574925245, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/GraphRepresentation.java:0-30': 5.847876369023656, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/GraphRepresentation.java:25-55': 4.7987608661014045, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/GraphRepresentation.java:50-80': 4.305166444507572, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/KeyStrategy.java:0-30': 5.420104986655041, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/KeyStrategyFactory.java:0-30': 2.110634121140135, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/KeyStrategyFactory.java:25-55': 2.6083212126421005, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/package-info.java:0-30': 10.887467870384752, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/package-info.java:25-55': 2.7214329461883273, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/CompileConfig.java:0-30': 1.4567346345301488, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/CompileConfig.java:25-55': 2.5655372655014466, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/CompileConfig.java:50-80': 1.744619444156759, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/CompileConfig.java:75-105': 2.233884771648712, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/CompileConfig.java:150-180': 1.5660667218567792, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/CompileConfig.java:175-205': 1.555453881109404, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/CompileConfig.java:250-280': 1.7249526580211665, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/GraphLifecycleListener.java:0-30': 4.867641309416314, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/GraphLifecycleListener.java:50-80': 2.16220772806157, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/serializer/StateSerializer.java:0-30': 1.5346539525074083, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/serializer/Serializer.java:0-30': 1.5194154424953983, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/serializer/plain_text/PlainTextStateSerializer.java:0-30': 1.4339821696206667, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/serializer/plain_text/jackson/JacksonStateSerializer.java:0-30': 1.394769803506785, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/serializer/std/NullableObjectSerializer.java:0-30': 1.5295406001183258, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/serializer/std/package-info.java:0-30': 1.65648406863643, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/serializer/check_point/CheckPointSerializer.java:0-30': 1.4429973314407667, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/checkpoint/HasVersions.java:0-30': 1.5932434174609875, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/checkpoint/HasVersions.java:25-55': 3.8206822549555985, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/checkpoint/BaseCheckpointSaver.java:0-30': 1.555453881109404, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/checkpoint/Checkpoint.java:0-30': 1.539801608104721, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/checkpoint/savers/VersionedMemorySaver.java:0-30': 1.4429973314407667, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/checkpoint/savers/VersionedMemorySaver.java:25-55': 2.362775993344414, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/checkpoint/savers/VersionedMemorySaver.java:50-80': 2.549955096050456, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/checkpoint/savers/VersionedMemorySaver.java:100-130': 3.829346060809287, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/checkpoint/savers/VersionedMemorySaver.java:150-180': 4.961509557960117, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/checkpoint/savers/FileSystemSaver.java:0-30': 1.390544851406706, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/checkpoint/savers/FileSystemSaver.java:125-155': 1.5660667218567792, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/checkpoint/savers/FileSystemSaver.java:200-230': 1.4707360092264137, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/checkpoint/savers/RedisSaver.java:0-30': 1.3697983017004274, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/checkpoint/savers/RedisSaver.java:200-230': 1.555453881109404, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/checkpoint/savers/MongoSaver.java:0-30': 1.3378614571544742, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/checkpoint/savers/MongoSaver.java:225-255': 1.712085963376952, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/checkpoint/savers/MemorySaver.java:0-30': 1.4206686751462156, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/checkpoint/config/SaverConfig.java:0-30': 1.4802207498974258, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/checkpoint/constant/SaverConstant.java:0-30': 1.744619444156759, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/diagram/MermaidGenerator.java:0-30': 2.0046400786841847, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/diagram/MermaidGenerator.java:25-55': 1.539801608104721, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/diagram/PlantUMLGenerator.java:0-30': 1.5144029672636237, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/async/AsyncGenerator.java:0-30': 1.4475475537922684, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/async/AsyncGenerator.java:75-105': 2.482957064370015, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/async/AsyncGenerator.java:125-155': 3.6051451234016283, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/async/AsyncGenerator.java:150-180': 3.0815831557115874, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/async/AsyncGenerator.java:175-205': 4.684213970442147, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/async/AsyncGenerator.java:225-255': 3.913769451456074, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/async/AsyncGenerator.java:250-280': 2.9154440725184005, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/async/AsyncGenerator.java:275-305': 3.7959688865593693, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/async/AsyncGenerator.java:325-355': 1.5660667218567792, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/async/AsyncGenerator.java:350-380': 1.5346539525074083, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/async/AsyncGenerator.java:375-405': 1.4802207498974258, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/async/AsyncGeneratorQueue.java:0-30': 1.539801608104721, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/async/AsyncGeneratorQueue.java:50-80': 1.7853297775722856, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/async/AsyncGeneratorQueue.java:75-105': 1.4339821696206667, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/async/AsyncGeneratorQueue.java:100-130': 1.7249526580211665, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/async/FlowGenerator.java:0-30': 2.115497405994602, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/async/FlowGenerator.java:25-55': 6.425917551113829, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/async/FlowGenerator.java:50-80': 4.9779386517872295, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/async/AsyncGeneratorOperators.java:0-30': 1.4995620278218964, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/async/AsyncGeneratorOperators.java:25-55': 1.5346539525074083, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/async/AsyncGeneratorOperators.java:50-80': 2.0493871560742165, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/async/AsyncGeneratorOperators.java:75-105': 4.193198993414353, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/async/AsyncGeneratorOperators.java:100-130': 2.3580291500730404, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/async/internal/UnmodifiableDeque.java:0-30': 1.6329148574925245, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/async/internal/reactive/GeneratorPublisher.java:0-30': 4.401542195876399, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/async/internal/reactive/GeneratorPublisher.java:25-55': 8.868999983543395, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/async/internal/reactive/GeneratorPublisher.java:50-80': 3.2584761210481337, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/async/internal/reactive/GeneratorSubscriber.java:0-30': 2.0866492893536632, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/async/internal/reactive/GeneratorSubscriber.java:25-55': 7.335861517003008, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/async/internal/reactive/GeneratorSubscriber.java:50-80': 4.923544183906407, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/async/internal/reactive/GeneratorSubscriber.java:75-105': 1.638744057033641, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/async/internal/reactive/GeneratorSubscriber.java:100-130': 3.47291411675538, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/internal/edge/EdgeValue.java:0-30': 1.5502012189992864, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/internal/edge/Edge.java:0-30': 1.4475475537922684, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/internal/edge/Edge.java:25-55': 1.6446150239729562, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/internal/edge/Edge.java:50-80': 1.4995620278218964, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/internal/edge/Edge.java:75-105': 1.6446150239729562, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/internal/edge/Edge.java:125-155': 4.987740439686177, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/internal/edge/EdgeCondition.java:0-30': 1.5346539525074083, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/internal/node/Node.java:0-30': 2.0772072989769175, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/internal/node/Node.java:25-55': 5.398154744232642, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/internal/node/Node.java:50-80': 5.612648993886779, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/internal/node/Node.java:100-130': 5.356921202701207, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/internal/node/SubCompiledGraphNode.java:0-30': 1.4032971997212973, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/internal/node/CommandNode.java:0-30': 1.4429973314407667, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/internal/node/ParallelNode.java:0-30': 1.3821712726179565, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/internal/node/SubCompiledGraphNodeAction.java:0-30': 1.5044765819016563, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/internal/node/SubCompiledGraphNodeAction.java:25-55': 4.561374501566698, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/internal/node/SubCompiledGraphNodeAction.java:50-80': 3.322587353419976, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/internal/node/SubStateGraphNode.java:0-30': 1.4898286183391802, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/utils/InMemoryFileStorage.java:0-30': 1.5295406001183258, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/utils/FileUtils.java:0-30': 1.6624830670303476, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/utils/FileUtils.java:25-55': 5.347083419383979, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/utils/TryConsumer.java:0-30': 1.5714276365890276, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/utils/CollectionsUtils.java:0-30': 1.4898286183391802, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/utils/SystemClock.java:0-30': 2.1057931452632648, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/utils/SystemClock.java:25-55': 2.2284626359745925, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/utils/EdgeMappings.java:0-30': 2.125291522339446, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/utils/EdgeMappings.java:25-55': 1.7380142090139563, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/utils/EdgeMappings.java:75-105': 1.744619444156759, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/utils/EdgeMappings.java:100-130': 2.1142306775732047, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/utils/CodeUtils.java:0-30': 4.5713507467184336, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/utils/CodeUtils.java:25-55': 4.58898514665252, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/agent/ReactAgentWithHuman.java:0-30': 1.3990205075566946, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/agent/ReactAgentWithHuman.java:125-155': 1.4032971997212973, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/agent/ReactAgentWithHuman.java:150-180': 1.8002428076383057, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/agent/ReflectAgent.java:0-30': 1.3457052358459312, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/agent/ReflectAgent.java:100-130': 1.9534570733186172, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/agent/ReflectAgent.java:125-155': 1.970225178300757, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/agent/ReflectAgent.java:225-255': 1.8207351761199357, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/agent/ReactAgent.java:0-30': 1.3657230586340428, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/agent/ReactAgent.java:150-180': 2.035754673184634, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/agent/ReactAgent.java:175-205': 1.2402671679441073, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/state/RemoveByHash.java:0-30': 4.1100739442505, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/state/RemoveByHash.java:25-55': 6.957486389902649, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/state/AppenderChannel.java:0-30': 1.5144029672636237, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/state/AppenderChannel.java:50-80': 5.127212811579977, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/state/AppenderChannel.java:75-105': 4.123075291473685, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/state/AppenderChannel.java:100-130': 1.4660390745170708, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/state/AppenderChannel.java:125-155': 1.5502012189992864, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/state/AppenderChannel.java:150-180': 6.102899967210211, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/state/AgentStateFactory.java:0-30': 1.6685256744000492, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/state/Reducer.java:0-30': 1.7715501919595864, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/state/Channel.java:0-30': 1.6505282088323439, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/state/Channel.java:25-55': 6.45407970488612, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/state/Channel.java:50-80': 5.031026351389513, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/state/Channel.java:75-105': 4.573541454692977, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/state/Channel.java:100-130': 6.037912773386549, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/state/Channel.java:125-155': 1.461372044515705, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/state/StateSnapshot.java:0-30': 1.4660390745170708, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/state/StateSnapshot.java:50-80': 1.4119295075815892, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/state/StateSnapshot.java:75-105': 1.806405830545199, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/state/package-info.java:0-30': 2.4337602875157067, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/state/strategy/AppendStrategy.java:0-30': 1.5044765819016563, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/state/strategy/ReplaceStrategy.java:0-30': 1.6329148574925245, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/streaming/AsyncGeneratorUtils.java:0-30': 1.4032971997212973, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/streaming/AsyncGeneratorUtils.java:25-55': 1.3417718832367183, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/streaming/AsyncGeneratorUtils.java:75-105': 1.582260333065363, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/streaming/AsyncGeneratorUtils.java:100-130': 1.576825380051758, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/streaming/StreamingOutput.java:0-30': 1.5144029672636237, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/streaming/StreamingChatGenerator.java:0-30': 4.338063397226261, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/streaming/StreamingChatGenerator.java:25-55': 2.4642443332029242, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/streaming/StreamingChatGenerator.java:75-105': 2.3832201743582417, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/streaming/StreamingChatGenerator.java:100-130': 6.064397076726883, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/action/EdgeAction.java:0-30': 2.180822559135374, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/action/AsyncCommandAction.java:0-30': 1.390544851406706, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/action/AsyncNodeActionWithConfig.java:0-30': 1.4475475537922684, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/action/Command.java:0-30': 4.738001885692194, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/action/Command.java:25-55': 5.947574748880751, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/action/AsyncEdgeAction.java:0-30': 2.1351767481159443, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/action/CommandAction.java:0-30': 1.6100069440831988, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/action/AsyncNodeAction.java:0-30': 2.1057931452632648, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/action/NodeActionWithConfig.java:0-30': 1.5660667218567792, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/action/NodeAction.java:0-30': 1.6446150239729562, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/action/package-info.java:0-30': 2.701621070561738, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/action/package-info.java:25-55': 6.032446831593633, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/action/package-info.java:50-80': 4.897720255355321, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/node/ListOperatorNode.java:0-30': 1.3616719918917644, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/node/ListOperatorNode.java:75-105': 1.3417718832367183, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/node/ListOperatorNode.java:100-130': 1.865120477714578, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/node/ListOperatorNode.java:175-205': 1.8727292772502506, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/node/ListOperatorNode.java:275-305': 1.6746123679952611, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/node/ListOperatorNode.java:300-330': 1.9197184768097486, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/node/ListOperatorNode.java:450-480': 2.7010699486912366, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/node/AnswerNode.java:0-30': 1.4995620278218964, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/node/QuestionClassifierNode.java:0-30': 1.4206686751462156, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/node/QuestionClassifierNode.java:25-55': 2.0902281048334155, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/node/HttpNode.java:0-30': 1.2890243568454003, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/node/HttpNode.java:225-255': 2.4047109682282337, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/node/KnowledgeRetrievalNode.java:0-30': 1.3378614571544742, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/node/DocumentExtractorNode.java:0-30': 1.3301085874193976, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/node/LlmNode.java:0-30': 1.3576448869712883, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/node/ToolNode.java:0-30': 1.4206686751462156, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/node/HumanNode.java:0-30': 1.461372044515705, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/node/HumanNode.java:50-80': 1.576825380051758, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/node/ParameterParsingNode.java:0-30': 1.3496617171995993, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/node/ParameterParsingNode.java:25-55': 1.9915945152377517, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/node/ParameterParsingNode.java:50-80': 2.5025987753123475, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/node/ParameterParsingNode.java:75-105': 2.0448227535674666, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/node/McpNode.java:0-30': 1.3576448869712883, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/node/McpNode.java:50-80': 1.5502012189992864, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/node/McpNode.java:75-105': 1.236925271703581, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/node/BranchNode.java:0-30': 1.4567346345301488, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/node/BranchNode.java:25-55': 1.461372044515705, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/node/VariableAggregatorNode.java:0-30': 1.4707360092264137, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/node/code/DockerCodeExecutor.java:0-30': 6.492093215814574, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/node/code/DockerCodeExecutor.java:25-55': 3.7196936177492654, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/node/code/DockerCodeExecutor.java:50-80': 5.991878243867098, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/node/code/DockerCodeExecutor.java:75-105': 6.942549506524023, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/node/code/DockerCodeExecutor.java:100-130': 3.3910417135755178, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/node/code/DockerCodeExecutor.java:125-155': 6.688648046907424, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/node/code/DockerCodeExecutor.java:150-180': 5.446621374758559, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/node/code/DockerCodeExecutor.java:175-205': 4.853933805746784, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/node/code/TemplateTransformer.java:0-30': 6.048418000006746, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/node/code/TemplateTransformer.java:25-55': 6.79690550941427, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/node/code/TemplateTransformer.java:50-80': 4.265439758423243, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/node/code/TemplateTransformer.java:75-105': 5.0713202963005415, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/node/code/CodeExecutorNodeAction.java:0-30': 7.427376168169537, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/node/code/CodeExecutorNodeAction.java:25-55': 7.313902250175375, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/node/code/CodeExecutorNodeAction.java:50-80': 6.083880382309053, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/node/code/CodeExecutorNodeAction.java:75-105': 8.41166020385862, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/node/code/CodeExecutorNodeAction.java:100-130': 5.632975058715709, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/node/code/CodeExecutorNodeAction.java:125-155': 6.110738579759644, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/node/code/CodeExecutorNodeAction.java:150-180': 5.626177190002114, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/node/code/LocalCommandlineCodeExecutor.java:0-30': 6.560011179285995, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/node/code/LocalCommandlineCodeExecutor.java:25-55': 5.804876024709901, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/node/code/LocalCommandlineCodeExecutor.java:50-80': 6.071504470862028, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/node/code/LocalCommandlineCodeExecutor.java:75-105': 5.787872001504224, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/node/code/LocalCommandlineCodeExecutor.java:100-130': 4.504884478591452, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/node/code/LocalCommandlineCodeExecutor.java:125-155': 2.4324208558455553, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/node/code/LocalCommandlineCodeExecutor.java:150-180': 4.744828092305736, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/node/code/CodeExecutor.java:0-30': 7.132485428102304, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/node/code/CodeExecutor.java:25-55': 7.727674958076844, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/node/code/python3/Python3TemplateTransformer.java:0-30': 6.077557335459806, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/node/code/python3/Python3TemplateTransformer.java:25-55': 3.669101126637336, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/node/code/entity/CodeExecutionResult.java:0-30': 6.998313280406588, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/node/code/entity/CodeExecutionResult.java:25-55': 5.802402662036233, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/node/code/entity/CodeBlock.java:0-30': 6.602632066528943, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/node/code/entity/CodeExecutionConfig.java:0-30': 6.416700569863563, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/node/code/entity/CodeExecutionConfig.java:25-55': 4.330115017784948, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/node/code/entity/CodeExecutionConfig.java:50-80': 5.065691827232039, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/node/code/entity/CodeExecutionConfig.java:75-105': 4.998221938990925, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/node/code/entity/CodeExecutionConfig.java:100-130': 3.1491014221135623, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/node/code/entity/RunnerAndPreload.java:0-30': 5.485430447444259, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/node/code/entity/CodeLanguage.java:0-30': 6.27041635060146, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/node/code/entity/CodeLanguage.java:25-55': 5.340026935389284, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/node/code/java/JavaTemplateTransformer.java:0-30': 6.503953325525826, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/node/code/java/JavaTemplateTransformer.java:25-55': 4.001760023132778, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/node/code/java/JavaTemplateTransformer.java:50-80': 4.766972195222565, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/node/code/javascript/NodeJsTemplateTransformer.java:0-30': 5.505101514530458, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/node/code/javascript/NodeJsTemplateTransformer.java:25-55': 5.650129396700192, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/exception/GraphRunnerException.java:0-30': 1.5502012189992864, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/exception/RunnableErrors.java:0-30': 1.4250789533148378, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/exception/GraphStateException.java:0-30': 1.555453881109404, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/exception/Errors.java:0-30': 1.2999753052429273, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/observation/SpringAiAlibabaKind.java:0-30': 2.5045785784589194, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/observation/GraphMetricsGenerator.java:0-30': 1.3301085874193976, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/observation/GraphMetricsGenerator.java:25-55': 2.370911430089372, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/observation/GraphMetricsGenerator.java:50-80': 1.2108247615986183, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/observation/GraphMetricsGenerator.java:75-105': 1.2963043732383113, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/observation/GraphObservationLifecycleListener.java:0-30': 4.9273737147795735, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/observation/GraphObservationLifecycleListener.java:25-55': 1.3339737577322137, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/observation/GraphObservationLifecycleListener.java:75-105': 1.3738979381611398, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/observation/GraphObservationLifecycleListener.java:100-130': 6.889377535442365, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/observation/GraphObservationLifecycleListener.java:125-155': 9.754770609670398, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/observation/GraphObservationLifecycleListener.java:150-180': 9.622999597365016, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/observation/GraphObservationLifecycleListener.java:175-205': 2.6316778854305483, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/observation/graph/GraphObservationDocumentation.java:0-30': 9.700678150699979, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/observation/graph/GraphObservationDocumentation.java:25-55': 9.207646777648304, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/observation/graph/GraphObservationDocumentation.java:50-80': 1.7784132932580297, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/observation/graph/DefaultGraphObservationConvention.java:0-30': 5.358348177355238, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/observation/graph/DefaultGraphObservationConvention.java:50-80': 6.412022200661974, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/observation/graph/DefaultGraphObservationConvention.java:75-105': 7.573400630072443, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/observation/graph/GraphObservationConvention.java:0-30': 2.395657388917073, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/observation/graph/GraphObservationContext.java:0-30': 2.455456959720817, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/observation/graph/GraphObservationHandler.java:0-30': 2.1057931452632648, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/observation/graph/GraphObservationHandler.java:25-55': 4.186736272545969, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/observation/graph/GraphObservationHandler.java:50-80': 1.4898286183391802, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/observation/edge/GraphEdgeObservationContext.java:0-30': 2.1705111834422235, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/observation/edge/GraphEdgeObservationContext.java:125-155': 1.9197184768097486, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/observation/edge/GraphEdgeObservationHandler.java:0-30': 5.299490726821633, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/observation/edge/GraphEdgeObservationHandler.java:25-55': 7.2326067350306005, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/observation/edge/GraphEdgeObservationHandler.java:50-80': 1.3224450553019844, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/observation/edge/DefaultGraphEdgeObservationConvention.java:0-30': 5.308599763209841, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/observation/edge/DefaultGraphEdgeObservationConvention.java:50-80': 6.297270697668868, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/observation/edge/DefaultGraphEdgeObservationConvention.java:75-105': 8.299575848425846, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/observation/edge/GraphEdgeObservationDocumentation.java:0-30': 8.327235929373844, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/observation/edge/GraphEdgeObservationDocumentation.java:25-55': 7.965319636649646, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/observation/edge/GraphEdgeObservationDocumentation.java:50-80': 2.409784201240354, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/observation/edge/GraphEdgeObservationDocumentation.java:75-105': 1.7853297775722856, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/observation/edge/GraphEdgeObservationConvention.java:0-30': 2.1009743252108466, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/observation/node/GraphNodeObservationHandler.java:0-30': 5.379247542610843, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/observation/node/GraphNodeObservationHandler.java:25-55': 7.326231781291068, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/observation/node/GraphNodeObservationHandler.java:50-80': 1.6156734531778587, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/observation/node/GraphNodeObservationConvention.java:0-30': 2.0448227535674666, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/observation/node/GraphNodeObservationContext.java:0-30': 2.1401539252526356, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/observation/node/GraphNodeObservationContext.java:100-130': 2.133886349869392, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/observation/node/GraphNodeObservationDocumentation.java:0-30': 8.327235929373844, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/observation/node/GraphNodeObservationDocumentation.java:25-55': 7.814888846242231, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/observation/node/GraphNodeObservationDocumentation.java:50-80': 2.3972002588781467, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/observation/node/GraphNodeObservationDocumentation.java:75-105': 1.7853297775722856, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/observation/node/GraphNodeObservationDocumentation.java:100-130': 3.6237286559687565, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/observation/node/DefaultGraphNodeObservationConvention.java:0-30': 5.406411647476638, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/observation/node/DefaultGraphNodeObservationConvention.java:25-55': 5.506498285046794, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/observation/node/DefaultGraphNodeObservationConvention.java:75-105': 8.093054991216341, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/observation/metric/SpringAiAlibabaObservationMetricAttributes.java:0-30': 2.468661647832403, 'spring-ai-alibaba-graph/spring-ai-alibaba-graph-core/src/main/java/com/alibaba/cloud/ai/graph/observation/metric/SpringAiAlibabaObservationMetricNames.java:0-30': 2.473094827535635}
2025-08-13 21:18:14 - utils.logger - INFO - Query 'AI code understanding and analysis techniques - methods and tools for analyzing code structure, dependencies, and patterns using artificial intelligence' Search Failed: 1 validation error for CodeSnippet
start_line
  Input should be greater than or equal to 1 [type=greater_than_equal, input_value='0', input_type=str]
    For further information visit https://errors.pydantic.dev/2.5/v/greater_than_equal
2025-08-13 21:18:14 - utils.logger - INFO - Query 'Code intelligence and AI-assisted development - applications of AI in code completion, refactoring suggestions, and automated documentation generation' Found 3 Code Snippets
2025-08-13 21:18:14 - utils.logger - INFO - Found 3 Code Snippets, Start Filtering...
2025-08-13 21:18:21 - utils.logger - INFO -  Sub Query 'Code intelligence and AI-assisted development - applications of AI in code completion, refactoring suggestions, and automated documentation generation' Filtered 0 Snippets
2025-08-13 21:18:21 - utils.logger - INFO - Filtered Snippets Deduplicated to 0 Unique Snippets
2025-08-13 21:18:21 - utils.logger - INFO - 新查询未找到相关代码，搜索结束
2025-08-13 21:18:21 - utils.logger - INFO - Merging Code Snippets at File Level...
2025-08-13 21:18:21 - utils.logger - INFO - DeepSearch Completed: 搜索摘要:
- 原始查询: 我对代码的ai
- 子查询数量: 0
- 总查询数量: 2
- 找到的代码片段: 0
- 涉及文件数: 0
- 迭代次数: 1

2025-08-13 21:18:21 - utils.logger - INFO - Search Code Snippets:
2025-08-13 21:18:59 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 21:19:22 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 21:19:28 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 21:19:32 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 21:19:41 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 21:19:48 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 21:20:56 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 21:21:02 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 21:21:04 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 21:21:28 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 21:21:34 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 21:21:36 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 21:21:44 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 21:21:45 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 21:21:51 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 21:21:52 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 21:22:31 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 21:23:36 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 21:23:39 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 21:23:41 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 21:23:52 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 21:25:27 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 21:26:18 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 21:26:20 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 21:26:24 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 21:26:27 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 21:27:00 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 21:27:04 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 21:27:13 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 21:27:19 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 21:27:23 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 21:27:43 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 21:27:51 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 21:27:55 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 21:27:58 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 21:28:06 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 21:28:21 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 21:28:25 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 21:28:27 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 21:28:30 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 21:28:37 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 21:28:40 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 21:28:41 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 21:28:43 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 21:28:46 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 21:28:53 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 21:28:58 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 21:29:34 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 21:30:23 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 21:30:48 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 21:31:54 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 21:31:59 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 21:34:07 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 21:34:10 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 21:34:30 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 21:34:36 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 21:34:45 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 21:34:50 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 21:35:41 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 21:35:43 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 21:36:20 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 21:36:25 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 21:36:26 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 21:36:31 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 21:36:35 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
2025-08-13 21:36:40 - utils.logger - INFO - 日志系统初始化完成 - 级别: info, 文件: logs/app.log, 最大大小: 5MB
