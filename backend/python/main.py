import os
import click
import uvicorn
import importlib

@click.command()
@click.option('--env', default='dev', help='Environment to run')
def run(env: str):
    os.environ["ENV"] = env

    # 根据版本获取对应路径下的api并启动app程序
    from core.config import get_config
    api_config = get_config().api

    # 根据API版本动态导入对应的API模块
    version = api_config.version
    api_module_path = f"server.api.v{version}.api"

    try:
        # 动态导入API模块
        # api_module = importlib.import_module(api_module_path)

        # 使用uvicorn启动FastAPI应用
        uvicorn.run(
            f'{api_module_path}:app',
            host=api_config.host,
            port=api_config.port,
            reload=api_config.reload
        )
    except ImportError as e:
        click.echo(f"Error: Cannot import API module '{api_module_path}': {e}", err=True)
        raise click.Abort()
    except AttributeError as e:
        click.echo(f"Error: API module '{api_module_path}' does not have 'app' attribute: {e}", err=True)
        raise click.Abort()

if __name__ == "__main__":
    run()
