from typing import List, Dict
from pydantic import BaseModel, Field, ConfigDict, field_validator


class CodeSnippet(BaseModel):
    """代码片段数据结构"""
    file_path: str = Field(..., description="文件路径", min_length=1)
    start_line: int = Field(..., description="行号", ge=1)
    end_line: int = Field(..., description="行号", ge=1)
    content: str = Field(..., description="代码内容", min_length=1)
    context_before: str = Field(default="", description="前置上下文")
    context_after: str = Field(default="", description="后置上下文")

    def get_full_content(self) -> str:
        """获取完整代码内容，包括上下文"""
        return f"{self.context_before}\n{self.content}\n{self.context_after}"

class SearchResult(BaseModel):
    """搜索结果数据结构"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        frozen=False
    )

    original_query: str = Field(..., description="原始查询字符串", min_length=1)
    sub_queries: List[str] = Field(
        default_factory=list,
        description="子查询列表"
    )
    all_queries: List[str] = Field(
        default_factory=list,
        description="包含生成的新查询的所有查询列表"
    )
    code_snippets: List[CodeSnippet] = Field(
        default_factory=list,
        description="搜索到的代码片段列表"
    )
    file_level_results: Dict[str, str] = Field(
        default_factory=dict,
        description="文件级别的合并结果"
    )
    iterations: int = Field(
        default=0,
        description="搜索迭代次数",
        ge=0
    )

    def get_summary(self) -> str:
        """获取搜索结果摘要"""
        return f"""搜索摘要:
- 原始查询: {self.original_query}
- 子查询数量: {len(self.sub_queries)}
- 总查询数量: {len(self.all_queries)}
- 找到的代码片段: {len(self.code_snippets)}
- 涉及文件数: {len(self.file_level_results)}
- 迭代次数: {self.iterations}
"""