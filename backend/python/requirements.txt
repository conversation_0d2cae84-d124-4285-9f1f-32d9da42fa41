# This file was autogenerated by uv via the following command:
#    uv pip compile ./python/pyproject.toml -o requirements.txt
annotated-types==0.7.0
    # via pydantic
anyio==3.7.1
    # via
    #   fastapi
    #   httpx
    #   openai
    #   starlette
    #   watchfiles
certifi==2025.8.3
    # via
    #   httpcore
    #   httpx
click==8.2.1
    # via
    #   codebase-dev (./python/pyproject.toml)
    #   uvicorn
distro==1.9.0
    # via openai
fastapi==0.104.1
    # via codebase-dev (./python/pyproject.toml)
h11==0.16.0
    # via
    #   httpcore
    #   uvicorn
httpcore==1.0.9
    # via httpx
httptools==0.6.4
    # via uvicorn
httpx==0.28.1
    # via openai
idna==3.10
    # via
    #   anyio
    #   httpx
jiter==0.10.0
    # via openai
markdown-it-py==4.0.0
    # via rich
mdurl==0.1.2
    # via markdown-it-py
openai==1.99.9
    # via codebase-dev (./python/pyproject.toml)
pydantic==2.5.0
    # via
    #   codebase-dev (./python/pyproject.toml)
    #   fastapi
    #   openai
pydantic-core==2.14.1
    # via pydantic
pygments==2.19.2
    # via rich
python-dotenv==1.0.0
    # via
    #   codebase-dev (./python/pyproject.toml)
    #   uvicorn
python-multipart==0.0.6
    # via codebase-dev (./python/pyproject.toml)
pyyaml==6.0.2
    # via
    #   codebase-dev (./python/pyproject.toml)
    #   uvicorn
rich==14.1.0
    # via codebase-dev (./python/pyproject.toml)
sniffio==1.3.1
    # via
    #   anyio
    #   openai
starlette==0.27.0
    # via fastapi
tqdm==4.67.1
    # via openai
typing-extensions==4.14.1
    # via
    #   fastapi
    #   openai
    #   pydantic
    #   pydantic-core
uvicorn==0.24.0
    # via codebase-dev (./python/pyproject.toml)
uvloop==0.21.0
    # via uvicorn
watchfiles==1.1.0
    # via uvicorn
websockets==15.0.1
    # via uvicorn
