#!/usr/bin/env python3
"""
简单的流式传输测试脚本
使用标准库的urllib，不需要额外依赖
"""

import urllib.request
import urllib.parse
import json
import time

def test_streaming_search():
    """测试流式搜索接口"""
    
    # 测试数据
    test_request = {
        "query": "import",
        "workspaceName": "PyMySQL",  # 使用实际存在的工作区
        "searchType": "grep",
        "maxResults": 50
    }
    
    url = "http://localhost:3451/api/v1/search"
    
    print(f"🚀 开始测试流式搜索接口: {url}")
    print(f"📝 测试请求: {json.dumps(test_request, indent=2)}")
    print("-" * 60)
    
    start_time = time.time()
    message_count = 0
    last_message_time = start_time
    
    try:
        # 准备请求数据
        data = json.dumps(test_request).encode('utf-8')
        
        # 创建请求
        req = urllib.request.Request(
            url,
            data=data,
            headers={
                'Content-Type': 'application/json',
                'Accept': 'text/event-stream'
            },
            method='POST'
        )
        
        # 发送请求并读取流式响应
        with urllib.request.urlopen(req) as response:
            print(f"📡 响应状态: {response.status}")
            print(f"📋 响应头: {dict(response.headers)}")
            print("-" * 60)
            
            buffer = ""
            
            # 逐字节读取响应
            while True:
                try:
                    chunk = response.read(1024)
                    if not chunk:
                        break
                    
                    current_time = time.time()
                    chunk_text = chunk.decode('utf-8')
                    buffer += chunk_text
                    
                    # 处理完整的SSE消息
                    lines = buffer.split('\n\n')
                    buffer = lines.pop()  # 保留最后一个可能不完整的行
                    
                    for line in lines:
                        if line.startswith('data: '):
                            try:
                                data = json.loads(line[6:])
                                message_count += 1
                                time_since_start = current_time - start_time
                                time_since_last = current_time - last_message_time
                                
                                print(f"📨 消息 #{message_count} (+{time_since_start:.2f}s, Δ{time_since_last:.3f}s)")
                                print(f"   类型: {data.get('type', 'unknown')}")
                                print(f"   内容: {data.get('message', '')[:100]}...")
                                print(f"   时间戳: {data.get('timestamp', 'N/A')}")
                                print()
                                
                                last_message_time = current_time
                                
                                if data.get('type') == 'complete':
                                    print("✅ 收到完成信号")
                                    break
                                elif data.get('type') == 'error':
                                    print(f"❌ 收到错误信号: {data.get('message')}")
                                    break
                                    
                            except json.JSONDecodeError as e:
                                print(f"⚠️  JSON解析失败: {e}")
                                print(f"   原始数据: {line}")
                
                except Exception as e:
                    print(f"⚠️  读取数据时出错: {e}")
                    break
            
            # 处理剩余的缓冲区数据
            if buffer.strip():
                print(f"📦 剩余缓冲区数据: {buffer}")
                
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        total_time = time.time() - start_time
        print("-" * 60)
        print(f"📊 测试总结:")
        print(f"   总耗时: {total_time:.2f}s")
        print(f"   消息数量: {message_count}")
        if message_count > 0:
            print(f"   平均消息间隔: {total_time/message_count:.3f}s")
        print("🏁 测试完成")

if __name__ == "__main__":
    print("🧪 流式传输修复测试")
    print("=" * 60)
    test_streaming_search()
