#!/usr/bin/env python3
"""
测试流式传输修复的脚本
用于验证search_code接口的流式传输是否正常工作
"""

import asyncio
import aiohttp
import json
import time
from typing import AsyncGenerator

async def test_streaming_search():
    """测试流式搜索接口"""
    
    # 测试数据
    test_request = {
        "query": "test search query",
        "workspaceName": "test-workspace",  # 需要根据实际情况修改
        "searchType": "grep",
        "maxResults": 50
    }
    
    url = "http://localhost:3451/api/v1/search"
    
    print(f"🚀 开始测试流式搜索接口: {url}")
    print(f"📝 测试请求: {json.dumps(test_request, indent=2)}")
    print("-" * 60)
    
    start_time = time.time()
    message_count = 0
    last_message_time = start_time
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(
                url,
                json=test_request,
                headers={"Content-Type": "application/json"}
            ) as response:
                
                print(f"📡 响应状态: {response.status}")
                print(f"📋 响应头: {dict(response.headers)}")
                print("-" * 60)
                
                if response.status != 200:
                    print(f"❌ 请求失败: {response.status}")
                    text = await response.text()
                    print(f"错误信息: {text}")
                    return
                
                # 读取流式响应
                buffer = ""
                async for chunk in response.content.iter_chunked(1024):
                    current_time = time.time()
                    chunk_text = chunk.decode('utf-8')
                    buffer += chunk_text
                    
                    # 处理完整的SSE消息
                    lines = buffer.split('\n\n')
                    buffer = lines.pop()  # 保留最后一个可能不完整的行
                    
                    for line in lines:
                        if line.startswith('data: '):
                            try:
                                data = json.parse(line[6:])
                                message_count += 1
                                time_since_start = current_time - start_time
                                time_since_last = current_time - last_message_time
                                
                                print(f"📨 消息 #{message_count} (+{time_since_start:.2f}s, Δ{time_since_last:.3f}s)")
                                print(f"   类型: {data.get('type', 'unknown')}")
                                print(f"   内容: {data.get('message', '')[:100]}...")
                                print(f"   时间戳: {data.get('timestamp', 'N/A')}")
                                
                                last_message_time = current_time
                                
                                if data.get('type') == 'complete':
                                    print("✅ 收到完成信号")
                                    break
                                elif data.get('type') == 'error':
                                    print(f"❌ 收到错误信号: {data.get('message')}")
                                    break
                                    
                            except json.JSONDecodeError as e:
                                print(f"⚠️  JSON解析失败: {e}")
                                print(f"   原始数据: {line}")
                
                # 处理剩余的缓冲区数据
                if buffer.strip():
                    print(f"📦 剩余缓冲区数据: {buffer}")
                
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        total_time = time.time() - start_time
        print("-" * 60)
        print(f"📊 测试总结:")
        print(f"   总耗时: {total_time:.2f}s")
        print(f"   消息数量: {message_count}")
        if message_count > 0:
            print(f"   平均消息间隔: {total_time/message_count:.3f}s")
        print("🏁 测试完成")

async def test_multiple_requests():
    """测试多个并发请求"""
    print("🔄 开始并发测试...")
    
    tasks = []
    for i in range(3):
        print(f"启动测试 #{i+1}")
        task = asyncio.create_task(test_streaming_search())
        tasks.append(task)
        await asyncio.sleep(1)  # 间隔1秒启动
    
    await asyncio.gather(*tasks)

if __name__ == "__main__":
    print("🧪 流式传输修复测试")
    print("=" * 60)
    
    # 运行单个测试
    asyncio.run(test_streaming_search())
    
    print("\n" + "=" * 60)
    print("🔄 开始并发测试...")
    
    # 运行并发测试
    # asyncio.run(test_multiple_requests())
