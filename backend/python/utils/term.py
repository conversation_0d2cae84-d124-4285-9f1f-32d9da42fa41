# 分词工具
import re
from typing import List, Set, Iterator


def get_terms(text: str) -> List[str]:
    """
    Break a string into terms (words).

    Args:
        text: Input string to tokenize

    Returns:
        List of normalized terms
    """
    return list(_split_terms(text))

@staticmethod
def _split_terms(input_text: str) -> Iterator[str]:
    """
    Generator that yields normalized terms from input text.

    Only matches words that are at least 3 characters long and start with a letter.
    Handles camelCase, snake_case, and numeric suffix splitting.
    """
    def normalize(word: str) -> str:
        return word.lower()

    # Unicode regex pattern equivalent to the JavaScript version
    # Matches words that:
    # - Are at least 3 characters long
    # - Start with a letter, underscore, or dollar sign
    # - Contain only letters, numbers, underscores, or dollar signs
    # - Are not preceded or followed by word characters
    pattern = r'(?<![a-zA-Z0-9_$])[a-zA-Z_$][a-zA-Z0-9_$]{2,}(?![a-zA-Z0-9_$])'

    for match in re.finditer(pattern, input_text):
        word = match.group(0)
        parts: Set[str] = set()
        parts.add(normalize(word))

        sub_parts: List[str] = []

        # Handle camelCase splitting
        camel_parts = re.split(r'(?<=[a-z$])(?=[A-Z])', word)
        if len(camel_parts) > 1:
            sub_parts.extend(camel_parts)

        # Handle snake_case splitting
        snake_parts = word.split('_')
        if len(snake_parts) > 1:
            sub_parts.extend(snake_parts)

        # Handle numeric suffix (e.g., "variable123" -> "variable")
        non_digit_prefix_match = re.match(r'^([^0-9]+)[0-9]+$', word)
        if non_digit_prefix_match:
            sub_parts.append(non_digit_prefix_match.group(1))

        # Process sub parts
        for part in sub_parts:
            # Require at least 3 letters in the sub parts
            if len(part) > 2 and re.search(r'[a-zA-Z_$]{3,}', part):
                parts.add(normalize(part))

        yield from parts
