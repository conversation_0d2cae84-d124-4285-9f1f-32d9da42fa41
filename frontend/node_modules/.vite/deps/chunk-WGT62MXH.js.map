{"version": 3, "sources": ["../../refractor/lang/mongodb.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = mongodb\nmongodb.displayName = 'mongodb'\nmongodb.aliases = []\nfunction mongodb(Prism) {\n  ;(function (Prism) {\n    var operators = [\n      // query and projection\n      '$eq',\n      '$gt',\n      '$gte',\n      '$in',\n      '$lt',\n      '$lte',\n      '$ne',\n      '$nin',\n      '$and',\n      '$not',\n      '$nor',\n      '$or',\n      '$exists',\n      '$type',\n      '$expr',\n      '$jsonSchema',\n      '$mod',\n      '$regex',\n      '$text',\n      '$where',\n      '$geoIntersects',\n      '$geoWithin',\n      '$near',\n      '$nearSphere',\n      '$all',\n      '$elemMatch',\n      '$size',\n      '$bitsAllClear',\n      '$bitsAllSet',\n      '$bitsAnyClear',\n      '$bitsAnySet',\n      '$comment',\n      '$elemMatch',\n      '$meta',\n      '$slice', // update\n      '$currentDate',\n      '$inc',\n      '$min',\n      '$max',\n      '$mul',\n      '$rename',\n      '$set',\n      '$setOnInsert',\n      '$unset',\n      '$addToSet',\n      '$pop',\n      '$pull',\n      '$push',\n      '$pullAll',\n      '$each',\n      '$position',\n      '$slice',\n      '$sort',\n      '$bit', // aggregation pipeline stages\n      '$addFields',\n      '$bucket',\n      '$bucketAuto',\n      '$collStats',\n      '$count',\n      '$currentOp',\n      '$facet',\n      '$geoNear',\n      '$graphLookup',\n      '$group',\n      '$indexStats',\n      '$limit',\n      '$listLocalSessions',\n      '$listSessions',\n      '$lookup',\n      '$match',\n      '$merge',\n      '$out',\n      '$planCacheStats',\n      '$project',\n      '$redact',\n      '$replaceRoot',\n      '$replaceWith',\n      '$sample',\n      '$set',\n      '$skip',\n      '$sort',\n      '$sortByCount',\n      '$unionWith',\n      '$unset',\n      '$unwind',\n      '$setWindowFields', // aggregation pipeline operators\n      '$abs',\n      '$accumulator',\n      '$acos',\n      '$acosh',\n      '$add',\n      '$addToSet',\n      '$allElementsTrue',\n      '$and',\n      '$anyElementTrue',\n      '$arrayElemAt',\n      '$arrayToObject',\n      '$asin',\n      '$asinh',\n      '$atan',\n      '$atan2',\n      '$atanh',\n      '$avg',\n      '$binarySize',\n      '$bsonSize',\n      '$ceil',\n      '$cmp',\n      '$concat',\n      '$concatArrays',\n      '$cond',\n      '$convert',\n      '$cos',\n      '$dateFromParts',\n      '$dateToParts',\n      '$dateFromString',\n      '$dateToString',\n      '$dayOfMonth',\n      '$dayOfWeek',\n      '$dayOfYear',\n      '$degreesToRadians',\n      '$divide',\n      '$eq',\n      '$exp',\n      '$filter',\n      '$first',\n      '$floor',\n      '$function',\n      '$gt',\n      '$gte',\n      '$hour',\n      '$ifNull',\n      '$in',\n      '$indexOfArray',\n      '$indexOfBytes',\n      '$indexOfCP',\n      '$isArray',\n      '$isNumber',\n      '$isoDayOfWeek',\n      '$isoWeek',\n      '$isoWeekYear',\n      '$last',\n      '$last',\n      '$let',\n      '$literal',\n      '$ln',\n      '$log',\n      '$log10',\n      '$lt',\n      '$lte',\n      '$ltrim',\n      '$map',\n      '$max',\n      '$mergeObjects',\n      '$meta',\n      '$min',\n      '$millisecond',\n      '$minute',\n      '$mod',\n      '$month',\n      '$multiply',\n      '$ne',\n      '$not',\n      '$objectToArray',\n      '$or',\n      '$pow',\n      '$push',\n      '$radiansToDegrees',\n      '$range',\n      '$reduce',\n      '$regexFind',\n      '$regexFindAll',\n      '$regexMatch',\n      '$replaceOne',\n      '$replaceAll',\n      '$reverseArray',\n      '$round',\n      '$rtrim',\n      '$second',\n      '$setDifference',\n      '$setEquals',\n      '$setIntersection',\n      '$setIsSubset',\n      '$setUnion',\n      '$size',\n      '$sin',\n      '$slice',\n      '$split',\n      '$sqrt',\n      '$stdDevPop',\n      '$stdDevSamp',\n      '$strcasecmp',\n      '$strLenBytes',\n      '$strLenCP',\n      '$substr',\n      '$substrBytes',\n      '$substrCP',\n      '$subtract',\n      '$sum',\n      '$switch',\n      '$tan',\n      '$toBool',\n      '$toDate',\n      '$toDecimal',\n      '$toDouble',\n      '$toInt',\n      '$toLong',\n      '$toObjectId',\n      '$toString',\n      '$toLower',\n      '$toUpper',\n      '$trim',\n      '$trunc',\n      '$type',\n      '$week',\n      '$year',\n      '$zip',\n      '$count',\n      '$dateAdd',\n      '$dateDiff',\n      '$dateSubtract',\n      '$dateTrunc',\n      '$getField',\n      '$rand',\n      '$sampleRate',\n      '$setField',\n      '$unsetField', // aggregation pipeline query modifiers\n      '$comment',\n      '$explain',\n      '$hint',\n      '$max',\n      '$maxTimeMS',\n      '$min',\n      '$orderby',\n      '$query',\n      '$returnKey',\n      '$showDiskLoc',\n      '$natural'\n    ]\n    var builtinFunctions = [\n      'ObjectId',\n      'Code',\n      'BinData',\n      'DBRef',\n      'Timestamp',\n      'NumberLong',\n      'NumberDecimal',\n      'MaxKey',\n      'MinKey',\n      'RegExp',\n      'ISODate',\n      'UUID'\n    ]\n    operators = operators.map(function (operator) {\n      return operator.replace('$', '\\\\$')\n    })\n    var operatorsSource = '(?:' + operators.join('|') + ')\\\\b'\n    Prism.languages.mongodb = Prism.languages.extend('javascript', {})\n    Prism.languages.insertBefore('mongodb', 'string', {\n      property: {\n        pattern:\n          /(?:([\"'])(?:\\\\(?:\\r\\n|[\\s\\S])|(?!\\1)[^\\\\\\r\\n])*\\1|(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*)(?=\\s*:)/,\n        greedy: true,\n        inside: {\n          keyword: RegExp('^([\\'\"])?' + operatorsSource + '(?:\\\\1)?$')\n        }\n      }\n    })\n    Prism.languages.mongodb.string.inside = {\n      url: {\n        // url pattern\n        pattern:\n          /https?:\\/\\/[-\\w@:%.+~#=]{1,256}\\.[a-z0-9()]{1,6}\\b[-\\w()@:%+.~#?&/=]*/i,\n        greedy: true\n      },\n      entity: {\n        // ipv4\n        pattern:\n          /\\b(?:(?:[01]?\\d\\d?|2[0-4]\\d|25[0-5])\\.){3}(?:[01]?\\d\\d?|2[0-4]\\d|25[0-5])\\b/,\n        greedy: true\n      }\n    }\n    Prism.languages.insertBefore('mongodb', 'constant', {\n      builtin: {\n        pattern: RegExp('\\\\b(?:' + builtinFunctions.join('|') + ')\\\\b'),\n        alias: 'keyword'\n      }\n    })\n  })(Prism)\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,YAAQ,cAAc;AACtB,YAAQ,UAAU,CAAC;AACnB,aAAS,QAAQ,OAAO;AACtB;AAAC,OAAC,SAAUA,QAAO;AACjB,YAAI,YAAY;AAAA;AAAA,UAEd;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AACA,YAAI,mBAAmB;AAAA,UACrB;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AACA,oBAAY,UAAU,IAAI,SAAU,UAAU;AAC5C,iBAAO,SAAS,QAAQ,KAAK,KAAK;AAAA,QACpC,CAAC;AACD,YAAI,kBAAkB,QAAQ,UAAU,KAAK,GAAG,IAAI;AACpD,QAAAA,OAAM,UAAU,UAAUA,OAAM,UAAU,OAAO,cAAc,CAAC,CAAC;AACjE,QAAAA,OAAM,UAAU,aAAa,WAAW,UAAU;AAAA,UAChD,UAAU;AAAA,YACR,SACE;AAAA,YACF,QAAQ;AAAA,YACR,QAAQ;AAAA,cACN,SAAS,OAAO,aAAc,kBAAkB,WAAW;AAAA,YAC7D;AAAA,UACF;AAAA,QACF,CAAC;AACD,QAAAA,OAAM,UAAU,QAAQ,OAAO,SAAS;AAAA,UACtC,KAAK;AAAA;AAAA,YAEH,SACE;AAAA,YACF,QAAQ;AAAA,UACV;AAAA,UACA,QAAQ;AAAA;AAAA,YAEN,SACE;AAAA,YACF,QAAQ;AAAA,UACV;AAAA,QACF;AACA,QAAAA,OAAM,UAAU,aAAa,WAAW,YAAY;AAAA,UAClD,SAAS;AAAA,YACP,SAAS,OAAO,WAAW,iBAAiB,KAAK,GAAG,IAAI,MAAM;AAAA,YAC9D,OAAO;AAAA,UACT;AAAA,QACF,CAAC;AAAA,MACH,GAAG,KAAK;AAAA,IACV;AAAA;AAAA;", "names": ["Prism"]}