{"version": 3, "sources": ["../../refractor/lang/asmatmel.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = asmatmel\nasmatmel.displayName = 'asmatmel'\nasmatmel.aliases = []\nfunction asmatmel(Prism) {\n  Prism.languages.asmatmel = {\n    comment: {\n      pattern: /;.*/,\n      greedy: true\n    },\n    string: {\n      pattern: /([\"'`])(?:\\\\.|(?!\\1)[^\\\\\\r\\n])*\\1/,\n      greedy: true\n    },\n    constant: /\\b(?:PORT[A-Z]|DDR[A-Z]|(?:DD|P)[A-Z](?:\\d|[0-2]\\d|3[01]))\\b/,\n    directive: {\n      pattern: /\\.\\w+(?= )/,\n      alias: 'property'\n    },\n    'r-register': {\n      pattern: /\\br(?:\\d|[12]\\d|3[01])\\b/,\n      alias: 'variable'\n    },\n    'op-code': {\n      pattern:\n        /\\b(?:ADC|ADD|ADIW|AND|ANDI|ASR|BCLR|BLD|BRBC|BRBS|BRCC|BRCS|BREAK|BREQ|BRGE|BRHC|BRHS|BRID|BRIE|BRLO|BRLT|BRMI|BRNE|BRPL|BRSH|BRTC|BRTS|BRVC|BRVS|BSET|BST|CALL|CBI|CBR|CLC|CLH|CLI|CLN|CLR|CLS|CLT|CLV|CLZ|COM|CP|CPC|CPI|CPSE|DEC|DES|EICALL|EIJMP|ELPM|EOR|FMUL|FMULS|FMULSU|ICALL|IJMP|IN|INC|JMP|LAC|LAS|LAT|LD|LD[A-Za-z0-9]|LPM|LSL|LSR|MOV|MOVW|MUL|MULS|MULSU|NEG|NOP|OR|ORI|OUT|POP|PUSH|RCALL|RET|RETI|RJMP|ROL|ROR|SBC|SBCI|SBI|SBIC|SBIS|SBIW|SBR|SBRC|SBRS|SEC|SEH|SEI|SEN|SER|SES|SET|SEV|SEZ|SLEEP|SPM|ST|ST[A-Z0-9]|SUB|SUBI|SWAP|TST|WDR|XCH|adc|add|adiw|and|andi|asr|bclr|bld|brbc|brbs|brcc|brcs|break|breq|brge|brhc|brhs|brid|brie|brlo|brlt|brmi|brne|brpl|brsh|brtc|brts|brvc|brvs|bset|bst|call|cbi|cbr|clc|clh|cli|cln|clr|cls|clt|clv|clz|com|cp|cpc|cpi|cpse|dec|des|eicall|eijmp|elpm|eor|fmul|fmuls|fmulsu|icall|ijmp|in|inc|jmp|lac|las|lat|ld|ld[a-z0-9]|lpm|lsl|lsr|mov|movw|mul|muls|mulsu|neg|nop|or|ori|out|pop|push|rcall|ret|reti|rjmp|rol|ror|sbc|sbci|sbi|sbic|sbis|sbiw|sbr|sbrc|sbrs|sec|seh|sei|sen|ser|ses|set|sev|sez|sleep|spm|st|st[a-zA-Z0-9]|sub|subi|swap|tst|wdr|xch)\\b/,\n      alias: 'keyword'\n    },\n    'hex-number': {\n      pattern: /#?\\$[\\da-f]{2,4}\\b/i,\n      alias: 'number'\n    },\n    'binary-number': {\n      pattern: /#?%[01]+\\b/,\n      alias: 'number'\n    },\n    'decimal-number': {\n      pattern: /#?\\b\\d+\\b/,\n      alias: 'number'\n    },\n    register: {\n      pattern: /\\b[acznvshtixy]\\b/i,\n      alias: 'variable'\n    },\n    operator: />>=?|<<=?|&&?|\\|\\|?|[-+*/%&|^!=<>?]=?/,\n    punctuation: /[(),:]/\n  }\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,aAAS,cAAc;AACvB,aAAS,UAAU,CAAC;AACpB,aAAS,SAAS,OAAO;AACvB,YAAM,UAAU,WAAW;AAAA,QACzB,SAAS;AAAA,UACP,SAAS;AAAA,UACT,QAAQ;AAAA,QACV;AAAA,QACA,QAAQ;AAAA,UACN,SAAS;AAAA,UACT,QAAQ;AAAA,QACV;AAAA,QACA,UAAU;AAAA,QACV,WAAW;AAAA,UACT,SAAS;AAAA,UACT,OAAO;AAAA,QACT;AAAA,QACA,cAAc;AAAA,UACZ,SAAS;AAAA,UACT,OAAO;AAAA,QACT;AAAA,QACA,WAAW;AAAA,UACT,SACE;AAAA,UACF,OAAO;AAAA,QACT;AAAA,QACA,cAAc;AAAA,UACZ,SAAS;AAAA,UACT,OAAO;AAAA,QACT;AAAA,QACA,iBAAiB;AAAA,UACf,SAAS;AAAA,UACT,OAAO;AAAA,QACT;AAAA,QACA,kBAAkB;AAAA,UAChB,SAAS;AAAA,UACT,OAAO;AAAA,QACT;AAAA,QACA,UAAU;AAAA,UACR,SAAS;AAAA,UACT,OAAO;AAAA,QACT;AAAA,QACA,UAAU;AAAA,QACV,aAAa;AAAA,MACf;AAAA,IACF;AAAA;AAAA;", "names": []}