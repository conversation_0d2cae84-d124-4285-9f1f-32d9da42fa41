import React from 'react';
import { cn } from '@/utils/cn';

interface MainLayoutProps {
  leftPanel: React.ReactNode;
  centerPanel: React.ReactNode;
  rightPanel: React.ReactNode;
  leftPanelCollapsed?: boolean;
  rightPanelCollapsed?: boolean;
  onToggleLeftPanel?: () => void;
  onToggleRightPanel?: () => void;
}

export function MainLayout({
  leftPanel,
  centerPanel,
  rightPanel,
  leftPanelCollapsed = false,
  rightPanelCollapsed = false,
}: MainLayoutProps) {
  return (
    <div className="h-screen w-screen flex bg-gray-50 overflow-hidden">
      {/* 左侧面板 - 工作区管理 */}
      <div
        className={cn(
          'bg-white border-r border-gray-200 transition-all duration-300 ease-in-out flex-shrink-0',
          leftPanelCollapsed ? 'w-0 overflow-hidden' : 'w-80'
        )}
      >
        {!leftPanelCollapsed && (
          <div className="h-full flex flex-col">
            {leftPanel}
          </div>
        )}
      </div>

      {/* 中央面板 - 文件查看器 */}
      <div className="flex-1 flex flex-col bg-white min-w-0 overflow-hidden">
        {centerPanel}
      </div>

      {/* 右侧面板 - 搜索对话 */}
      <div
        className={cn(
          'bg-white border-l border-gray-200 transition-all duration-300 ease-in-out flex-shrink-0',
          rightPanelCollapsed ? 'w-0 overflow-hidden' : 'w-[400px]'
        )}
      >
        {!rightPanelCollapsed && (
          <div className="h-full flex flex-col">
            {rightPanel}
          </div>
        )}
      </div>
    </div>
  );
}
