import { Select } from '@/components/ui';

interface SettingsPanelProps {
  searchType: 'grep' | 'bm25';
  onSearchTypeChange: (type: 'grep' | 'bm25') => void;
}

export function SettingsPanel({ searchType, onSearchTypeChange }: SettingsPanelProps) {
  const searchTypeOptions = [
    { label: 'Grep 搜索', value: 'grep' as const },
    { label: 'BM25搜索', value: 'bm25' as const },
  ];


  return (
    <div className="space-y-4">
      <div className="space-y-3">
        <Select
          options={searchTypeOptions}
          value={searchType}
          onChange={onSearchTypeChange}
        />
      </div>
    </div>
  );
}
