import type { SearchRequest } from '@/types';
import { buildApiUrl, API_ENDPOINTS } from '@/config/apiConfig';

export class SearchService {
  private static instance: SearchService;

  static getInstance(): SearchService {
    if (!SearchService.instance) {
      SearchService.instance = new SearchService();
    }
    return SearchService.instance;
  }

  // 执行流式搜索（所有搜索都使用流式）
  async executeSearch(
    request: SearchRequest,
    onProgress: (message: string) => void,
    onComplete: (result: string) => void,
    onError: (error: string) => void
  ): Promise<void> {
    try {
      const response = await fetch(buildApiUrl(API_ENDPOINTS.SEARCH), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query: request.query,
          workspaceName: request.workspaceId,
          searchType: request.searchType,
          maxResults: request.options?.maxResults || 50,
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('无法读取响应流');
      }

      const decoder = new TextDecoder();
      let buffer = '';

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n\n');
        
        // 保留最后一个可能不完整的行
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6));
              
              switch (data.type) {
                case 'start':
                  onProgress(data.message);
                  break;
                case 'process':
                  onProgress(data.message);
                  break;
                case 'complete':
                  onProgress(data.message);
                  onComplete('')
                  return;
                case 'error':
                  onError(data.message);
                  return;
              }
            } catch (e) {
              console.warn('解析SSE数据失败:', line, e);
            }
          }
        }
      }
    } catch (error) {
      onError(`搜索失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  // 获取搜索状态（用于实时更新）
  async getSearchStatus(_searchId: string): Promise<{ status: string; progress: number }> {
    // 在实际应用中，这里会查询搜索进度
    return {
      status: 'completed',
      progress: 100,
    };
  }
}

export const searchService = SearchService.getInstance();
