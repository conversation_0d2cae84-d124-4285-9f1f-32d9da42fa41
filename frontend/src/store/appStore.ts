import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import type { 
  WorkspaceOption, 
  FileNode
} from '@/types';

interface AppState {
  // 工作区状态
  workspaces: WorkspaceOption[];
  currentWorkspace: string | null;
  fileTree: FileNode[];
  
  // 文件状态
  selectedFile: string | null;
  fileContent: string | null;
  fileLanguage: string | null;
  
  // 搜索状态
  searchQuery: string;
  searchType: 'grep' | 'bm25';
  searchResult: string;
  isSearching: boolean;
  searchTime: number;
  
  // UI 状态
  leftPanelCollapsed: boolean;
  rightPanelCollapsed: boolean;
  
  // Actions
  setWorkspaces: (workspaces: WorkspaceOption[]) => void;
  setCurrentWorkspace: (workspaceId: string | null) => void;
  setFileTree: (fileTree: FileNode[]) => void;
  setSelectedFile: (filePath: string | null) => void;
  setFileContent: (content: string | null, language?: string) => void;
  setSearchQuery: (query: string) => void;
  setSearchType: (type: 'grep' | 'bm25') => void;
  setSearchResult: (results: string) => void;
  setIsSearching: (searching: boolean) => void;
  setSearchTime: (time: number) => void;
  toggleLeftPanel: () => void;
  toggleRightPanel: () => void;
  clearSearch: () => void;
}

export const useAppStore = create<AppState>()(
  devtools(
    (set, _get) => ({ // 重命名为 _get 表示未使用
      // 初始状态
      workspaces: [],
      currentWorkspace: null,
      fileTree: [],
      selectedFile: null,
      fileContent: null,
      fileLanguage: null,
      searchQuery: '',
      searchType: 'grep',
      searchResult: '',
      isSearching: false,
      searchTime: 0,
      leftPanelCollapsed: false,
      rightPanelCollapsed: false,

      // Actions
      setWorkspaces: (workspaces) => set({ workspaces }),
      
      setCurrentWorkspace: (workspaceId) => set({ 
        currentWorkspace: workspaceId,
        selectedFile: null,
        fileContent: null,
        fileTree: [],
      }),
      
      setFileTree: (fileTree) => set({ fileTree }),
      
      setSelectedFile: (filePath) => set({ 
        selectedFile: filePath,
        fileContent: null,
      }),
      
      setFileContent: (content, language = 'text') => set({ 
        fileContent: content,
        fileLanguage: language,
      }),
      
      setSearchQuery: (query) => set({ searchQuery: query }),
      
      setSearchType: (type) => set({ searchType: type }),
      
      setSearchResult: (result) => set({ searchResult: result }),
      
      setIsSearching: (searching) => set({ isSearching: searching }),
      
      setSearchTime: (time) => set({ searchTime: time }),
      
      toggleLeftPanel: () => set((state) => ({ 
        leftPanelCollapsed: !state.leftPanelCollapsed 
      })),
      
      toggleRightPanel: () => set((state) => ({ 
        rightPanelCollapsed: !state.rightPanelCollapsed 
      })),
      
      clearSearch: () => set({
        searchQuery: '',
        searchResult: '',
        searchTime: 0,
      }),
    }),
    {
      name: 'webview-app-store',
    }
  )
);
