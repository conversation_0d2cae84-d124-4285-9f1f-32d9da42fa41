export interface FileNode {
  id: string;
  name: string;
  type: 'file' | 'directory';
  path: string;
  relativePath: string;
  size?: number;
  lastModified?: Date;
  children?: FileNode[];
  isExpanded?: boolean;
}

export interface WorkspaceOption {
  id: string;
  name: string;
  path: string;
  description?: string;
  lastAccessed?: Date;
  fileCount?: number;
  size?: number;
}

export interface SearchResult {
  id: string;
  filePath: string;
  fileName: string;
  snippet: string;
  lineNumber: number;
  score: number;
  context: {
    before: string[];
    after: string[];
  };
}

export interface SearchProcessStep {
  id: string;
  title: string;
  status: 'pending' | 'running' | 'completed' | 'error';
  startTime?: Date;
  endTime?: Date;
  details?: string;
  subSteps?: SearchProcessStep[];
}

export interface SearchRequest {
  query: string;
  workspaceId: string;
  searchType: 'grep' | 'bm25';
  options?: {
    maxResults?: number;
    fileTypes?: string[];
    excludePaths?: string[];
  };
}

export interface SearchResponse {
  searchId: string;
  query: string;
  result: string;
  searchTime: number;
}

export type ButtonVariant = 'primary' | 'secondary' | 'danger' | 'ghost';
export type ButtonSize = 'sm' | 'md' | 'lg';
export type InputType = 'text' | 'search' | 'textarea';
