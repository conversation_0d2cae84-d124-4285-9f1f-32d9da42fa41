<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>流式传输测试</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-controls {
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
        }
        .log-container {
            height: 500px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            background: #000;
            color: #0f0;
            font-size: 12px;
            line-height: 1.4;
        }
        .log-entry {
            margin-bottom: 5px;
            word-wrap: break-word;
        }
        .log-timestamp {
            color: #888;
        }
        .log-type-start { color: #00ff00; }
        .log-type-process { color: #ffff00; }
        .log-type-complete { color: #00ffff; }
        .log-type-error { color: #ff0000; }
        .stats {
            margin-top: 10px;
            padding: 10px;
            background: #e9ecef;
            border-radius: 5px;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
        }
        .stat-item {
            text-align: center;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            font-size: 12px;
            color: #666;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn-primary {
            background: #007bff;
            color: white;
        }
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
        input, select {
            padding: 8px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 流式传输测试工具</h1>
        
        <div class="test-controls">
            <h3>测试配置</h3>
            <div>
                <label>查询内容:</label>
                <input type="text" id="queryInput" value="test search" placeholder="输入搜索查询">
            </div>
            <div>
                <label>工作区:</label>
                <input type="text" id="workspaceInput" value="test-workspace" placeholder="工作区名称">
            </div>
            <div>
                <label>搜索类型:</label>
                <select id="searchTypeSelect">
                    <option value="grep">Grep</option>
                    <option value="embedding">Embedding</option>
                </select>
            </div>
            <div>
                <button class="btn-primary" onclick="startTest()" id="startBtn">🚀 开始测试</button>
                <button class="btn-secondary" onclick="clearLog()" id="clearBtn">🧹 清空日志</button>
                <button class="btn-danger" onclick="stopTest()" id="stopBtn" disabled>⏹️ 停止测试</button>
            </div>
        </div>

        <div class="stats">
            <div class="stat-item">
                <div class="stat-value" id="messageCount">0</div>
                <div class="stat-label">消息数量</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="elapsedTime">0.0s</div>
                <div class="stat-label">已用时间</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="avgInterval">0.0s</div>
                <div class="stat-label">平均间隔</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="lastInterval">0.0s</div>
                <div class="stat-label">最后间隔</div>
            </div>
        </div>

        <h3>📋 实时日志</h3>
        <div class="log-container" id="logContainer"></div>
    </div>

    <script>
        let testStartTime = null;
        let lastMessageTime = null;
        let messageCount = 0;
        let currentReader = null;
        let updateTimer = null;

        function log(message, type = 'info') {
            const container = document.getElementById('logContainer');
            const timestamp = new Date().toLocaleTimeString();
            const entry = document.createElement('div');
            entry.className = `log-entry log-type-${type}`;
            entry.innerHTML = `<span class="log-timestamp">[${timestamp}]</span> ${message}`;
            container.appendChild(entry);
            container.scrollTop = container.scrollHeight;
        }

        function updateStats() {
            if (!testStartTime) return;
            
            const now = Date.now();
            const elapsed = (now - testStartTime) / 1000;
            const avgInterval = messageCount > 0 ? elapsed / messageCount : 0;
            const lastInterval = lastMessageTime ? (now - lastMessageTime) / 1000 : 0;

            document.getElementById('messageCount').textContent = messageCount;
            document.getElementById('elapsedTime').textContent = elapsed.toFixed(1) + 's';
            document.getElementById('avgInterval').textContent = avgInterval.toFixed(3) + 's';
            document.getElementById('lastInterval').textContent = lastInterval.toFixed(3) + 's';
        }

        async function startTest() {
            const query = document.getElementById('queryInput').value;
            const workspace = document.getElementById('workspaceInput').value;
            const searchType = document.getElementById('searchTypeSelect').value;

            if (!query || !workspace) {
                alert('请填写查询内容和工作区名称');
                return;
            }

            // 重置状态
            testStartTime = Date.now();
            lastMessageTime = null;
            messageCount = 0;
            
            // 更新UI状态
            document.getElementById('startBtn').disabled = true;
            document.getElementById('stopBtn').disabled = false;
            
            log(`🚀 开始测试流式搜索`, 'start');
            log(`📝 查询: ${query}`, 'info');
            log(`🏢 工作区: ${workspace}`, 'info');
            log(`🔍 搜索类型: ${searchType}`, 'info');

            // 启动统计更新定时器
            updateTimer = setInterval(updateStats, 100);

            try {
                const response = await fetch('http://localhost:8000/api/v1/search', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        query: query,
                        workspaceName: workspace,
                        searchType: searchType,
                        maxResults: 50,
                    }),
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                currentReader = response.body?.getReader();
                if (!currentReader) {
                    throw new Error('无法读取响应流');
                }

                const decoder = new TextDecoder();
                let buffer = '';

                while (true) {
                    const { done, value } = await currentReader.read();
                    if (done) {
                        log('📡 流结束', 'complete');
                        break;
                    }

                    buffer += decoder.decode(value, { stream: true });
                    const lines = buffer.split('\n\n');
                    buffer = lines.pop() || '';

                    for (const line of lines) {
                        if (line.startsWith('data: ')) {
                            try {
                                const data = JSON.parse(line.slice(6));
                                messageCount++;
                                lastMessageTime = Date.now();
                                
                                const timeSinceStart = (lastMessageTime - testStartTime) / 1000;
                                log(`📨 [${timeSinceStart.toFixed(2)}s] ${data.type}: ${data.message}`, data.type);
                                
                                if (data.type === 'complete') {
                                    log('✅ 搜索完成', 'complete');
                                    stopTest();
                                    return;
                                } else if (data.type === 'error') {
                                    log(`❌ 搜索错误: ${data.message}`, 'error');
                                    stopTest();
                                    return;
                                }
                            } catch (e) {
                                log(`⚠️ JSON解析失败: ${e.message}`, 'error');
                            }
                        }
                    }
                }
            } catch (error) {
                log(`❌ 测试失败: ${error.message}`, 'error');
            } finally {
                stopTest();
            }
        }

        function stopTest() {
            if (currentReader) {
                currentReader.cancel();
                currentReader = null;
            }
            
            if (updateTimer) {
                clearInterval(updateTimer);
                updateTimer = null;
            }
            
            document.getElementById('startBtn').disabled = false;
            document.getElementById('stopBtn').disabled = true;
            
            log('⏹️ 测试停止', 'info');
        }

        function clearLog() {
            document.getElementById('logContainer').innerHTML = '';
            messageCount = 0;
            testStartTime = null;
            lastMessageTime = null;
            updateStats();
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('🔧 流式传输测试工具已就绪', 'start');
            updateStats();
        });
    </script>
</body>
</html>
